# 📦 DreamVault Shared Types

A comprehensive TypeScript type definitions package shared across all DreamVault applications (backend API, web app, and mobile app). Ensures type safety and consistency throughout the entire ecosystem.

## 🎯 Purpose

This package provides centralized TypeScript type definitions that are used across:
- **Backend API** - Express.js server type validation and responses
- **Web Application** - Next.js client-side type safety
- **Mobile App** - React Native app type consistency
- **Development Tools** - Enhanced developer experience with IntelliSense

## 📋 Type Categories

### 🔐 Authentication Types (`user.ts`)
- **User Profile** - Complete user information and preferences
- **Authentication State** - Login status and session management
- **Account Management** - Profile updates and account operations

### 🎯 Bucket List Types (`bucketList.ts`)
- **Bucket List Items** - Core goal and dream structures
- **Categories** - Predefined goal categories (Travel, Adventure, Career, etc.)
- **Priority Levels** - Goal prioritization system
- **Status Tracking** - Progress states and completion tracking
- **Media Attachments** - Photo and document associations

### 🌐 API Types (`api.ts`)
- **Request/Response** - Standardized API communication structures
- **Error Handling** - Consistent error response formats
- **Pagination** - List and search result pagination
- **Authentication** - JWT and Auth0 integration types

### 👥 Social Features (`social.ts`)
- **Shared Lists** - Collaborative bucket list functionality
- **Discovery** - Public goal exploration and inspiration
- **Achievements** - Gamification and milestone tracking
- **Notifications** - Social interaction alerts

## 🏗️ Architecture

### Type Organization
```
src/
├── index.ts           # Main export file - re-exports all types
├── api.ts            # API request/response types
├── bucketList.ts     # Core bucket list functionality types
├── social.ts         # Social and sharing feature types
└── user.ts           # User authentication and profile types
```

### Build Output
```
dist/
├── index.js          # Compiled JavaScript (for runtime if needed)
├── index.d.ts        # Main type definitions export
├── api.d.ts          # API type definitions
├── bucketList.d.ts   # Bucket list type definitions
├── social.d.ts       # Social feature type definitions
└── user.d.ts         # User type definitions
```

## 🛠️ Installation & Usage

### As a Dependency
The package is used as a local file dependency in other DreamVault applications:

```json
{
  "dependencies": {
    "@dreamvault/types": "file:../packages/types"
  }
}
```

### Development Setup
```bash
cd packages/types

# Install dependencies
npm install

# Build type definitions
npm run build

# Watch for changes during development
npm run dev

# Clean build artifacts
npm run clean
```

## 📜 Available Scripts

- `npm run build` - Compile TypeScript to JavaScript and generate `.d.ts` files
- `npm run dev` - Watch mode for development with automatic rebuilds
- `npm run clean` - Remove compiled files and build artifacts

## 🔍 Type Examples

### User Types
```typescript
// User profile with Auth0 integration
export interface User {
  id: string;
  auth0Id: string;
  email: string;
  name: string;
  avatar?: string;
  preferences: UserPreferences;
  createdAt: Date;
  updatedAt: Date;
}

// User preferences and settings
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  language: string;
}
```

### Bucket List Types
```typescript
// Core bucket list item structure
export interface BucketListItem {
  id: string;
  userId: string;
  title: string;
  description?: string;
  category: BucketListCategory;
  priority: Priority;
  status: BucketListStatus;
  targetDate?: Date;
  completedDate?: Date;
  media: MediaAttachment[];
  progress: ProgressEntry[];
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Predefined categories for organization
export enum BucketListCategory {
  TRAVEL = 'TRAVEL',
  ADVENTURE = 'ADVENTURE',
  CAREER = 'CAREER',
  PERSONAL = 'PERSONAL',
  CREATIVE = 'CREATIVE',
  FITNESS = 'FITNESS',
  EDUCATION = 'EDUCATION',
  SOCIAL = 'SOCIAL'
}

// Priority levels for goal management
export enum Priority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// Status tracking for progress
export enum BucketListStatus {
  NOT_STARTED = 'NOT_STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  ON_HOLD = 'ON_HOLD',
  CANCELLED = 'CANCELLED'
}
```

### API Types
```typescript
// Standardized API response wrapper
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    hasMore?: boolean;
  };
}

// Consistent error structure
export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
}

// Authentication context
export interface AuthUser {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  isAuthenticated: boolean;
  permissions: string[];
}
```

### Social Types
```typescript
// Shared bucket list functionality
export interface SharedList {
  id: string;
  bucketListId: string;
  ownerId: string;
  members: SharedListMember[];
  permissions: SharePermissions;
  createdAt: Date;
}

// Achievement and gamification system
export interface Achievement {
  id: string;
  userId: string;
  type: AchievementType;
  title: string;
  description: string;
  earnedAt: Date;
  metadata?: Record<string, any>;
}
```

## 🔧 Development Workflow

### Adding New Types
1. **Identify the category** - Determine which file the new types belong in
2. **Define the interface/type** - Create comprehensive type definitions
3. **Export from index.ts** - Ensure types are available to consumers
4. **Build and test** - Run build process and test in consuming applications

### Updating Existing Types
1. **Review breaking changes** - Consider impact on existing code
2. **Update documentation** - Ensure examples reflect changes
3. **Increment version** - Update package.json version if needed
4. **Test across applications** - Verify changes work in all consuming apps

### Type Safety Best Practices
- Use strict TypeScript configuration
- Prefer interfaces over types for object shapes
- Use enums for predefined constant values
- Include JSDoc comments for complex types
- Maintain backward compatibility when possible

## 🧪 Testing

### Type Checking
```bash
# Verify types compile correctly
npm run build

# Check for type errors
npx tsc --noEmit
```

### Integration Testing
```bash
# Test types in backend
cd ../../backend && npm run type-check

# Test types in web app  
cd ../../web && npm run type-check

# Test types in mobile app
cd ../../mobile && npm run type-check
```

## 📖 Usage in Applications

### Backend API
```typescript
import { BucketListItem, ApiResponse, User } from '@dreamvault/types';

// API route handler with proper typing
app.get('/api/items', async (req, res): Promise<ApiResponse<BucketListItem[]>> => {
  // Implementation with type safety
});
```

### Web Application
```typescript
import { BucketListItem, User, BucketListCategory } from '@dreamvault/types';

// React component with typed props
interface BucketListProps {
  items: BucketListItem[];
  user: User;
  onCategoryFilter: (category: BucketListCategory) => void;
}
```

### Mobile Application
```typescript
import { BucketListItem, ApiResponse } from '@dreamvault/types';

// React Native screen with typed state
const [items, setItems] = useState<BucketListItem[]>([]);
const [loading, setLoading] = useState<boolean>(false);
```

## 🔄 Version Management

### Semantic Versioning
- **Patch (1.0.x)** - Bug fixes, documentation updates
- **Minor (1.x.0)** - New types, non-breaking additions
- **Major (x.0.0)** - Breaking changes, structural modifications

### Dependency Updates
When types are updated, dependent applications should:
1. Update the package reference
2. Run type checking to identify issues
3. Update code to match new type definitions
4. Test thoroughly before deployment

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](../../LICENSE) file for details.

## 🔗 Related Projects

- [Backend API](../../backend/README.md) - Express.js API server that uses these types
- [Web App](../../web/README.md) - Next.js web application with type integration
- [Mobile App](../../mobile/README.md) - React Native app with shared type safety

---

**Made with ❤️ using TypeScript for type-safe development across the entire stack**

*Ensuring consistency and reliability through shared type definitions.*