#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying @dreamvault/types package linking...\n');

// Check if dist directory exists and has files
const distPath = path.join(__dirname, 'dist');
if (!fs.existsSync(distPath)) {
  console.error('❌ Dist directory not found. Run npm run build first.');
  process.exit(1);
}

const distFiles = fs.readdirSync(distPath);
console.log('✅ Dist directory contains:', distFiles.join(', '));

// Check if package.json exists
const packageJsonPath = path.join(__dirname, 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ package.json not found');
  process.exit(1);
}

const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
console.log('✅ Package name:', packageJson.name);
console.log('✅ Package version:', packageJson.version);

// Try to require the package
try {
  const types = require('./dist/index.js');
  console.log('✅ Package can be imported');
  console.log('✅ Available exports:', Object.keys(types));
  
  // Check specific exports
  if (types.ItemStatus) {
    console.log('✅ ItemStatus enum available:', Object.keys(types.ItemStatus));
  }
  if (types.ItemCategory) {
    console.log('✅ ItemCategory enum available:', Object.keys(types.ItemCategory));
  }
  if (types.ItemPriority) {
    console.log('✅ ItemPriority enum available:', Object.keys(types.ItemPriority));
  }
  
} catch (error) {
  console.error('❌ Failed to import package:', error.message);
  process.exit(1);
}

console.log('\n🎉 Package linking verification completed successfully!');