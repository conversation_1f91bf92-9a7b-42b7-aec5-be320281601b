{"name": "@dreamvault/types", "version": "1.0.0", "description": "Shared TypeScript types for DreamVault applications", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist"}, "keywords": ["dreamvault", "types", "typescript"], "author": "DreamVault Team", "license": "MIT", "devDependencies": {"typescript": "^5.0.0"}, "files": ["dist/**/*"]}