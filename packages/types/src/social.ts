import { MediaFile } from './bucketList';

export interface SharedList {
  id: string;
  title: string;
  description?: string;
  ownerId: string;
  collaborators: Collaborator[];
  items: string[]; // Array of bucket list item IDs
  permissions: SharedListPermissions;
  createdAt: Date;
  updatedAt: Date;
}

export interface Collaborator {
  userId: string;
  displayName: string;
  avatar?: string;
  role: CollaboratorRole;
  joinedAt: Date;
}

export type CollaboratorRole = 'owner' | 'editor' | 'viewer';

export interface SharedListPermissions {
  canAddItems: boolean;
  canEditItems: boolean;
  canDeleteItems: boolean;
  canInviteOthers: boolean;
  canManagePermissions: boolean;
}

export interface Invitation {
  id: string;
  listId: string;
  inviterId: string;
  inviteeEmail: string;
  role: CollaboratorRole;
  status: InvitationStatus;
  createdAt: Date;
  expiresAt: Date;
}

export type InvitationStatus = 'pending' | 'accepted' | 'declined' | 'expired';

export interface DiscoveryItem {
  id: string;
  title: string;
  description?: string;
  category: string;
  popularityScore: number;
  completionCount: number;
  averageRating?: number;
  tags: string[];
  media?: MediaFile[];
}

export interface SocialActivity {
  id: string;
  userId: string;
  type: ActivityType;
  data: any;
  createdAt: Date;
}

export type ActivityType = 
  | 'item_completed' 
  | 'achievement_unlocked' 
  | 'milestone_reached' 
  | 'list_shared' 
  | 'collaboration_joined';