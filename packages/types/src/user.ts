export interface User {
  id: string;
  email: string;
  profile: UserProfile;
  preferences: UserPreferences;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserProfile {
  displayName: string;
  avatar?: string;
  bio?: string;
  location?: string;
  interests: string[];
  isPublic: boolean;
}

export interface UserPreferences {
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  theme: 'light' | 'dark' | 'system';
}

export interface NotificationSettings {
  pushNotifications: boolean;
  emailNotifications: boolean;
  reminderNotifications: boolean;
  achievementNotifications: boolean;
  socialNotifications: boolean;
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'private' | 'friends';
  showAchievements: boolean;
  showProgress: boolean;
  allowDiscovery: boolean;
}