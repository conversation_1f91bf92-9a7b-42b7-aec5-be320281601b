import { BucketListItem } from './bucketList';

export interface APIResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
}

export interface APIError {
  error: {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
    requestId: string;
  };
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}



export interface SortOptions {
  field: 'createdAt' | 'updatedAt' | 'targetDate' | 'priority' | 'title';
  direction: 'asc' | 'desc';
}

// API Response Types
export interface BucketListItemResponse {
  items: BucketListItem[];
  total: number;
  page: number;
  limit: number;
}

export interface BucketListStatsResponse {
  totalItems: number;
  completedItems: number;
  inProgressItems: number;
  completionRate: number;
  categoriesBreakdown: CategoryStats[];
  recentActivity: ActivitySummary[];
}

export interface CategoryStats {
  category: string;
  count: number;
  completedCount: number;
}

export interface ActivitySummary {
  date: string;
  itemsCompleted: number;
  milestonesReached: number;
}