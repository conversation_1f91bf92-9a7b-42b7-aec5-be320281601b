export interface BucketListItem {
  id: string;
  userId: string;
  title: string;
  description?: string;
  category: ItemCategory;
  priority: ItemPriority;
  status: ItemStatus;
  targetDate?: Date;
  estimatedCost?: number;
  location?: Location;
  tags: string[];
  media: MediaFile[];
  progress: ProgressTracking;
  createdAt: Date;
  updatedAt: Date;
}

export enum ItemCategory {
  TRAVEL = 'Travel',
  CAREER = 'Career',
  PERSONAL_GROWTH = 'Personal Growth',
  RELATIONSHIPS = 'Relationships',
  ADVENTURES = 'Adventures',
  LEARNING = 'Learning',
}

export enum ItemPriority {
  MUST_DO = 'Must-do',
  WANT_TO_DO = 'Want-to-do',
  SOMEDAY = 'Someday',
}

export enum ItemStatus {
  NOT_STARTED = 'Not Started',
  IN_PROGRESS = 'In Progress',
  COMPLETED = 'Completed',
  ON_HOLD = 'On Hold',
}

export interface Location {
  name: string;
  latitude?: number;
  longitude?: number;
  address?: string;
  country?: string;
  city?: string;
}

export interface MediaFile {
  id: string;
  url: string;
  type: MediaType;
  size: number;
  caption?: string;
  uploadedAt: Date;
}

export type MediaType = 'image' | 'video' | 'audio';

export interface ProgressTracking {
  percentage: number;
  milestones: Milestone[];
  photos: MediaFile[];
  notes: ProgressNote[];
  analytics: ProgressAnalytics;
}

export interface Milestone {
  id: string;
  title: string;
  description?: string;
  createdAt: Date;
  completedAt?: Date;
  media?: MediaFile[];
}

export interface ProgressNote {
  id: string;
  content: string;
  createdAt: Date;
}

export interface ProgressAnalytics {
  streakDays: number;
  completionTrend: 'improving' | 'stable' | 'declining';
}

export interface TimelineEntry {
  id: string;
  date: Date;
  description: string;
  media?: MediaFile[];
  type: 'progress' | 'milestone' | 'completion';
}

export interface Achievement {
  id: string;
  userId: string;
  type: AchievementType;
  title: string;
  description: string;
  iconUrl?: string;
  earnedAt: Date;
  metadata?: any;
  rarity: AchievementRarity;
}

export enum AchievementType {
  STREAK = 'streak',
  CATEGORY_MASTER = 'category_master',
  FIRST_COMPLETION = 'first_completion',
  MILESTONE = 'milestone',
  SOCIAL = 'social',
  EXPLORATION = 'exploration',
}

export enum AchievementRarity {
  COMMON = 'common',
  UNCOMMON = 'uncommon',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary',
}

export type AchievementCategory = 
  | 'completion' 
  | 'streak' 
  | 'milestone' 
  | 'social' 
  | 'exploration';

// Search and filtering interfaces
export interface SearchFilters {
  category?: ItemCategory[];
  priority?: ItemPriority[];
  status?: ItemStatus[];
  tags?: string[];
  dateRange?: {
    created?: { after?: string; before?: string };
    targetDate?: { after?: string; before?: string };
    completed?: { after?: string; before?: string };
  };
  costRange?: { min?: number; max?: number };
  completionRange?: { min?: number; max?: number };
  location?: {
    name?: string;
    country?: string;
    city?: string;
    radius?: number;
    latitude?: number;
    longitude?: number;
  };
}

export interface SearchOptions {
  query?: string;
  filters?: SearchFilters;
  sortBy?: 'createdAt' | 'updatedAt' | 'title' | 'targetDate' | 'priority' | 'completionPercentage' | 'estimatedCost' | 'completedAt' | 'relevance';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
  includeCompleted?: boolean;
  fuzzySearch?: boolean;
}

export interface SearchResult {
  items: BucketListItem[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  facets?: {
    categories: Record<string, number>;
    priorities: Record<string, number>;
    statuses: Record<string, number>;
  };
  searchMeta: {
    query?: string;
    appliedFilters: SearchFilters;
    sortBy: string;
    sortOrder: string;
    fuzzySearch?: boolean;
  };
}

export interface SearchSuggestion {
  titles: string[];
  tags: string[];
  locations: Location[];
}

// Analytics interfaces
export interface DashboardAnalytics {
  summary: {
    totalItems: number;
    completedItems: number;
    inProgressItems: number;
    completionRate: number;
    avgCompletionDays: number;
  };
  breakdowns: {
    category: Array<{ category: ItemCategory; count: number }>;
    priority: Array<{ priority: ItemPriority; count: number }>;
    status: Array<{ status: ItemStatus; count: number }>;
  };
  trends: {
    completion: Array<{ month: Date; completedCount: number }>;
  };
  recentActivity: Array<{
    id: string;
    title: string;
    status: ItemStatus;
    updatedAt: Date;
    completedAt?: Date;
  }>;
  popularTags: Array<{ tag: string; count: number }>;
  timeframe: string;
  generatedAt: string;
}

export interface SearchAnalytics {
  searchability: {
    totalItems: number;
    itemsWithTags: number;
    itemsWithLocation: number;
    itemsWithMedia: number;
    avgTagsPerItem: number;
    taggingRate: number;
    locationRate: number;
    mediaRate: number;
  };
  popularCategories: Array<{ category: ItemCategory; count: number }>;
  generatedAt: string;
}

// Progress tracking and achievement interfaces
export interface DetailedProgressAnalytics {
  summary: {
    totalItems: number;
    completedItems: number;
    inProgressItems: number;
    completionRate: number;
    averageCompletion: number;
    totalMilestones: number;
    completedMilestones: number;
    milestoneCompletionRate: number;
    recentCompletions: number;
    currentStreak: number;
    longestStreak: number;
  };
  trends: {
    progressOverTime: Array<{
      month: Date;
      averageCompletion: number;
      completedCount: number;
    }>;
    completionVelocity: Array<{
      month: Date;
      velocity: number;
    }>;
  };
  breakdowns: {
    byCategory: Array<{
      category: ItemCategory;
      averageCompletion: number;
      itemCount: number;
    }>;
    byPriority: Array<{
      priority: ItemPriority;
      averageCompletion: number;
      itemCount: number;
    }>;
  };
  recentActivity: Array<{
    id: string;
    description: string;
    entryType: string;
    entryDate: Date;
    item: {
      id: string;
      title: string;
      category: ItemCategory;
      completionPercentage: number;
    };
    createdAt: Date;
  }>;
  timeframe: string;
  generatedAt: string;
}

export interface TimelineEntry {
  id: string;
  type: 'progress' | 'milestone' | 'completion';
  date: Date;
  title: string;
  description: string;
  item: {
    id: string;
    title: string;
    category: ItemCategory;
    completionPercentage?: number;
  };
  metadata: any;
}

export interface ProgressInsight {
  type: 'improvement' | 'celebration' | 'pattern' | 'motivation';
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'positive';
  actionable: boolean;
  suggestion?: string;
}

export interface AchievementProgress {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  points: number;
  isEarned: boolean;
  progress: number;
  currentValue: number;
  targetValue: number;
  criteria: {
    type: string;
    value: number;
    category?: string;
    timeframe?: number;
  };
}

export interface UserAchievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  points: number;
  earnedAt: Date;
  metadata?: any;
  isShared?: boolean;
  sharedAt?: Date;
  shareMessage?: string;
}

export interface SharedAchievement {
  id: string;
  achievement: {
    id: string;
    name: string;
    description: string;
    icon: string;
    category: string;
    points: number;
  };
  user: {
    id: string;
    name: string;
    email: string;
    picture?: string;
  };
  earnedAt: Date;
  sharedAt: Date;
  shareMessage?: string;
  reactionCount: number;
  reactions: AchievementReaction[];
}

export interface AchievementShare {
  id: string;
  platform: 'twitter' | 'facebook' | 'linkedin' | 'native' | 'copy';
  sharedAt: Date;
  metadata?: any;
  content: any;
}

export interface AchievementReaction {
  id: string;
  type: 'LIKE' | 'LOVE' | 'CELEBRATE' | 'INSPIRE';
  user: {
    id: string;
    name: string;
    picture?: string;
  };
  createdAt: Date;
}

export interface StreakData {
  currentStreak: number;
  longestStreak: number;
}