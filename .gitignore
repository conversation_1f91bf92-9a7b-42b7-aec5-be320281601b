# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
*.tsbuildinfo

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/
.turbo/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Database
*.sqlite
*.sqlite3
*.db

# Testing
# Keep test coverage directories if you want to commit them
# coverage/

# Expo
.expo/
.expo-shared/

# React Native
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*
web-build/

# Xcode
*.xcodeproj
*.xcworkspace
*.pbxuser
*.mode1v3
*.mode2v3
*.perspectivev3
!default.pbxuser
!default.mode1v3
!default.mode2v3
!default.perspectivev3
xcuserdata
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate

# Android
*.apk
*.ap_
*.aab
.gradle
/build/
/*/build/
gradle-app.setting
!gradle-wrapper.jar
.gradletasknamecache
.gradle/
local.properties
*.iml
.idea/
.DS_Store/
build/
captures/
.externalNativeBuild
.cxx/

# Bundle artifacts
*.jsbundle

# CocoaPods
/ios/Pods/

# Temporary files
*.tmp
*.temp

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Prisma
# Keep migrations in version control
# /prisma/migrations/

# Additional .env files
backend/.env
