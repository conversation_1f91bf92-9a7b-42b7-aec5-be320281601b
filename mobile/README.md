# 📱 DreamVault Mobile App

A cross-platform mobile application for managing bucket lists and achieving life goals on the go. Built with React Native, Expo, and modern mobile development practices for iOS and Android.

## 🚀 Features

### 📱 Native Mobile Experience
- **Cross-Platform** - Single codebase for iOS and Android
- **Native Performance** - Optimized for mobile devices
- **Offline Support** - Continue working without internet connection
- **Push Notifications** - Stay motivated with goal reminders
- **Responsive Design** - Adapts to different screen sizes and orientations

### 🔐 Secure Authentication
- **Auth0 Integration** - Secure mobile authentication with biometric support
- **Token Management** - Automatic token refresh and secure storage
- **Biometric Authentication** - Fingerprint and Face ID support
- **Account Linking** - Seamless sync across devices

### 📋 Bucket List Management
- **Create & Edit Goals** - Full CRUD operations for bucket list items
- **Progress Tracking** - Visual progress indicators and completion tracking
- **Smart Categorization** - Organize by travel, adventure, career, personal, creative, fitness, education, and social
- **Priority Management** - Set and adjust priorities with intuitive interface
- **Status Updates** - Track progress from planning to completion

### 📸 Media & Documentation
- **Camera Integration** - Capture moments directly in the app
- **Photo Gallery** - Attach photos from device gallery
- **Media Management** - View, organize, and delete media files
- **Progress Documentation** - Document your journey with photos and notes

### 🔍 Search & Discovery
- **Smart Search** - Find your goals quickly with intelligent search
- **Filter Options** - Filter by category, priority, and status
- **Social Discovery** - Explore and get inspired by others' goals

### 🌐 Social Features
- **List Sharing** - Share bucket lists with friends and family
- **Achievement Sharing** - Celebrate milestones together
- **Social Feed** - Discover inspiring goals from the community

## 🏗️ Architecture

### Technology Stack
- **React Native 0.79** - Cross-platform mobile framework
- **Expo SDK 53** - Development platform and tools
- **TypeScript** - Type-safe development
- **React Navigation 6** - Native navigation patterns
- **Auth0 React Native** - Mobile authentication
- **Zustand** - Lightweight state management
- **Async Storage** - Persistent local storage
- **Expo Camera** - Camera and media capture
- **React Native Vector Icons** - Icon library
- **Metro** - JavaScript bundler for React Native

### Project Structure
```
src/
├── App.tsx                 # Main app component
├── components/             # Reusable UI components
├── config/                 # Configuration files
│   ├── auth0.ts           # Auth0 mobile configuration
│   ├── env.ts             # Environment variables
│   └── environment.ts     # Environment-specific settings
├── contexts/              # React contexts
│   └── AuthContext.tsx    # Authentication context
├── hooks/                 # Custom React hooks
│   └── useApi.ts          # API integration hook
├── navigation/            # Navigation setup
│   ├── AppNavigator.tsx   # Main app navigation
│   └── AuthNavigator.tsx  # Authentication flow navigation
├── screens/               # Screen components
│   ├── auth/              # Authentication screens
│   │   ├── ForgotPasswordScreen.tsx
│   │   ├── LoginScreen.tsx
│   │   ├── ProfileSetupScreen.tsx
│   │   └── RegisterScreen.tsx
│   ├── AddItemScreen.tsx  # Add new bucket list item
│   ├── BucketListScreen.tsx # Main bucket list view
│   ├── EditItemScreen.tsx # Edit existing item
│   ├── HomeScreen.tsx     # Dashboard/home screen
│   ├── ItemDetailScreen.tsx # Detailed item view
│   ├── NotificationsScreen.tsx # Notifications
│   ├── ProfileScreen.tsx  # User profile
│   ├── ProgressScreen.tsx # Progress tracking
│   └── SearchScreen.tsx   # Search functionality
├── types/                 # Mobile-specific type definitions
│   └── react-native-vector-icons.d.ts
└── utils/                 # Utility functions
    ├── api.ts             # API client
    ├── camera.ts          # Camera utilities
    ├── errorHandler.ts    # Error handling
    ├── initialization.ts  # App initialization
    └── storage.ts         # Storage utilities
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js 18+ and npm
- React Native development environment
- Expo CLI: `npm install -g @expo/cli`
- Auth0 account with mobile app configuration
- Backend API server running (see `/backend/README.md`)

### Quick Start

1. **Install dependencies**
   ```bash
   cd mobile
   npm install
   ```

2. **Environment Configuration**
   
   Create a `.env` file in the mobile directory:
   ```bash
   # API Configuration
   API_BASE_URL=http://localhost:3001/api
   
   # Auth0 Mobile Configuration
   AUTH0_DOMAIN=your-auth0-domain.auth0.com
   AUTH0_CLIENT_ID=your-mobile-client-id
   AUTH0_AUDIENCE=your-api-identifier
   
   # Feature Flags
   ENABLE_BIOMETRIC_AUTH=true
   ENABLE_OFFLINE_SYNC=true
   ENABLE_PUSH_NOTIFICATIONS=true
   ENABLE_SOCIAL_FEATURES=true
   
   # Development
   EXPO_PUBLIC_API_URL=http://localhost:3001/api
   ```

3. **Start development server**
   ```bash
   npm run start
   ```

4. **Run on specific platforms**
   ```bash
   # iOS Simulator
   npm run ios
   
   # Android Emulator
   npm run android
   
   # Physical device with Expo Go
   # Scan QR code from the terminal
   ```

## 📜 Available Scripts

### Development
- `npm start` - Start Expo development server
- `npm run ios` - Run on iOS simulator
- `npm run android` - Run on Android emulator
- `npm run start-tunnel` - Start with tunnel for physical device testing
- `npm run start-dev-client` - Start with development build

### Code Quality
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking
- `npm test` - Run Jest tests

## 📱 Platform-Specific Features

### iOS Features
- **Face ID/Touch ID** - Biometric authentication
- **iOS Design Patterns** - Native iOS navigation and UI elements
- **Camera Roll Integration** - Access iOS photo library
- **Push Notifications** - Native iOS notifications

### Android Features
- **Fingerprint Authentication** - Android biometric authentication
- **Material Design** - Android design language integration
- **Back Button Handling** - Proper Android back navigation
- **File System Access** - Android-specific file operations

## 🔧 Configuration

### Expo Configuration (`app.json`)
```json
{
  "expo": {
    "name": "DreamVault",
    "slug": "dreamvault",
    "version": "1.0.0",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "userInterfaceStyle": "automatic",
    "splash": {
      "image": "./assets/splash.png",
      "resizeMode": "contain",
      "backgroundColor": "#ffffff"
    },
    "plugins": [
      "expo-camera",
      "expo-image-picker",
      "expo-secure-store"
    ],
    "ios": {
      "supportsTablet": true,
      "bundleIdentifier": "com.dreamvault.app"
    },
    "android": {
      "adaptiveIcon": {
        "foregroundImage": "./assets/adaptive-icon.png",
        "backgroundColor": "#FFFFFF"
      },
      "package": "com.dreamvault.app"
    }
  }
}
```

### Auth0 Mobile Configuration
```typescript
// config/auth0.ts
export const auth0Config = {
  domain: process.env.AUTH0_DOMAIN!,
  clientId: process.env.AUTH0_CLIENT_ID!,
  audience: process.env.AUTH0_AUDIENCE,
  additionalParameters: {},
  customScheme: 'com.dreamvault.app'
};
```

## 🔌 API Integration

### Authentication Flow
1. User opens app and is presented with login/register options
2. Auth0 handles authentication with redirect back to app
3. JWT tokens are stored securely using Expo SecureStore
4. API requests automatically include authentication headers
5. Token refresh handled automatically in the background

### API Client (`utils/api.ts`)
```typescript
class ApiClient {
  private baseURL: string;
  private authToken: string | null = null;

  constructor() {
    this.baseURL = process.env.API_BASE_URL || 'http://localhost:3001/api';
  }

  // Automatic token inclusion
  // Request/response interceptors
  // Error handling and retries
  // Offline queue management
}
```

### Offline Support
- Local storage with AsyncStorage
- Offline queue for API requests
- Sync when connection restored
- Optimistic UI updates

## 📸 Camera & Media

### Camera Integration
```typescript
// utils/camera.ts
export const cameraUtils = {
  takePhoto: async () => {
    // Camera permissions
    // Launch camera
    // Process image
    // Upload to server
  },
  
  pickFromGallery: async () => {
    // Gallery permissions
    // Launch image picker
    // Process selected image
    // Upload to server
  }
};
```

### Permissions Required
- **Camera** - Take photos for progress documentation
- **Photo Library** - Select photos from device
- **Storage** - Save and cache images
- **Notifications** - Send goal reminders

## 🧪 Testing

### Unit Testing
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Device Testing
```bash
# Test on iOS simulator
npm run ios

# Test on Android emulator
npm run android

# Test on physical device
npm run start-tunnel
# Scan QR code with Expo Go app
```

### Testing Strategies
- Component testing with React Native Testing Library
- Integration testing for navigation flows
- API integration testing
- Authentication flow testing
- Camera and media functionality testing

## 🚀 Deployment

### Development Builds
```bash
# Create development build for iOS
expo build:ios --type simulator

# Create development build for Android
expo build:android --type apk
```

### Production Builds

**iOS App Store**
```bash
# Create production build
expo build:ios --type archive

# Submit to App Store
expo upload:ios
```

**Google Play Store**
```bash
# Create production build
expo build:android --type app-bundle

# Submit to Play Store
expo upload:android
```

### Environment Configuration for Production
```bash
# Production API
API_BASE_URL=https://api.yourdomain.com/api

# Auth0 Production
AUTH0_DOMAIN=your-production-domain.auth0.com
AUTH0_CLIENT_ID=production-mobile-client-id
AUTH0_AUDIENCE=production-api-identifier

# Features
ENABLE_BIOMETRIC_AUTH=true
ENABLE_OFFLINE_SYNC=true
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_SOCIAL_FEATURES=true
```

## 🔧 Troubleshooting

### Common Issues

**Metro Bundle Issues**
```bash
# Clear Metro cache
npx expo start --clear

# Reset Metro resolver
rm -rf node_modules && npm install
```

**iOS Simulator Issues**
```bash
# Reset iOS simulator
xcrun simctl erase all

# Rebuild iOS app
npx expo run:ios --clear
```

**Android Emulator Issues**
```bash
# Clean Android build
cd android && ./gradlew clean

# Rebuild Android app
npx expo run:android --clear
```

**Auth0 Configuration Issues**
- Verify callback URLs include custom scheme
- Check bundle identifiers match Auth0 app settings
- Ensure Auth0 domain and client ID are correct

## 🤝 Contributing

1. Follow React Native best practices
2. Use TypeScript for all new code
3. Test on both iOS and Android
4. Follow existing navigation patterns
5. Use proper error handling
6. Implement accessibility features

### Code Style
- Use functional components with hooks
- Implement proper loading states
- Handle offline scenarios gracefully
- Follow platform-specific design guidelines

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](../LICENSE) file for details.

## 🔗 Related Projects

- [Backend API](../backend/README.md) - Express.js API server
- [Web App](../web/README.md) - Next.js web application
- [Shared Types](../packages/types/README.md) - TypeScript type definitions

---

**Made with ❤️ using React Native, Expo, and modern mobile technologies**

*Your dreams in your pocket, wherever you go.*