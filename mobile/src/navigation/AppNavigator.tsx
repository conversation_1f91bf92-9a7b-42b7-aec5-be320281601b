import React from "react";
import { View, Text } from "react-native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { MaterialIcons as Icon } from "@expo/vector-icons";
import { useAuth } from "../contexts/AuthContext";

// Import screens
import AuthNavigator from "./AuthNavigator";
import HomeScreen from "../screens/HomeScreen";
import BucketListScreen from "../screens/BucketListScreen";
import SearchScreen from "../screens/SearchScreen";
import ProgressScreen from "../screens/ProgressScreen";
import ProfileScreen from "../screens/ProfileScreen";
import AddItemScreen from "../screens/AddItemScreen";
import ItemDetailScreen from "../screens/ItemDetailScreen";
import EditItemScreen from "../screens/EditItemScreen";
import DiscoveryScreen from "../screens/DiscoveryScreen";
import SharedListsScreen from "../screens/SharedListsScreen";
import AchievementsScreen from "../screens/AchievementsScreen";

// Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  AddItem: undefined;
  ItemDetail: { itemId: string };
  EditItem: { itemId: string };
  Notifications: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  BucketList: undefined;
  Discovery: undefined;
  SharedLists: undefined;
  Achievements: undefined;
  Profile: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

// Extract tab bar icon component to prevent re-creation on each render
const getTabBarIcon =
  (route: { name: keyof MainTabParamList }) =>
  ({ color, size }: { color: string; size: number }) => {
    let iconName: string;

    switch (route.name) {
      case "Home":
        iconName = "home";
        break;
      case "BucketList":
        iconName = "list";
        break;
      case "Discovery":
        iconName = "explore";
        break;
      case "SharedLists":
        iconName = "group-work";
        break;
      case "Achievements":
        iconName = "emoji-events";
        break;
      case "Profile":
        iconName = "person";
        break;
      default:
        iconName = "help";
    }

    return <Icon name={iconName as any} size={size} color={color} />;
  };

function MainTabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: getTabBarIcon(route),
        tabBarActiveTintColor: "#007AFF",
        tabBarInactiveTintColor: "gray",
        headerShown: false,
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="BucketList" component={BucketListScreen} />
      <Tab.Screen name="Discovery" component={DiscoveryScreen} />
      <Tab.Screen name="SharedLists" component={SharedListsScreen} />
      <Tab.Screen name="Achievements" component={AchievementsScreen} />
      <Tab.Screen name="Profile" component={ProfileScreen} />
    </Tab.Navigator>
  );
}

export default function AppNavigator() {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
        <Text>Loading...</Text>
      </View>
    );
  }

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {!user ? (
        <Stack.Screen name="Auth" component={AuthNavigator} />
      ) : (
        <>
          <Stack.Screen name="Main" component={MainTabNavigator} />
          <Stack.Screen
            name="AddItem"
            component={AddItemScreen}
            options={{
              headerShown: true,
              title: "Add Dream",
              presentation: "modal",
            }}
          />
          <Stack.Screen
            name="ItemDetail"
            component={ItemDetailScreen}
            options={{
              headerShown: true,
              title: "Dream Details",
            }}
          />
          <Stack.Screen
            name="EditItem"
            component={EditItemScreen}
            options={{
              headerShown: true,
              title: "Edit Dream",
              presentation: "modal",
            }}
          />
        </>
      )}
    </Stack.Navigator>
  );
}
