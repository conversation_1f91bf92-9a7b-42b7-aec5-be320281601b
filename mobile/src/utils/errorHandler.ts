// Error handling utilities for API responses and Auth0 errors

export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp?: string;
}

// Common error codes
export const ERROR_CODES = {
  // Network errors
  NETWORK_ERROR: "NETWORK_ERROR",
  TIMEOUT_ERROR: "TIMEOUT_ERROR",
  CONNECTION_ERROR: "CONNECTION_ERROR",

  // Authentication errors
  AUTH_TOKEN_INVALID: "AUTH_TOKEN_INVALID",
  AUTH_TOKEN_EXPIRED: "AUTH_TOKEN_EXPIRED",
  AUTH_REQUIRED: "AUTH_REQUIRED",
  INVALID_CREDENTIALS: "INVALID_CREDENTIALS",

  // API errors
  VALIDATION_FAILED: "VALIDATION_FAILED",
  RESOURCE_NOT_FOUND: "RESOURCE_NOT_FOUND",
  PERMISSION_DENIED: "PERMISSION_DENIED",
  RATE_LIMIT_EXCEEDED: "RATE_LIMIT_EXCEEDED",

  // App errors
  OFFLINE_ERROR: "OFFLINE_ERROR",
  STORAGE_ERROR: "STORAGE_ERROR",
  UNKNOWN_ERROR: "UNKNOWN_ERROR",
} as const;

// Error message mappings
const ERROR_MESSAGES: Record<string, string> = {
  [ERROR_CODES.NETWORK_ERROR]:
    "Network connection failed. Please check your internet connection.",
  [ERROR_CODES.TIMEOUT_ERROR]: "Request timed out. Please try again.",
  [ERROR_CODES.CONNECTION_ERROR]:
    "Unable to connect to server. Please try again later.",

  [ERROR_CODES.AUTH_TOKEN_INVALID]:
    "Your session is invalid. Please log in again.",
  [ERROR_CODES.AUTH_TOKEN_EXPIRED]:
    "Your session has expired. Please log in again.",
  [ERROR_CODES.AUTH_REQUIRED]: "Authentication required. Please log in.",
  [ERROR_CODES.INVALID_CREDENTIALS]: "Invalid email or password.",

  [ERROR_CODES.VALIDATION_FAILED]: "Please check your input and try again.",
  [ERROR_CODES.RESOURCE_NOT_FOUND]: "The requested item was not found.",
  [ERROR_CODES.PERMISSION_DENIED]:
    "You do not have permission to perform this action.",
  [ERROR_CODES.RATE_LIMIT_EXCEEDED]:
    "Too many requests. Please wait a moment and try again.",

  [ERROR_CODES.OFFLINE_ERROR]:
    "You are offline. Some features may not be available.",
  [ERROR_CODES.STORAGE_ERROR]: "Failed to save data locally.",
  [ERROR_CODES.UNKNOWN_ERROR]:
    "An unexpected error occurred. Please try again.",
};

// Create standardized error object
export const createError = (
  code: string,
  message?: string,
  details?: any,
): AppError => ({
  code,
  message:
    message ||
    ERROR_MESSAGES[code] ||
    ERROR_MESSAGES[ERROR_CODES.UNKNOWN_ERROR],
  details,
  timestamp: new Date().toISOString(),
});

// Handle API response errors
export const handleApiError = (error: any): AppError => {
  // Handle network errors
  if (!error.response) {
    if (error.code === "NETWORK_ERROR" || error.message?.includes("Network")) {
      return createError(ERROR_CODES.NETWORK_ERROR);
    }
    if (error.code === "TIMEOUT" || error.message?.includes("timeout")) {
      return createError(ERROR_CODES.TIMEOUT_ERROR);
    }
    return createError(ERROR_CODES.CONNECTION_ERROR);
  }

  // Handle HTTP status codes
  const status = error.response?.status;
  const data = error.response?.data;

  switch (status) {
    case 400:
      return createError(
        data?.error?.code || ERROR_CODES.VALIDATION_FAILED,
        data?.error?.message,
        data?.error?.details,
      );
    case 401:
      return createError(
        data?.error?.code || ERROR_CODES.AUTH_TOKEN_INVALID,
        data?.error?.message,
      );
    case 403:
      return createError(
        data?.error?.code || ERROR_CODES.PERMISSION_DENIED,
        data?.error?.message,
      );
    case 404:
      return createError(
        data?.error?.code || ERROR_CODES.RESOURCE_NOT_FOUND,
        data?.error?.message,
      );
    case 429:
      return createError(
        data?.error?.code || ERROR_CODES.RATE_LIMIT_EXCEEDED,
        data?.error?.message,
      );
    case 500:
    case 502:
    case 503:
    case 504:
      return createError(
        ERROR_CODES.CONNECTION_ERROR,
        "Server error. Please try again later.",
      );
    default:
      return createError(
        data?.error?.code || ERROR_CODES.UNKNOWN_ERROR,
        data?.error?.message || `HTTP ${status} error`,
      );
  }
};

// Handle Auth0 errors
export const handleAuth0Error = (error: any): AppError => {
  const errorCode = error.code || error.error;
  const errorMessage = error.message || error.error_description;

  switch (errorCode) {
    case "a0.authentication.cancelled":
      return createError("LOGIN_CANCELLED", "Login was cancelled");
    case "invalid_grant":
    case "invalid_user_password":
      return createError(ERROR_CODES.INVALID_CREDENTIALS);
    case "access_denied":
      return createError(ERROR_CODES.PERMISSION_DENIED, "Access denied");
    case "unauthorized":
      return createError(ERROR_CODES.AUTH_TOKEN_INVALID);
    case "network_error":
      return createError(ERROR_CODES.NETWORK_ERROR);
    case "user_exists":
      return createError(
        "USER_EXISTS",
        "An account with this email already exists",
      );
    default:
      return createError(
        ERROR_CODES.UNKNOWN_ERROR,
        errorMessage || "Authentication failed",
      );
  }
};

// Check if error requires re-authentication
export const requiresReauth = (error: AppError): boolean => {
  return [
    ERROR_CODES.AUTH_TOKEN_INVALID,
    ERROR_CODES.AUTH_TOKEN_EXPIRED,
    ERROR_CODES.AUTH_REQUIRED,
  ].includes(error.code as any);
};

// Get user-friendly error message
export const getErrorMessage = (error: AppError | string): string => {
  if (typeof error === "string") {
    return ERROR_MESSAGES[error] || error;
  }
  return error.message || ERROR_MESSAGES[ERROR_CODES.UNKNOWN_ERROR];
};
