import { Alert, Platform } from "react-native";

export interface CameraOptions {
  mediaTypes?: "images" | "videos" | "all";
  quality?: number;
  maxWidth?: number;
  maxHeight?: number;
  allowsEditing?: boolean;
}

export interface CameraResult {
  uri: string;
  fileName?: string;
  fileSize?: number;
  type?: string;
}

// STUB: Camera functionality disabled for Expo Go compatibility
// To enable camera features, install expo-image-picker and expo-camera
// and create a development build

// Request camera permission (stub)
export const requestCameraPermission = async (): Promise<boolean> => {
  Alert.alert(
    "Camera Not Available",
    "Camera functionality requires a development build. This feature is disabled in Expo Go for compatibility."
  );
  return false;
};

// Request photo library permission (stub)
export const requestPhotoLibraryPermission = async (): Promise<boolean> => {
  Alert.alert(
    "Photo Library Not Available",
    "Photo library functionality requires a development build. This feature is disabled in Expo Go for compatibility."
  );
  return false;
};

// Open camera (stub)
export const openCamera = async (
  _options: CameraOptions = {},
): Promise<CameraResult | null> => {
  Alert.alert(
    "Camera Not Available",
    "Camera functionality requires a development build with expo-camera. This feature is disabled in Expo Go for compatibility."
  );
  return null;
};

// Open photo library (stub)
export const openPhotoLibrary = async (
  _options: CameraOptions = {},
): Promise<CameraResult | null> => {
  Alert.alert(
    "Photo Library Not Available",
    "Photo library functionality requires a development build with expo-image-picker. This feature is disabled in Expo Go for compatibility."
  );
  return null;
};

// Show photo picker options (stub)
export const showPhotoPickerOptions = (): Promise<
  "camera" | "library" | null
> => {
  return new Promise((resolve) => {
    Alert.alert(
      "Photo Features Not Available",
      "Camera and photo library features require a development build. These features are disabled in Expo Go for compatibility.",
      [{ text: "OK", onPress: () => resolve(null) }]
    );
  });
};

// Complete photo picker flow (stub)
export const pickPhoto = async (
  _options: CameraOptions = {},
): Promise<CameraResult | null> => {
  Alert.alert(
    "Photo Features Not Available",
    "Camera and photo library features require a development build with expo-image-picker and expo-camera. These features are disabled in Expo Go for compatibility."
  );
  return null;
};
