import AsyncStorage from "@react-native-async-storage/async-storage";
import { BucketListItem } from "@dreamvault/types";

// Storage keys
const BUCKET_LIST_ITEMS_KEY = "@dreamvault_bucket_list_items";
const OFFLINE_ACTIONS_KEY = "@dreamvault_offline_actions";
const USER_PREFERENCES_KEY = "@dreamvault_user_preferences";

// Types
export interface OfflineAction {
  id: string;
  type: "create" | "update" | "delete";
  resource: "bucket_list_item";
  data: any;
  timestamp: number;
}

export interface UserPreferences {
  theme: "light" | "dark" | "system";
  notifications: {
    reminders: boolean;
    achievements: boolean;
    social: boolean;
  };
  privacy: {
    profileVisibility: "public" | "private";
    shareProgress: boolean;
  };
}

// Bucket List Items Storage
export const storeBucketListItems = async (
  items: BucketListItem[],
): Promise<void> => {
  try {
    await AsyncStorage.setItem(BUCKET_LIST_ITEMS_KEY, JSON.stringify(items));
  } catch (error) {
    console.error("Failed to store bucket list items:", error);
    throw error;
  }
};

export const getBucketListItems = async (): Promise<BucketListItem[]> => {
  try {
    const itemsJson = await AsyncStorage.getItem(BUCKET_LIST_ITEMS_KEY);
    if (!itemsJson) {
      return [];
    }

    const items = JSON.parse(itemsJson);
    // Convert date strings back to Date objects
    return items.map((item: any) => ({
      ...item,
      createdAt: new Date(item.createdAt),
      updatedAt: new Date(item.updatedAt),
      targetDate: item.targetDate ? new Date(item.targetDate) : undefined,
    }));
  } catch (error) {
    console.error("Failed to get bucket list items:", error);
    return [];
  }
};

export const addBucketListItem = async (
  item: BucketListItem,
): Promise<void> => {
  try {
    const existingItems = await getBucketListItems();
    const updatedItems = [...existingItems, item];
    await storeBucketListItems(updatedItems);
  } catch (error) {
    console.error("Failed to add bucket list item:", error);
    throw error;
  }
};

export const updateBucketListItem = async (
  itemId: string,
  updates: Partial<BucketListItem>,
): Promise<void> => {
  try {
    const existingItems = await getBucketListItems();
    const updatedItems = existingItems.map((item) =>
      item.id === itemId
        ? { ...item, ...updates, updatedAt: new Date() }
        : item,
    );
    await storeBucketListItems(updatedItems);
  } catch (error) {
    console.error("Failed to update bucket list item:", error);
    throw error;
  }
};

export const deleteBucketListItem = async (itemId: string): Promise<void> => {
  try {
    const existingItems = await getBucketListItems();
    const updatedItems = existingItems.filter((item) => item.id !== itemId);
    await storeBucketListItems(updatedItems);
  } catch (error) {
    console.error("Failed to delete bucket list item:", error);
    throw error;
  }
};

// Offline Actions Queue
export const addOfflineAction = async (
  action: Omit<OfflineAction, "id" | "timestamp">,
): Promise<void> => {
  try {
    const existingActions = await getOfflineActions();
    const newAction: OfflineAction = {
      ...action,
      id: Date.now().toString(),
      timestamp: Date.now(),
    };
    const updatedActions = [...existingActions, newAction];
    await AsyncStorage.setItem(
      OFFLINE_ACTIONS_KEY,
      JSON.stringify(updatedActions),
    );
  } catch (error) {
    console.error("Failed to add offline action:", error);
    throw error;
  }
};

export const getOfflineActions = async (): Promise<OfflineAction[]> => {
  try {
    const actionsJson = await AsyncStorage.getItem(OFFLINE_ACTIONS_KEY);
    return actionsJson ? JSON.parse(actionsJson) : [];
  } catch (error) {
    console.error("Failed to get offline actions:", error);
    return [];
  }
};

export const clearOfflineActions = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(OFFLINE_ACTIONS_KEY);
  } catch (error) {
    console.error("Failed to clear offline actions:", error);
    throw error;
  }
};

export const removeOfflineAction = async (actionId: string): Promise<void> => {
  try {
    const existingActions = await getOfflineActions();
    const updatedActions = existingActions.filter(
      (action) => action.id !== actionId,
    );
    await AsyncStorage.setItem(
      OFFLINE_ACTIONS_KEY,
      JSON.stringify(updatedActions),
    );
  } catch (error) {
    console.error("Failed to remove offline action:", error);
    throw error;
  }
};

// User Preferences
export const getUserPreferences = async (): Promise<UserPreferences> => {
  try {
    const preferencesJson = await AsyncStorage.getItem(USER_PREFERENCES_KEY);
    if (!preferencesJson) {
      // Return default preferences
      return {
        theme: "system",
        notifications: {
          reminders: true,
          achievements: true,
          social: true,
        },
        privacy: {
          profileVisibility: "private",
          shareProgress: false,
        },
      };
    }
    return JSON.parse(preferencesJson);
  } catch (error) {
    console.error("Failed to get user preferences:", error);
    // Return default preferences on error
    return {
      theme: "system",
      notifications: {
        reminders: true,
        achievements: true,
        social: true,
      },
      privacy: {
        profileVisibility: "private",
        shareProgress: false,
      },
    };
  }
};

export const updateUserPreferences = async (
  preferences: Partial<UserPreferences>,
): Promise<void> => {
  try {
    const existingPreferences = await getUserPreferences();
    const updatedPreferences = { ...existingPreferences, ...preferences };
    await AsyncStorage.setItem(
      USER_PREFERENCES_KEY,
      JSON.stringify(updatedPreferences),
    );
  } catch (error) {
    console.error("Failed to update user preferences:", error);
    throw error;
  }
};

// General storage utilities
export const clearAllData = async (): Promise<void> => {
  try {
    await AsyncStorage.multiRemove([
      BUCKET_LIST_ITEMS_KEY,
      OFFLINE_ACTIONS_KEY,
      USER_PREFERENCES_KEY,
    ]);
  } catch (error) {
    console.error("Failed to clear all data:", error);
    throw error;
  }
};

export const getStorageSize = async (): Promise<{
  keys: number;
  size: string;
}> => {
  try {
    const keys = await AsyncStorage.getAllKeys();
    let totalSize = 0;

    for (const key of keys) {
      const value = await AsyncStorage.getItem(key);
      if (value) {
        totalSize += new Blob([value]).size;
      }
    }

    const sizeInMB = (totalSize / (1024 * 1024)).toFixed(2);

    return {
      keys: keys.length,
      size: `${sizeInMB} MB`,
    };
  } catch (error) {
    console.error("Failed to get storage size:", error);
    return { keys: 0, size: "0 MB" };
  }
};
