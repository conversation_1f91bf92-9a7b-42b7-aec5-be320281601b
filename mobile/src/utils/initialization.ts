import { validateEnvironment } from "../config/env";

// Initialize the app with required configurations
export const initializeApp = async (): Promise<void> => {
  try {
    console.log("🚀 Initializing DreamVault Mobile App...");

    // Validate environment configuration
    validateEnvironment();
    console.log("✅ Environment configuration validated");

    // Note: Offline sync is now handled by Supabase client-side caching
    console.log("✅ Supabase client initialized");

    console.log("🎉 App initialization completed successfully");
  } catch (error) {
    console.error("❌ App initialization failed:", error);
    throw error;
  }
};
