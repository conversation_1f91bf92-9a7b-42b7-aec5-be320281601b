import React, { useEffect } from "react";
import { NavigationContainer } from "@react-navigation/native";
import { AuthProvider } from "./contexts/AuthContext";
import AppNavigator from "./navigation/AppNavigator";
import { initializeApp } from "./utils/initialization";

export default function App() {
  useEffect(() => {
    // Initialize the app
    initializeApp().catch((error) => {
      console.error("Failed to initialize app:", error);
    });
  }, []);

  return (
    <AuthProvider>
      <NavigationContainer>
        <AppNavigator />
      </NavigationContainer>
    </AuthProvider>
  );
}
