import { useState, useCallback } from "react";
import {
  AppError,
  handleApiError,
  createError,
  ERROR_CODES,
} from "../utils/errorHandler";

interface ApiState<T> {
  data: T | null;
  loading: boolean;
  error: AppError | null;
}

interface UseApiReturn<T> {
  data: T | null;
  loading: boolean;
  error: AppError | null;
  execute: (...args: any[]) => Promise<T | null>;
  reset: () => void;
  setData: (data: T | null) => void;
}

// Generic hook for API calls with loading and error states
export function useApi<T = any>(
  apiFunction: (
    ...args: any[]
  ) => Promise<{ success: boolean; data?: T; error?: string }>,
): UseApiReturn<T> {
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const execute = useCallback(
    async (...args: any[]): Promise<T | null> => {
      setState((prev) => ({ ...prev, loading: true, error: null }));

      try {
        const response = await apiFunction(...args);

        if (response.success && response.data) {
          setState({
            data: response.data,
            loading: false,
            error: null,
          });
          return response.data;
        } else {
          const error = createError(
            ERROR_CODES.UNKNOWN_ERROR,
            response.error || "API call failed",
          );
          setState({
            data: null,
            loading: false,
            error,
          });
          return null;
        }
      } catch (error) {
        const handledError = handleApiError(error);
        setState({
          data: null,
          loading: false,
          error: handledError,
        });
        return null;
      }
    },
    [apiFunction],
  );

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    });
  }, []);

  const setData = useCallback((data: T | null) => {
    setState((prev) => ({ ...prev, data }));
  }, []);

  return {
    data: state.data,
    loading: state.loading,
    error: state.error,
    execute,
    reset,
    setData,
  };
}

// Hook for paginated API calls
export function usePaginatedApi<T = any>(
  apiFunction: (...args: any[]) => Promise<{
    success: boolean;
    data?: { items: T[]; pagination: any };
    error?: string;
  }>,
) {
  const [state, setState] = useState<{
    items: T[];
    pagination: any;
    loading: boolean;
    error: AppError | null;
    refreshing: boolean;
    loadingMore: boolean;
  }>({
    items: [],
    pagination: null,
    loading: false,
    error: null,
    refreshing: false,
    loadingMore: false,
  });

  const loadPage = useCallback(
    async (page: number = 1, append: boolean = false) => {
      setState((prev) => ({
        ...prev,
        loading: !append,
        loadingMore: append,
        error: null,
      }));

      try {
        const response = await apiFunction({ page });

        if (response.success && response.data) {
          setState((prev) => ({
            ...prev,
            items: append
              ? [...prev.items, ...response.data!.items]
              : response.data!.items,
            pagination: response.data!.pagination,
            loading: false,
            loadingMore: false,
            error: null,
          }));
        } else {
          const error = createError(
            ERROR_CODES.UNKNOWN_ERROR,
            response.error || "Failed to load data",
          );
          setState((prev) => ({
            ...prev,
            loading: false,
            loadingMore: false,
            error,
          }));
        }
      } catch (error) {
        const handledError = handleApiError(error);
        setState((prev) => ({
          ...prev,
          loading: false,
          loadingMore: false,
          error: handledError,
        }));
      }
    },
    [apiFunction],
  );

  const refresh = useCallback(async () => {
    setState((prev) => ({ ...prev, refreshing: true, error: null }));
    await loadPage(1, false);
    setState((prev) => ({ ...prev, refreshing: false }));
  }, [loadPage]);

  const loadMore = useCallback(async () => {
    if (state.pagination?.hasNextPage && !state.loadingMore) {
      await loadPage(state.pagination.page + 1, true);
    }
  }, [loadPage, state.pagination, state.loadingMore]);

  const reset = useCallback(() => {
    setState({
      items: [],
      pagination: null,
      loading: false,
      error: null,
      refreshing: false,
      loadingMore: false,
    });
  }, []);

  return {
    items: state.items,
    pagination: state.pagination,
    loading: state.loading,
    error: state.error,
    refreshing: state.refreshing,
    loadingMore: state.loadingMore,
    loadPage,
    refresh,
    loadMore,
    reset,
  };
}

// Hook for optimistic updates
export function useOptimisticApi<T = any>() {
  const [optimisticData, setOptimisticData] = useState<T[]>([]);

  const addOptimistic = useCallback((item: T) => {
    setOptimisticData((prev) => [...prev, item]);
  }, []);

  const updateOptimistic = useCallback((id: string, updates: Partial<T>) => {
    setOptimisticData((prev) =>
      prev.map((item) =>
        (item as any).id === id ? { ...item, ...updates } : item,
      ),
    );
  }, []);

  const removeOptimistic = useCallback((id: string) => {
    setOptimisticData((prev) => prev.filter((item) => (item as any).id !== id));
  }, []);

  const clearOptimistic = useCallback(() => {
    setOptimisticData([]);
  }, []);

  return {
    optimisticData,
    addOptimistic,
    updateOptimistic,
    removeOptimistic,
    clearOptimistic,
  };
}
