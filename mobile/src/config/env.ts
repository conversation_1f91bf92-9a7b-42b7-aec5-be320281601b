// Simple environment configuration for React Native
// In a production app, you would use react-native-config or similar

// Check if __DEV__ is available, fallback to NODE_ENV check
const isDev = typeof __DEV__ !== 'undefined' ? __DEV__ : process.env.NODE_ENV !== 'production';

export const ENV = {
  // API Configuration
  API_BASE_URL: isDev
    ? "http://localhost:3001/api"
    : "https://api.dreamvault.app/api",

  // Supabase Configuration
  SUPABASE_URL: isDev
    ? "https://wjqfdtfxoznjecetegdj.supabase.co"
    : "https://wjqfdtfxoznjecetegdj.supabase.co",

  SUPABASE_ANON_KEY: isDev
    ? "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqcWZkdGZ4b3puamVjZXRlZ2RqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQwODA2MjgsImV4cCI6MjA2OTY1NjYyOH0.uZmcMU35SkYxlHRKR_yWUqUjjemHXMbA6bafWSNdYlU"
    : "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqcWZkdGZ4b3puamVjZXRlZ2RqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQwODA2MjgsImV4cCI6MjA2OTY1NjYyOH0.uZmcMU35SkYxlHRKR_yWUqUjjemHXMbA6bafWSNdYlU",

  // Feature flags
  ENABLE_BIOMETRIC_AUTH: true,
  ENABLE_OFFLINE_SYNC: true,
  ENABLE_PUSH_NOTIFICATIONS: true,

  // Debug settings
  DEBUG_API_CALLS: isDev,
  DEBUG_AUTH: isDev,
} as const;

// Validate required environment variables
export const validateEnvironment = (): void => {
  const requiredVars = [
    "API_BASE_URL",
    "SUPABASE_URL",
    "SUPABASE_ANON_KEY",
  ] as const;

  const missing = requiredVars.filter((key) => !ENV[key]);

  if (missing.length > 0) {
    console.error(
      `Missing required environment variables: ${missing.join(", ")}`,
    );
    // In development, we can continue with defaults
    if (!isDev) {
      throw new Error(
        `Missing required environment variables: ${missing.join(", ")}`,
      );
    }
  }
};
