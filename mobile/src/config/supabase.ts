import { createClient } from '@supabase/supabase-js'
import { Platform } from 'react-native'
import * as SecureStore from 'expo-secure-store'
import { ENV } from './env'

// Check if we're running on web or native
const isWeb = Platform.OS === 'web'

// Custom storage implementation with fallback for web
const createStorageAdapter = () => {
  if (isWeb) {
    // Use localStorage for web
    return {
      getItem: async (key: string) => {
        try {
          return localStorage.getItem(key)
        } catch (error) {
          console.error('Error getting item from localStorage:', error)
          return null
        }
      },
      setItem: async (key: string, value: string) => {
        try {
          localStorage.setItem(key, value)
        } catch (error) {
          console.error('Error setting item in localStorage:', error)
        }
      },
      removeItem: async (key: string) => {
        try {
          localStorage.removeItem(key)
        } catch (error) {
          console.error('Error removing item from localStorage:', error)
        }
      },
    }
  } else {
    // Use SecureStore for native
    return {
      getItem: async (key: string) => {
        try {
          return await SecureStore.getItemAsync(key)
        } catch (error) {
          console.error('Error getting item from SecureStore:', error)
          return null
        }
      },
      setItem: async (key: string, value: string) => {
        try {
          await SecureStore.setItemAsync(key, value)
        } catch (error) {
          console.error('Error setting item in SecureStore:', error)
        }
      },
      removeItem: async (key: string) => {
        try {
          await SecureStore.deleteItemAsync(key)
        } catch (error) {
          console.error('Error removing item from SecureStore:', error)
        }
      },
    }
  }
}

// Create storage adapter based on platform
const storageAdapter = createStorageAdapter()

// Create Supabase client for mobile app
export const supabase = createClient(
  ENV.SUPABASE_URL,
  ENV.SUPABASE_ANON_KEY,
  {
    auth: {
      storage: storageAdapter,
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: false
    }
  }
)

export default supabase
