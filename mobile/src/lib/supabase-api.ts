import { supabase } from '../config/supabase'
import { ENV } from '../config/env'
import { BucketListItem } from '@dreamvault/types'

// Error handling
export class ApiError extends Error {
  constructor(message: string, public code?: string) {
    super(message)
    this.name = 'ApiError'
  }
}

// Helper function to handle Supabase errors
const handleSupabaseError = (error: any): never => {
  console.error('Supabase error:', error)
  throw new ApiError(error.message || 'An unexpected error occurred', error.code)
}

// Bucket List API using direct Supabase calls
export const bucketListApi = {
  // Get all bucket list items for the current user
  getAll: async (): Promise<{ items: BucketListItem[] }> => {
    try {
      const { data, error } = await supabase
        .from('bucket_list_items')
        .select(`
          *,
          media_files (
            id,
            file_url,
            file_type,
            file_size,
            caption,
            uploaded_at
          )
        `)
        .order('created_at', { ascending: false })

      if (error) {
        handleSupabaseError(error)
      }

      // Transform the data to match the expected interface
      const items: BucketListItem[] = (data || []).map(item => ({
        id: item.id,
        userId: item.user_id,
        title: item.title,
        description: item.description || '',
        category: item.category,
        priority: item.priority,
        status: item.status,
        targetDate: item.target_date ? new Date(item.target_date) : undefined,
        location: item.location || '',
        estimatedCost: item.estimated_cost || 0,
        notes: item.notes || '',
        isPublic: item.is_public || false,
        tags: item.tags || [],
        media: (item.media_files || []).map((file: any) => ({
          id: file.id,
          fileUrl: file.file_url,
          fileType: file.file_type,
          fileSize: file.file_size,
          caption: file.caption,
          uploadedAt: new Date(file.uploaded_at),
        })),
        progress: {
          percentage: 0, // Will be calculated based on milestones
          milestones: [],
          photos: [],
          notes: [],
          analytics: {
            streakDays: 0,
            completionTrend: 'stable' as const,
          },
        },
        createdAt: new Date(item.created_at),
        updatedAt: new Date(item.updated_at),
      }))

      return { items }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      throw new ApiError('Failed to fetch bucket list items')
    }
  },

  // Get a single bucket list item
  getById: async (id: string): Promise<BucketListItem> => {
    try {
      const { data, error } = await supabase
        .from('bucket_list_items')
        .select(`
          *,
          media_files (
            id,
            file_url,
            file_type,
            file_size,
            caption,
            uploaded_at
          )
        `)
        .eq('id', id)
        .single()

      if (error) {
        handleSupabaseError(error)
      }

      if (!data) {
        throw new ApiError('Bucket list item not found')
      }

      // Transform the data to match the expected interface
      const item: BucketListItem = {
        id: data.id,
        userId: data.user_id,
        title: data.title,
        description: data.description || '',
        category: data.category,
        priority: data.priority,
        status: data.status,
        targetDate: data.target_date ? new Date(data.target_date) : undefined,
        location: data.location || '',
        estimatedCost: data.estimated_cost || 0,
        notes: data.notes || '',
        isPublic: data.is_public || false,
        tags: data.tags || [],
        media: (data.media_files || []).map((file: any) => ({
          id: file.id,
          fileUrl: file.file_url,
          fileType: file.file_type,
          fileSize: file.file_size,
          caption: file.caption,
          uploadedAt: new Date(file.uploaded_at),
        })),
        progress: {
          percentage: 0,
          milestones: [],
          photos: [],
          notes: [],
          analytics: {
            streakDays: 0,
            completionTrend: 'stable' as const,
          },
        },
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      }

      return item
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      throw new ApiError('Failed to fetch bucket list item')
    }
  },

  // Create a new bucket list item
  create: async (itemData: Omit<BucketListItem, 'id' | 'userId' | 'media' | 'progress' | 'createdAt' | 'updatedAt'>): Promise<BucketListItem> => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        throw new ApiError('User not authenticated')
      }

      const { data, error } = await supabase
        .from('bucket_list_items')
        .insert({
          user_id: user.id,
          title: itemData.title,
          description: itemData.description,
          category: itemData.category,
          priority: itemData.priority,
          status: itemData.status || 'PLAYING',
          target_date: itemData.targetDate?.toISOString(),
          location: itemData.location,
          estimated_cost: itemData.estimatedCost,
          notes: itemData.notes,
          is_public: itemData.isPublic,
          tags: itemData.tags,
        })
        .select()
        .single()

      if (error) {
        handleSupabaseError(error)
      }

      if (!data) {
        throw new ApiError('Failed to create bucket list item')
      }

      // Transform the response to match the expected interface
      const item: BucketListItem = {
        id: data.id,
        userId: data.user_id,
        title: data.title,
        description: data.description || '',
        category: data.category,
        priority: data.priority,
        status: data.status,
        targetDate: data.target_date ? new Date(data.target_date) : undefined,
        location: data.location || '',
        estimatedCost: data.estimated_cost || 0,
        notes: data.notes || '',
        isPublic: data.is_public || false,
        tags: data.tags || [],
        media: [],
        progress: {
          percentage: 0,
          milestones: [],
          photos: [],
          notes: [],
          analytics: {
            streakDays: 0,
            completionTrend: 'stable' as const,
          },
        },
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      }

      return item
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      throw new ApiError('Failed to create bucket list item')
    }
  },

  // Update a bucket list item
  update: async (id: string, updates: Partial<BucketListItem>): Promise<BucketListItem> => {
    try {
      const updateData: any = {}
      
      if (updates.title !== undefined) updateData.title = updates.title
      if (updates.description !== undefined) updateData.description = updates.description
      if (updates.category !== undefined) updateData.category = updates.category
      if (updates.priority !== undefined) updateData.priority = updates.priority
      if (updates.status !== undefined) updateData.status = updates.status
      if (updates.targetDate !== undefined) updateData.target_date = updates.targetDate?.toISOString()
      if (updates.location !== undefined) updateData.location = updates.location
      if (updates.estimatedCost !== undefined) updateData.estimated_cost = updates.estimatedCost
      if (updates.notes !== undefined) updateData.notes = updates.notes
      if (updates.isPublic !== undefined) updateData.is_public = updates.isPublic
      if (updates.tags !== undefined) updateData.tags = updates.tags

      updateData.updated_at = new Date().toISOString()

      const { data, error } = await supabase
        .from('bucket_list_items')
        .update(updateData)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        handleSupabaseError(error)
      }

      if (!data) {
        throw new ApiError('Bucket list item not found')
      }

      // Get the updated item with media files
      return await bucketListApi.getById(id)
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      throw new ApiError('Failed to update bucket list item')
    }
  },

  // Delete a bucket list item
  delete: async (id: string): Promise<void> => {
    try {
      const { error } = await supabase
        .from('bucket_list_items')
        .delete()
        .eq('id', id)

      if (error) {
        handleSupabaseError(error)
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      throw new ApiError('Failed to delete bucket list item')
    }
  },

  // Get statistics for the current user
  getStats: async (): Promise<{
    total: number;
    inProgress: number;
    completed: number;
    planning: number;
  }> => {
    try {
      const { data, error } = await supabase
        .from('bucket_list_items')
        .select('status')

      if (error) {
        handleSupabaseError(error)
      }

      const items = data || []
      const stats = {
        total: items.length,
        inProgress: items.filter(item => item.status === 'IN_PROGRESS').length,
        completed: items.filter(item => item.status === 'COMPLETED').length,
        planning: items.filter(item => item.status === 'PLAYING').length,
      }

      return stats
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      throw new ApiError('Failed to fetch statistics')
    }
  },

  // Get recent items (for home screen)
  getRecent: async (limit: number = 5): Promise<{ items: BucketListItem[] }> => {
    try {
      const { data, error } = await supabase
        .from('bucket_list_items')
        .select(`
          *,
          media_files (
            id,
            file_url,
            file_type,
            file_size,
            caption,
            uploaded_at
          )
        `)
        .order('updated_at', { ascending: false })
        .limit(limit)

      if (error) {
        handleSupabaseError(error)
      }

      // Transform the data to match the expected interface
      const items: BucketListItem[] = (data || []).map(item => ({
        id: item.id,
        userId: item.user_id,
        title: item.title,
        description: item.description || '',
        category: item.category,
        priority: item.priority,
        status: item.status,
        targetDate: item.target_date ? new Date(item.target_date) : undefined,
        location: item.location || '',
        estimatedCost: item.estimated_cost || 0,
        notes: item.notes || '',
        isPublic: item.is_public || false,
        tags: item.tags || [],
        media: (item.media_files || []).map((file: any) => ({
          id: file.id,
          fileUrl: file.file_url,
          fileType: file.file_type,
          fileSize: file.file_size,
          caption: file.caption,
          uploadedAt: new Date(file.uploaded_at),
        })),
        progress: {
          percentage: 0,
          milestones: [],
          photos: [],
          notes: [],
          analytics: {
            streakDays: 0,
            completionTrend: 'stable' as const,
          },
        },
        createdAt: new Date(item.created_at),
        updatedAt: new Date(item.updated_at),
      }))

      return { items }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      throw new ApiError('Failed to fetch recent items')
    }
  },

  // Search items (client-side filtering for now)
  search: async (query: string, filters?: {
    category?: string;
    status?: string;
    priority?: string;
  }): Promise<{ items: BucketListItem[]; pagination?: any }> => {
    try {
      // Get all items first
      const { items } = await bucketListApi.getAll()

      // Filter by search query
      let filteredItems = items.filter(item =>
        item.title.toLowerCase().includes(query.toLowerCase()) ||
        item.description.toLowerCase().includes(query.toLowerCase()) ||
        (item.tags && item.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase())))
      )

      // Apply additional filters
      if (filters?.category && filters.category !== 'all') {
        filteredItems = filteredItems.filter(item => item.category === filters.category)
      }
      if (filters?.status && filters.status !== 'all') {
        filteredItems = filteredItems.filter(item => item.status === filters.status)
      }
      if (filters?.priority && filters.priority !== 'all') {
        filteredItems = filteredItems.filter(item => item.priority === filters.priority)
      }

      return {
        items: filteredItems,
        pagination: { total: filteredItems.length, page: 1, limit: filteredItems.length }
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      throw new ApiError('Failed to search items')
    }
  },
}

// AI API - These still use the backend for AI services
export const aiApi = {
  // Get smart suggestions for a goal
  getSmartSuggestions: async (title: string, description?: string) => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        throw new ApiError('User not authenticated')
      }

      const response = await fetch(`${ENV.API_BASE_URL}/ai/smart-suggestions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({ title, description }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new ApiError(errorData.message || 'Failed to get AI suggestions')
      }

      return await response.json()
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      throw new ApiError('Failed to get AI suggestions')
    }
  },

  // Get category suggestions
  suggestCategories: async (title: string, description?: string) => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        throw new ApiError('User not authenticated')
      }

      const response = await fetch(`${ENV.API_BASE_URL}/ai/suggest-categories`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({ title, description }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new ApiError(errorData.message || 'Failed to get category suggestions')
      }

      return await response.json()
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      throw new ApiError('Failed to get category suggestions')
    }
  },
}
