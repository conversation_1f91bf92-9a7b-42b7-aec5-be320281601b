import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RouteProp } from "@react-navigation/native";
import { RootStackParamList } from "../navigation/AppNavigator";
import {
  BucketListItem,
  ItemPriority,
  ItemStatus,
  ItemCategory,
} from "@dreamvault/types";
import { MaterialIcons as Icon } from "@expo/vector-icons";

type EditItemScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  "EditItem"
>;
type EditItemScreenRouteProp = RouteProp<RootStackParamList, "EditItem">;

interface Props {
  navigation: EditItemScreenNavigationProp;
  route: EditItemScreenRouteProp;
}

const CATEGORIES = [
  { id: "travel", label: "Travel", icon: "flight" },
  { id: "career", label: "Career", icon: "work" },
  { id: "personal_growth", label: "Personal Growth", icon: "self-improvement" },
  { id: "relationships", label: "Relationships", icon: "favorite" },
  { id: "adventures", label: "Adventures", icon: "explore" },
  { id: "learning", label: "Learning", icon: "school" },
  { id: "fitness", label: "Fitness", icon: "fitness-center" },
  { id: "creative", label: "Creative", icon: "palette" },
  { id: "food", label: "Food", icon: "restaurant" },
];

const PRIORITIES: { id: ItemPriority; label: string; color: string }[] = [
  { id: ItemPriority.MUST_DO, label: "Must Do", color: "#FF3B30" },
  { id: ItemPriority.WANT_TO_DO, label: "Want To Do", color: "#007AFF" },
  { id: ItemPriority.SOMEDAY, label: "Someday", color: "#8E8E93" },
];

const STATUSES: { id: ItemStatus; label: string; color: string }[] = [
  { id: ItemStatus.NOT_STARTED, label: "Not Started", color: "#666" },
  { id: ItemStatus.IN_PROGRESS, label: "In Progress", color: "#007AFF" },
  { id: ItemStatus.COMPLETED, label: "Completed", color: "#34C759" },
  { id: ItemStatus.ON_HOLD, label: "On Hold", color: "#FF9500" },
];

export default function EditItemScreen({ navigation, route }: Props) {
  const { itemId } = route.params;
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Form state
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [category, setCategory] = useState("");
  const [priority, setPriority] = useState<ItemPriority>(
    ItemPriority.WANT_TO_DO,
  );
  const [status, setStatus] = useState<ItemStatus>(ItemStatus.NOT_STARTED);
  const [tags, setTags] = useState("");
  const [targetDate, setTargetDate] = useState("");
  const [estimatedCost, setEstimatedCost] = useState("");
  const [location, setLocation] = useState("");

  const loadItem = useCallback(async () => {
    try {
      // TODO: Implement actual API call
      // Simulate loading data
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Mock item data
      const mockItem: BucketListItem = {
        id: itemId,
        userId: "user1",
        title: "Visit Tokyo, Japan",
        description:
          "Experience the incredible culture, amazing food, and beautiful cherry blossoms.",
        category: ItemCategory.TRAVEL,
        priority: ItemPriority.MUST_DO,
        status: ItemStatus.IN_PROGRESS,
        tags: ["travel", "culture", "asia", "food"],
        targetDate: new Date("2024-04-01"),
        estimatedCost: 3000,
        location: { name: "Tokyo, Japan" },
        media: [],
        progress: {
          percentage: 60,
          milestones: [],
          photos: [],
          notes: [],
          analytics: {
            streakDays: 0,
            completionTrend: "stable" as const,
          },
        },
        createdAt: new Date("2024-01-15"),
        updatedAt: new Date(),
      };

      // Populate form with existing data
      setTitle(mockItem.title);
      setDescription(mockItem.description || "");
      setCategory(mockItem.category);
      setPriority(mockItem.priority);
      setStatus(mockItem.status);
      setTags(mockItem.tags.join(", "));
      setTargetDate(
        mockItem.targetDate
          ? mockItem.targetDate.toISOString().split("T")[0]
          : "",
      );
      setEstimatedCost(
        mockItem.estimatedCost ? mockItem.estimatedCost.toString() : "",
      );
      setLocation(mockItem.location?.name || "");
    } catch (error) {
      console.error("Failed to load item:", error);
      Alert.alert("Error", "Failed to load item details");
    } finally {
      setLoading(false);
    }
  }, [itemId]);

  useEffect(() => {
    loadItem();
  }, [loadItem]);

  const handleSave = async () => {
    if (!title.trim()) {
      Alert.alert("Error", "Please enter a title for your dream");
      return;
    }

    if (!category) {
      Alert.alert("Error", "Please select a category");
      return;
    }

    setSaving(true);
    try {
      // TODO: Implement actual API call
      const updatedItem = {
        id: itemId,
        title: title.trim(),
        description: description.trim(),
        category,
        priority,
        status,
        tags: tags
          .split(",")
          .map((tag) => tag.trim())
          .filter((tag) => tag.length > 0),
        targetDate: targetDate ? new Date(targetDate) : undefined,
        estimatedCost: estimatedCost ? parseFloat(estimatedCost) : undefined,
        location: location.trim() || undefined,
      };

      console.log("Updating item:", updatedItem);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      Alert.alert("Success", "Your dream has been updated!", [
        { text: "OK", onPress: () => navigation.goBack() },
      ]);
    } catch (error) {
      console.error("Failed to update item:", error);
      Alert.alert("Error", "Failed to update your dream. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    Alert.alert(
      "Discard Changes",
      "Are you sure you want to discard your changes?",
      [
        { text: "Keep Editing", style: "cancel" },
        {
          text: "Discard",
          style: "destructive",
          onPress: () => navigation.goBack(),
        },
      ],
    );
  };

  const renderCategorySelector = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Category *</Text>
      <View style={styles.categoriesGrid}>
        {CATEGORIES.map((cat) => (
          <TouchableOpacity
            key={cat.id}
            style={[
              styles.categoryItem,
              category === cat.id && styles.categoryItemSelected,
            ]}
            onPress={() => setCategory(cat.id)}
          >
            <Icon
              name={cat.icon as any}
              size={24}
              color={category === cat.id ? "#007AFF" : "#666"}
            />
            <Text
              style={[
                styles.categoryText,
                category === cat.id && styles.categoryTextSelected,
              ]}
            >
              {cat.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderPrioritySelector = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Priority</Text>
      <View style={styles.priorityContainer}>
        {PRIORITIES.map((p) => (
          <TouchableOpacity
            key={p.id}
            style={[
              styles.priorityItem,
              priority === p.id && styles.priorityItemSelected,
              priority === p.id && { borderColor: p.color },
            ]}
            onPress={() => setPriority(p.id)}
          >
            <View style={[styles.priorityDot, { backgroundColor: p.color }]} />
            <Text
              style={[
                styles.priorityText,
                priority === p.id && styles.priorityTextSelected,
              ]}
            >
              {p.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderStatusSelector = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Status</Text>
      <View style={styles.statusContainer}>
        {STATUSES.map((s) => (
          <TouchableOpacity
            key={s.id}
            style={[
              styles.statusItem,
              status === s.id && styles.statusItemSelected,
              status === s.id && { borderColor: s.color },
            ]}
            onPress={() => setStatus(s.id)}
          >
            <View style={[styles.statusDot, { backgroundColor: s.color }]} />
            <Text
              style={[
                styles.statusText,
                status === s.id && styles.statusTextSelected,
              ]}
            >
              {s.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={handleCancel}>
            <Text style={styles.cancelButton}>Cancel</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Dream</Text>
          <TouchableOpacity onPress={handleSave} disabled={saving}>
            <Text
              style={[styles.saveButton, saving && styles.saveButtonDisabled]}
            >
              {saving ? "Saving..." : "Save"}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          {/* Title */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Title *</Text>
            <TextInput
              style={styles.titleInput}
              placeholder="What's your dream?"
              value={title}
              onChangeText={setTitle}
              maxLength={100}
            />
          </View>

          {/* Description */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Description</Text>
            <TextInput
              style={styles.descriptionInput}
              placeholder="Tell us more about this dream..."
              value={description}
              onChangeText={setDescription}
              multiline
              numberOfLines={4}
              maxLength={500}
              textAlignVertical="top"
            />
          </View>

          {renderCategorySelector()}
          {renderPrioritySelector()}
          {renderStatusSelector()}

          {/* Tags */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Tags</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter tags separated by commas"
              value={tags}
              onChangeText={setTags}
              autoCapitalize="none"
            />
            <Text style={styles.helperText}>
              e.g., adventure, bucket list, travel
            </Text>
          </View>

          {/* Optional Details */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Optional Details</Text>

            <View style={styles.inputGroup}>
              <Icon
                name="event"
                size={20}
                color="#666"
                style={styles.inputIcon}
              />
              <TextInput
                style={styles.inputWithIcon}
                placeholder="Target date (YYYY-MM-DD)"
                value={targetDate}
                onChangeText={setTargetDate}
              />
            </View>

            <View style={styles.inputGroup}>
              <Icon
                name="attach-money"
                size={20}
                color="#666"
                style={styles.inputIcon}
              />
              <TextInput
                style={styles.inputWithIcon}
                placeholder="Estimated cost"
                value={estimatedCost}
                onChangeText={setEstimatedCost}
                keyboardType="numeric"
              />
            </View>

            <View style={styles.inputGroup}>
              <Icon
                name="location-on"
                size={20}
                color="#666"
                style={styles.inputIcon}
              />
              <TextInput
                style={styles.inputWithIcon}
                placeholder="Location"
                value={location}
                onChangeText={setLocation}
              />
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  keyboardView: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e1e5e9",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
  },
  cancelButton: {
    fontSize: 16,
    color: "#666",
  },
  saveButton: {
    fontSize: 16,
    color: "#007AFF",
    fontWeight: "600",
  },
  saveButtonDisabled: {
    color: "#ccc",
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: "#fff",
    marginBottom: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 12,
  },
  titleInput: {
    fontSize: 18,
    fontWeight: "500",
    color: "#1a1a1a",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#e1e5e9",
  },
  descriptionInput: {
    fontSize: 16,
    color: "#1a1a1a",
    borderWidth: 1,
    borderColor: "#e1e5e9",
    borderRadius: 8,
    padding: 12,
    minHeight: 100,
  },
  input: {
    fontSize: 16,
    color: "#1a1a1a",
    borderWidth: 1,
    borderColor: "#e1e5e9",
    borderRadius: 8,
    padding: 12,
  },
  helperText: {
    fontSize: 12,
    color: "#666",
    marginTop: 4,
  },
  categoriesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginHorizontal: -4,
  },
  categoryItem: {
    width: "33.33%",
    alignItems: "center",
    paddingVertical: 16,
    paddingHorizontal: 4,
    borderRadius: 8,
    marginBottom: 8,
  },
  categoryItemSelected: {
    backgroundColor: "#f0f8ff",
  },
  categoryText: {
    fontSize: 12,
    color: "#666",
    marginTop: 4,
    textAlign: "center",
  },
  categoryTextSelected: {
    color: "#007AFF",
    fontWeight: "500",
  },
  priorityContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  priorityItem: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderWidth: 1,
    borderColor: "#e1e5e9",
    borderRadius: 8,
    marginHorizontal: 4,
  },
  priorityItemSelected: {
    backgroundColor: "#f8f9fa",
    borderWidth: 2,
  },
  priorityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  priorityText: {
    fontSize: 14,
    color: "#666",
  },
  priorityTextSelected: {
    color: "#1a1a1a",
    fontWeight: "500",
  },
  statusContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginHorizontal: -4,
  },
  statusItem: {
    width: "50%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderWidth: 1,
    borderColor: "#e1e5e9",
    borderRadius: 8,
    marginHorizontal: 4,
    marginBottom: 8,
  },
  statusItemSelected: {
    backgroundColor: "#f8f9fa",
    borderWidth: 2,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    color: "#666",
  },
  statusTextSelected: {
    color: "#1a1a1a",
    fontWeight: "500",
  },
  inputGroup: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#e1e5e9",
    borderRadius: 8,
    marginBottom: 12,
  },
  inputIcon: {
    marginLeft: 12,
    marginRight: 8,
  },
  inputWithIcon: {
    flex: 1,
    fontSize: 16,
    color: "#1a1a1a",
    paddingVertical: 12,
    paddingRight: 12,
  },
});
