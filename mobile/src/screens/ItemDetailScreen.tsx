import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  Share,
} from "react-native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RouteProp } from "@react-navigation/native";
import { RootStackParamList } from "../navigation/AppNavigator";
import { BucketListItem, ItemStatus, ItemPriority } from "@dreamvault/types";
import { MaterialIcons as Icon } from "@expo/vector-icons";
import { bucketListApi } from "../lib/supabase-api";

type ItemDetailScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  "ItemDetail"
>;
type ItemDetailScreenRouteProp = RouteProp<RootStackParamList, "ItemDetail">;

interface Props {
  navigation: ItemDetailScreenNavigationProp;
  route: ItemDetailScreenRouteProp;
}

const STATUS_COLORS = {
  [ItemStatus.NOT_STARTED]: "#666",
  [ItemStatus.IN_PROGRESS]: "#007AFF",
  [ItemStatus.COMPLETED]: "#34C759",
  [ItemStatus.ON_HOLD]: "#FF9500",
};

const PRIORITY_COLORS = {
  [ItemPriority.MUST_DO]: "#FF3B30",
  [ItemPriority.WANT_TO_DO]: "#007AFF",
  [ItemPriority.SOMEDAY]: "#8E8E93",
};

export default function ItemDetailScreen({ navigation, route }: Props) {
  const { itemId } = route.params;
  const [item, setItem] = useState<BucketListItem | null>(null);
  const [loading, setLoading] = useState(true);

  const loadItem = useCallback(async () => {
    try {
      setLoading(true);

      // Fetch item from API
      const item = await bucketListApi.getById(itemId);
      setItem(item);
    } catch (error) {
      console.error("Failed to load item:", error);
      Alert.alert(
        "Error",
        "Failed to load item details. Please check your connection and try again.",
      );
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  }, [itemId]);

  useEffect(() => {
    loadItem();
  }, [loadItem]);

  const handleEdit = () => {
    if (item) {
      navigation.navigate("EditItem", { itemId: item.id });
    }
  };

  const handleShare = async () => {
    if (!item) {
      return;
    }

    try {
      await Share.share({
        message: `Check out my bucket list item: ${item.title}\n\n${item.description}`,
        title: item.title,
      });
    } catch (error) {
      console.error("Failed to share:", error);
    }
  };

  const handleStatusChange = (newStatus: ItemStatus) => {
    if (!item) {
      return;
    }

    Alert.alert(
      "Update Status",
      `Change status to "${newStatus.replace("_", " ")}"?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Update",
          onPress: async () => {
            try {
              // Update item status via API
              const updatedItem = await bucketListApi.update(itemId, {
                status: newStatus,
              });

              setItem(updatedItem);

              if (newStatus === ItemStatus.COMPLETED) {
                Alert.alert(
                  "Congratulations!",
                  "You completed your dream! 🎉",
                );
              }
            } catch (error) {
              console.error("Failed to update status:", error);
              Alert.alert(
                "Error",
                "Failed to update status. Please check your connection and try again.",
              );
            }
          },
        },
      ],
    );
  };

  const handleDelete = () => {
    Alert.alert(
      "Delete Dream",
      "Are you sure you want to delete this dream? This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              // Delete item via API
              const response = await bucketListApi.deleteItem(itemId);

              if (response.success) {
                navigation.goBack();
                Alert.alert("Deleted", "Your dream has been deleted");
              } else {
                console.error("Failed to delete item:", response.error);
                Alert.alert(
                  "Error",
                  response.error || "Failed to delete dream",
                );
              }
            } catch (error) {
              console.error("Failed to delete item:", error);
              Alert.alert(
                "Error",
                "Failed to delete dream. Please check your connection and try again.",
              );
            }
          },
        },
      ],
    );
  };

  const getStatusIcon = (status: ItemStatus) => {
    switch (status) {
      case ItemStatus.NOT_STARTED:
        return "radio-button-unchecked";
      case ItemStatus.IN_PROGRESS:
        return "schedule";
      case ItemStatus.COMPLETED:
        return "check-circle";
      case ItemStatus.ON_HOLD:
        return "pause-circle-filled";
      default:
        return "help";
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "must_do":
        return "priority-high";
      case "want_to_do":
        return "bookmark";
      case "someday":
        return "schedule";
      default:
        return "help";
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!item) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text>Item not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color="#1a1a1a" />
        </TouchableOpacity>
        <View style={styles.headerActions}>
          <TouchableOpacity onPress={handleShare} style={styles.headerButton}>
            <Icon name="share" size={24} color="#007AFF" />
          </TouchableOpacity>
          <TouchableOpacity onPress={handleEdit} style={styles.headerButton}>
            <Icon name="edit" size={24} color="#007AFF" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Main Info */}
        <View style={styles.mainCard}>
          <View style={styles.titleRow}>
            <Text style={styles.title}>{item.title}</Text>
            <Icon
              name={getPriorityIcon(item.priority)}
              size={24}
              color={PRIORITY_COLORS[item.priority]}
            />
          </View>

          <View style={styles.statusRow}>
            <Icon
              name={getStatusIcon(item.status)}
              size={20}
              color={STATUS_COLORS[item.status]}
            />
            <Text
              style={[styles.statusText, { color: STATUS_COLORS[item.status] }]}
            >
              {item.status.replace("_", " ").toUpperCase()}
            </Text>
          </View>

          {item.description && (
            <Text style={styles.description}>{item.description}</Text>
          )}

          {/* Progress */}
          {item.progress.percentage > 0 && (
            <View style={styles.progressSection}>
              <View style={styles.progressHeader}>
                <Text style={styles.progressLabel}>Progress</Text>
                <Text style={styles.progressPercentage}>
                  {item.progress.percentage}%
                </Text>
              </View>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    { width: `${item.progress.percentage}%` },
                  ]}
                />
              </View>
            </View>
          )}

          {/* Tags */}
          {item.tags.length > 0 && (
            <View style={styles.tagsSection}>
              <Text style={styles.sectionTitle}>Tags</Text>
              <View style={styles.tagsContainer}>
                {item.tags.map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>#{tag}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}
        </View>

        {/* Details */}
        <View style={styles.detailsCard}>
          <Text style={styles.cardTitle}>Details</Text>

          <View style={styles.detailRow}>
            <Icon name="category" size={20} color="#666" />
            <Text style={styles.detailLabel}>Category</Text>
            <Text style={styles.detailValue}>
              {item.category.replace("_", " ").toUpperCase()}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Icon name="flag" size={20} color="#666" />
            <Text style={styles.detailLabel}>Priority</Text>
            <Text
              style={[
                styles.detailValue,
                { color: PRIORITY_COLORS[item.priority] },
              ]}
            >
              {item.priority.replace("_", " ").toUpperCase()}
            </Text>
          </View>

          {item.targetDate && (
            <View style={styles.detailRow}>
              <Icon name="event" size={20} color="#666" />
              <Text style={styles.detailLabel}>Target Date</Text>
              <Text style={styles.detailValue}>
                {item.targetDate.toLocaleDateString()}
              </Text>
            </View>
          )}

          {item.estimatedCost && (
            <View style={styles.detailRow}>
              <Icon name="attach-money" size={20} color="#666" />
              <Text style={styles.detailLabel}>Estimated Cost</Text>
              <Text style={styles.detailValue}>
                ${item.estimatedCost.toLocaleString()}
              </Text>
            </View>
          )}

          {item.location && (
            <View style={styles.detailRow}>
              <Icon name="location-on" size={20} color="#666" />
              <Text style={styles.detailLabel}>Location</Text>
              <Text style={styles.detailValue}>{item.location.name}</Text>
            </View>
          )}

          <View style={styles.detailRow}>
            <Icon name="schedule" size={20} color="#666" />
            <Text style={styles.detailLabel}>Created</Text>
            <Text style={styles.detailValue}>
              {item.createdAt.toLocaleDateString()}
            </Text>
          </View>
        </View>

        {/* Milestones */}
        {item.progress.milestones.length > 0 && (
          <View style={styles.milestonesCard}>
            <Text style={styles.cardTitle}>Milestones</Text>
            {item.progress.milestones.map((milestone, index) => (
              <View key={index} style={styles.milestoneItem}>
                <Icon name="check-circle" size={16} color="#34C759" />
                <Text style={styles.milestoneText}>{milestone.title}</Text>
              </View>
            ))}
          </View>
        )}

        {/* Notes */}
        {item.progress.notes.length > 0 && (
          <View style={styles.notesCard}>
            <Text style={styles.cardTitle}>Notes</Text>
            {item.progress.notes.map((note, index) => (
              <View key={index} style={styles.noteItem}>
                <Icon name="note" size={16} color="#007AFF" />
                <Text style={styles.noteText}>{note.content}</Text>
              </View>
            ))}
          </View>
        )}

        {/* Actions */}
        <View style={styles.actionsCard}>
          <Text style={styles.cardTitle}>Actions</Text>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleStatusChange(ItemStatus.IN_PROGRESS)}
          >
            <Icon name="play-arrow" size={20} color="#007AFF" />
            <Text style={styles.actionButtonText}>Start Working On This</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleStatusChange(ItemStatus.COMPLETED)}
          >
            <Icon name="check-circle" size={20} color="#34C759" />
            <Text style={styles.actionButtonText}>Mark as Completed</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleStatusChange(ItemStatus.ON_HOLD)}
          >
            <Icon name="pause" size={20} color="#FF9500" />
            <Text style={styles.actionButtonText}>Put on Hold</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.deleteButton]}
            onPress={handleDelete}
          >
            <Icon name="delete" size={20} color="#FF3B30" />
            <Text style={[styles.actionButtonText, styles.deleteButtonText]}>
              Delete Dream
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e1e5e9",
  },
  headerActions: {
    flexDirection: "row",
  },
  headerButton: {
    marginLeft: 16,
  },
  scrollView: {
    flex: 1,
  },
  mainCard: {
    backgroundColor: "#fff",
    margin: 20,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: "#e1e5e9",
  },
  titleRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1a1a1a",
    flex: 1,
    marginRight: 12,
  },
  statusRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  statusText: {
    fontSize: 14,
    fontWeight: "600",
    marginLeft: 8,
  },
  description: {
    fontSize: 16,
    color: "#666",
    lineHeight: 24,
    marginBottom: 20,
  },
  progressSection: {
    marginBottom: 20,
  },
  progressHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
  },
  progressPercentage: {
    fontSize: 16,
    fontWeight: "600",
    color: "#007AFF",
  },
  progressBar: {
    height: 8,
    backgroundColor: "#e1e5e9",
    borderRadius: 4,
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#007AFF",
    borderRadius: 4,
  },
  tagsSection: {
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 8,
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  tag: {
    backgroundColor: "#f0f8ff",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 12,
    color: "#007AFF",
    fontWeight: "500",
  },
  detailsCard: {
    backgroundColor: "#fff",
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: "#e1e5e9",
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  detailLabel: {
    fontSize: 14,
    color: "#666",
    marginLeft: 12,
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    color: "#1a1a1a",
    fontWeight: "500",
  },
  milestonesCard: {
    backgroundColor: "#fff",
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: "#e1e5e9",
  },
  milestoneItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
  },
  milestoneText: {
    fontSize: 14,
    color: "#1a1a1a",
    marginLeft: 12,
  },
  notesCard: {
    backgroundColor: "#fff",
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: "#e1e5e9",
  },
  noteItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    paddingVertical: 8,
  },
  noteText: {
    fontSize: 14,
    color: "#1a1a1a",
    marginLeft: 12,
    flex: 1,
    lineHeight: 20,
  },
  actionsCard: {
    backgroundColor: "#fff",
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: "#e1e5e9",
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: "#f8f9fa",
    marginBottom: 8,
  },
  actionButtonText: {
    fontSize: 16,
    color: "#1a1a1a",
    marginLeft: 12,
    fontWeight: "500",
  },
  deleteButton: {
    backgroundColor: "#fff5f5",
  },
  deleteButtonText: {
    color: "#FF3B30",
  },
});
