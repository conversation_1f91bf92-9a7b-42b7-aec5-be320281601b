import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RouteProp } from "@react-navigation/native";
import { RootStackParamList } from "../navigation/AppNavigator";
import { ItemPriority, ItemStatus, ItemCategory } from "@dreamvault/types";
import { MaterialIcons as Icon } from "@expo/vector-icons";
import { pickPhoto } from "../utils/camera";
import { bucketListApi, aiApi } from "../lib/supabase-api";

type AddItemScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  "AddItem"
>;
type AddItemScreenRouteProp = RouteProp<RootStackParamList, "AddItem">;

interface Props {
  navigation: AddItemScreenNavigationProp;
  route: AddItemScreenRouteProp;
}

const CATEGORIES = [
  { id: ItemCategory.TRAVEL, label: "Travel", icon: "flight" },
  { id: ItemCategory.CAREER, label: "Career", icon: "work" },
  {
    id: ItemCategory.PERSONAL_GROWTH,
    label: "Personal Growth",
    icon: "self-improvement",
  },
  { id: ItemCategory.RELATIONSHIPS, label: "Relationships", icon: "favorite" },
  { id: ItemCategory.ADVENTURES, label: "Adventures", icon: "explore" },
  { id: ItemCategory.LEARNING, label: "Learning", icon: "school" },
];

const PRIORITIES: { id: ItemPriority; label: string; color: string }[] = [
  { id: ItemPriority.MUST_DO, label: "Must Do", color: "#FF3B30" },
  { id: ItemPriority.WANT_TO_DO, label: "Want To Do", color: "#007AFF" },
  { id: ItemPriority.SOMEDAY, label: "Someday", color: "#8E8E93" },
];

export default function AddItemScreen({ navigation }: Props) {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [category, setCategory] = useState<ItemCategory | "">("");
  const [priority, setPriority] = useState<ItemPriority>(
    ItemPriority.WANT_TO_DO,
  );
  const [tags, setTags] = useState("");
  const [targetDate, setTargetDate] = useState("");
  const [estimatedCost, setEstimatedCost] = useState("");
  const [location, setLocation] = useState("");
  const [loading, setLoading] = useState(false);
  const [selectedPhotos, setSelectedPhotos] = useState<
    Array<{
      uri: string;
      type: string;
      name: string;
      size?: number;
    }>
  >([]);
  const [aiSuggestions, setAiSuggestions] = useState<{
    categories: Array<{
      category: string;
      confidence: number;
      reasoning: string;
    }>;
    tags: Array<{ tag: string; confidence: number; category: string }>;
    locations: Array<{ name: string; confidence: number; type: string }>;
  } | null>(null);
  const [loadingAI, setLoadingAI] = useState(false);

  const handleSave = async () => {
    if (!title.trim()) {
      Alert.alert("Error", "Please enter a title for your dream");
      return;
    }

    if (category === "") {
      Alert.alert("Error", "Please select a category");
      return;
    }

    setLoading(true);
    try {
      const newItem = {
        title: title.trim(),
        description: description.trim() || undefined,
        category,
        priority,
        status: ItemStatus.NOT_STARTED,
        tags: tags
          .split(",")
          .map((tag) => tag.trim())
          .filter((tag) => tag.length > 0),
        targetDate: targetDate ? new Date(targetDate) : undefined,
        estimatedCost: estimatedCost ? parseFloat(estimatedCost) : undefined,
        location: location.trim() ? { name: location.trim() } : undefined,
      };

      console.log("Creating new item:", newItem);

      // Create item using API
      const createdItem = await bucketListApi.create(newItem);

      // TODO: Implement media upload with Supabase Storage
      if (selectedPhotos.length > 0) {
        console.warn("Media upload not yet implemented with Supabase");
        // Will implement this later with Supabase Storage
      }

      Alert.alert("Success", "Your dream has been added!", [
        { text: "OK", onPress: () => navigation.goBack() },
      ]);
    } catch (error) {
      console.error("Failed to create item:", error);
      Alert.alert(
        "Error",
        "Failed to add your dream. Please check your connection and try again.",
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    if (title.trim() || description.trim()) {
      Alert.alert(
        "Discard Changes",
        "Are you sure you want to discard your changes?",
        [
          { text: "Keep Editing", style: "cancel" },
          {
            text: "Discard",
            style: "destructive",
            onPress: () => navigation.goBack(),
          },
        ],
      );
    } else {
      navigation.goBack();
    }
  };

  const handleAddPhoto = async () => {
    try {
      const result = await pickPhoto({
        quality: 0.8,
        maxWidth: 1000,
        maxHeight: 1000,
      });

      if (result) {
        // Add photo to selected photos array
        const newPhoto = {
          uri: result.uri,
          type: result.type || "image/jpeg",
          name: result.fileName || `photo_${Date.now()}.jpg`,
          size: result.fileSize,
        };

        setSelectedPhotos((prev) => [...prev, newPhoto]);
        console.log("Photo selected:", result);
        Alert.alert(
          "Photo Added",
          "Photo will be uploaded when you save the item.",
        );
      }
    } catch (error) {
      console.error("Failed to add photo:", error);
      Alert.alert("Error", "Failed to add photo. Please try again.");
    }
  };

  const handleGetAISuggestions = async () => {
    if (!title.trim()) {
      Alert.alert("Info", "Please enter a title first to get AI suggestions");
      return;
    }

    setLoadingAI(true);
    try {
      const response = await aiApi.getSmartSuggestions(
        title.trim(),
        description.trim() || undefined,
      );

      if (response.success && response.data) {
        setAiSuggestions({
          categories: response.data.categories,
          tags: response.data.tags,
          locations: response.data.locations.locations,
        });

        // Auto-suggest category if not selected and AI has high confidence
        if (!category && response.data.categories.length > 0) {
          const topCategory = response.data.categories[0];
          if (topCategory.confidence > 0.8) {
            const categoryEnum = Object.values(ItemCategory).find(
              (cat) =>
                cat.toLowerCase().replace("_", " ") ===
                topCategory.category.toLowerCase(),
            );
            if (categoryEnum) {
              setCategory(categoryEnum);
            }
          }
        }

        // Auto-suggest location if not set and AI found one
        if (!location && response.data.locations.locations.length > 0) {
          const topLocation = response.data.locations.locations[0];
          if (topLocation.confidence > 0.7) {
            setLocation(topLocation.name);
          }
        }

        // Auto-suggest tags if not set
        if (!tags && response.data.tags.length > 0) {
          const suggestedTags = response.data.tags
            .filter((tag) => tag.confidence > 0.6)
            .slice(0, 3)
            .map((tag) => tag.tag)
            .join(", ");
          if (suggestedTags) {
            setTags(suggestedTags);
          }
        }

        Alert.alert(
          "AI Suggestions Applied",
          "AI has analyzed your dream and provided suggestions. Review and adjust as needed!",
        );
      } else {
        Alert.alert(
          "Info",
          "Unable to get AI suggestions at this time. Please try again later.",
        );
      }
    } catch (error) {
      console.error("AI suggestions error:", error);
      Alert.alert("Error", "Failed to get AI suggestions. Please try again.");
    } finally {
      setLoadingAI(false);
    }
  };

  const renderCategorySelector = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Category *</Text>
      <View style={styles.categoriesGrid}>
        {CATEGORIES.map((cat) => (
          <TouchableOpacity
            key={cat.id}
            style={[
              styles.categoryItem,
              category === cat.id && styles.categoryItemSelected,
            ]}
            onPress={() => setCategory(cat.id)}
          >
            <Icon
              name={cat.icon as any}
              size={24}
              color={category === cat.id ? "#007AFF" : "#666"}
            />
            <Text
              style={[
                styles.categoryText,
                category === cat.id && styles.categoryTextSelected,
              ]}
            >
              {cat.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderPrioritySelector = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Priority</Text>
      <View style={styles.priorityContainer}>
        {PRIORITIES.map((p) => (
          <TouchableOpacity
            key={p.id}
            style={[
              styles.priorityItem,
              priority === p.id && styles.priorityItemSelected,
              priority === p.id && { borderColor: p.color },
            ]}
            onPress={() => setPriority(p.id)}
          >
            <View style={[styles.priorityDot, { backgroundColor: p.color }]} />
            <Text
              style={[
                styles.priorityText,
                priority === p.id && styles.priorityTextSelected,
              ]}
            >
              {p.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={handleCancel}>
            <Text style={styles.cancelButton}>Cancel</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Add Dream</Text>
          <TouchableOpacity onPress={handleSave} disabled={loading}>
            <Text
              style={[styles.saveButton, loading && styles.saveButtonDisabled]}
            >
              {loading ? "Saving..." : "Save"}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          {/* Title */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Title *</Text>
            <TextInput
              style={styles.titleInput}
              placeholder="What's your dream?"
              value={title}
              onChangeText={setTitle}
              maxLength={100}
              autoFocus
            />
          </View>

          {/* Description */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Description</Text>
            <TextInput
              style={styles.descriptionInput}
              placeholder="Tell us more about this dream..."
              value={description}
              onChangeText={setDescription}
              multiline
              numberOfLines={4}
              maxLength={500}
              textAlignVertical="top"
            />
          </View>

          {/* AI Suggestions Button */}
          <View style={styles.section}>
            <TouchableOpacity
              style={[styles.aiButton, loadingAI && styles.aiButtonDisabled]}
              onPress={handleGetAISuggestions}
              disabled={loadingAI || !title.trim()}
            >
              <Icon name="auto-awesome" size={20} color="#FFFFFF" />
              <Text style={styles.aiButtonText}>
                {loadingAI ? "Getting AI Suggestions..." : "Get AI Suggestions"}
              </Text>
            </TouchableOpacity>
            {aiSuggestions && (
              <Text style={styles.aiHint}>
                AI suggestions have been applied! Review and adjust as needed.
              </Text>
            )}
          </View>

          {renderCategorySelector()}
          {renderPrioritySelector()}

          {/* Tags */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Tags</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter tags separated by commas"
              value={tags}
              onChangeText={setTags}
              autoCapitalize="none"
            />
            <Text style={styles.helperText}>
              e.g., adventure, bucket list, travel
            </Text>
          </View>

          {/* Optional Details */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Optional Details</Text>

            <View style={styles.inputGroup}>
              <Icon
                name="event"
                size={20}
                color="#666"
                style={styles.inputIcon}
              />
              <TextInput
                style={styles.inputWithIcon}
                placeholder="Target date (YYYY-MM-DD)"
                value={targetDate}
                onChangeText={setTargetDate}
              />
            </View>

            <View style={styles.inputGroup}>
              <Icon
                name="attach-money"
                size={20}
                color="#666"
                style={styles.inputIcon}
              />
              <TextInput
                style={styles.inputWithIcon}
                placeholder="Estimated cost"
                value={estimatedCost}
                onChangeText={setEstimatedCost}
                keyboardType="numeric"
              />
            </View>

            <View style={styles.inputGroup}>
              <Icon
                name="location-on"
                size={20}
                color="#666"
                style={styles.inputIcon}
              />
              <TextInput
                style={styles.inputWithIcon}
                placeholder="Location"
                value={location}
                onChangeText={setLocation}
              />
            </View>
          </View>

          {/* Media */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Media</Text>
            <TouchableOpacity
              style={styles.addPhotoButton}
              onPress={handleAddPhoto}
            >
              <Icon name="add-a-photo" size={24} color="#007AFF" />
              <Text style={styles.addPhotoText}>Add Photo</Text>
            </TouchableOpacity>

            {/* Photo Preview */}
            {selectedPhotos.length > 0 && (
              <View style={styles.photoPreview}>
                <Text style={styles.photoPreviewTitle}>
                  Selected Photos ({selectedPhotos.length})
                </Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  {selectedPhotos.map((photo, index) => (
                    <View key={index} style={styles.photoItem}>
                      <Text style={styles.photoName} numberOfLines={1}>
                        {photo.name}
                      </Text>
                      <TouchableOpacity
                        style={styles.removePhotoButton}
                        onPress={() => {
                          setSelectedPhotos((prev) =>
                            prev.filter((_, i) => i !== index),
                          );
                        }}
                      >
                        <Icon name="close" size={16} color="#FF3B30" />
                      </TouchableOpacity>
                    </View>
                  ))}
                </ScrollView>
              </View>
            )}
          </View>

          {/* AI Suggestions */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>AI Suggestions</Text>
            <View style={styles.suggestionCard}>
              <Icon name="lightbulb" size={20} color="#FFD700" />
              <Text style={styles.suggestionText}>
                Based on your dream, we suggest adding tags like "adventure" and
                "outdoor activities"
              </Text>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  keyboardView: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e1e5e9",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
  },
  cancelButton: {
    fontSize: 16,
    color: "#666",
  },
  saveButton: {
    fontSize: 16,
    color: "#007AFF",
    fontWeight: "600",
  },
  saveButtonDisabled: {
    color: "#ccc",
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: "#fff",
    marginBottom: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 12,
  },
  titleInput: {
    fontSize: 18,
    fontWeight: "500",
    color: "#1a1a1a",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#e1e5e9",
  },
  descriptionInput: {
    fontSize: 16,
    color: "#1a1a1a",
    borderWidth: 1,
    borderColor: "#e1e5e9",
    borderRadius: 8,
    padding: 12,
    minHeight: 100,
  },
  input: {
    fontSize: 16,
    color: "#1a1a1a",
    borderWidth: 1,
    borderColor: "#e1e5e9",
    borderRadius: 8,
    padding: 12,
  },
  helperText: {
    fontSize: 12,
    color: "#666",
    marginTop: 4,
  },
  categoriesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginHorizontal: -4,
  },
  categoryItem: {
    width: "33.33%",
    alignItems: "center",
    paddingVertical: 16,
    paddingHorizontal: 4,
    borderRadius: 8,
    marginBottom: 8,
  },
  categoryItemSelected: {
    backgroundColor: "#f0f8ff",
  },
  categoryText: {
    fontSize: 12,
    color: "#666",
    marginTop: 4,
    textAlign: "center",
  },
  categoryTextSelected: {
    color: "#007AFF",
    fontWeight: "500",
  },
  priorityContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  priorityItem: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderWidth: 1,
    borderColor: "#e1e5e9",
    borderRadius: 8,
    marginHorizontal: 4,
  },
  priorityItemSelected: {
    backgroundColor: "#f8f9fa",
    borderWidth: 2,
  },
  priorityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  priorityText: {
    fontSize: 14,
    color: "#666",
  },
  priorityTextSelected: {
    color: "#1a1a1a",
    fontWeight: "500",
  },
  inputGroup: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#e1e5e9",
    borderRadius: 8,
    marginBottom: 12,
  },
  inputIcon: {
    marginLeft: 12,
    marginRight: 8,
  },
  inputWithIcon: {
    flex: 1,
    fontSize: 16,
    color: "#1a1a1a",
    paddingVertical: 12,
    paddingRight: 12,
  },
  addPhotoButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    borderWidth: 2,
    borderColor: "#007AFF",
    borderStyle: "dashed",
    borderRadius: 8,
  },
  addPhotoText: {
    fontSize: 16,
    color: "#007AFF",
    fontWeight: "500",
    marginLeft: 8,
  },
  suggestionCard: {
    flexDirection: "row",
    alignItems: "flex-start",
    backgroundColor: "#fff9e6",
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#ffe066",
  },
  suggestionText: {
    flex: 1,
    fontSize: 14,
    color: "#666",
    marginLeft: 8,
    lineHeight: 20,
  },
  aiButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#007AFF",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  aiButtonDisabled: {
    backgroundColor: "#8E8E93",
  },
  aiButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  aiHint: {
    fontSize: 12,
    color: "#007AFF",
    textAlign: "center",
    fontStyle: "italic",
  },
  photoPreview: {
    marginTop: 12,
    padding: 12,
    backgroundColor: "#F8F9FA",
    borderRadius: 8,
  },
  photoPreviewTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  photoItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    marginRight: 8,
    borderWidth: 1,
    borderColor: "#E5E5EA",
  },
  photoName: {
    fontSize: 12,
    color: "#666",
    maxWidth: 80,
  },
  removePhotoButton: {
    marginLeft: 8,
    padding: 2,
  },
});
