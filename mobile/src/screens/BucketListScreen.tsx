import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  RefreshControl,
  Alert,
  TextInput,
} from "react-native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { useFocusEffect } from "@react-navigation/native";
import { RootStackParamList } from "../navigation/AppNavigator";
import {
  BucketListItem,
  ItemStatus,
  ItemPriority,
  ItemCategory,
} from "@dreamvault/types";
import { MaterialIcons as Icon } from "@expo/vector-icons";
import { bucketListApi } from "../lib/supabase-api";

type BucketListScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  "Main"
>;

interface Props {
  navigation: BucketListScreenNavigationProp;
}

const STATUS_COLORS = {
  [ItemStatus.NOT_STARTED]: "#666",
  [ItemStatus.IN_PROGRESS]: "#007AFF",
  [ItemStatus.COMPLETED]: "#34C759",
  [ItemStatus.ON_HOLD]: "#FF9500",
};

const PRIORITY_COLORS = {
  [ItemPriority.MUST_DO]: "#FF3B30",
  [ItemPriority.WANT_TO_DO]: "#007AFF",
  [ItemPriority.SOMEDAY]: "#8E8E93",
};

export default function BucketListScreen({ navigation }: Props) {
  const [items, setItems] = useState<BucketListItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<BucketListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<ItemStatus | "all">(
    "all",
  );
  const [selectedPriority] = useState<ItemPriority | "all">("all");
  const [selectedCategory] = useState<ItemCategory | "all">("all");
  const [sortBy] = useState<string>("updatedAt");
  const [sortOrder] = useState<"asc" | "desc">("desc");

  const filterItems = useCallback(() => {
    let filtered = items;
    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (item) =>
          item.title.toLowerCase().includes(query) ||
          item.description?.toLowerCase().includes(query) ||
          item.tags.some((tag) => tag.toLowerCase().includes(query)),
      );
    }
    // Filter by status
    if (selectedStatus !== "all") {
      filtered = filtered.filter((item) => item.status === selectedStatus);
    }
    // Filter by priority
    if (selectedPriority !== "all") {
      filtered = filtered.filter((item) => item.priority === selectedPriority);
    }
    setFilteredItems(filtered);
  }, [items, searchQuery, selectedStatus, selectedPriority]);

  useFocusEffect(
    useCallback(() => {
      loadItems();
    }, []),
  );

  useEffect(() => {
    filterItems();
  }, [filterItems]);

  const loadItems = async () => {
    try {
      // Fetch all items from Supabase (filtering will be done client-side for now)
      const response = await bucketListApi.getAll();

      let filteredItems = response.items || [];

      // Apply client-side filters
      if (selectedCategory !== "all") {
        filteredItems = filteredItems.filter(item => item.category === selectedCategory);
      }
      if (selectedStatus !== "all") {
        filteredItems = filteredItems.filter(item => item.status === selectedStatus);
      }
      if (selectedPriority !== "all") {
        filteredItems = filteredItems.filter(item => item.priority === selectedPriority);
      }

      // Apply sorting
      filteredItems.sort((a, b) => {
        let aValue: any, bValue: any;

        switch (sortBy) {
          case 'title':
            aValue = a.title.toLowerCase();
            bValue = b.title.toLowerCase();
            break;
          case 'created_at':
            aValue = a.createdAt;
            bValue = b.createdAt;
            break;
          case 'updated_at':
            aValue = a.updatedAt;
            bValue = b.updatedAt;
            break;
          case 'priority':
            const priorityOrder = { 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
            aValue = priorityOrder[a.priority as keyof typeof priorityOrder] || 0;
            bValue = priorityOrder[b.priority as keyof typeof priorityOrder] || 0;
            break;
          default:
            aValue = a.createdAt;
            bValue = b.createdAt;
        }

        if (sortOrder === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });

      setItems(filteredItems);
    } catch (error) {
      console.error("Failed to load items:", error);
      Alert.alert(
        "Error",
        error instanceof Error
          ? error.message
          : "Failed to load bucket list items. Please check your connection and try again.",
      );
      setItems([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadItems();
  };

  const handleItemPress = (item: BucketListItem) => {
    navigation.navigate("ItemDetail", { itemId: item.id });
  };

  const handleAddItem = () => {
    navigation.navigate("AddItem");
  };

  const getStatusIcon = (status: ItemStatus) => {
    switch (status) {
      case ItemStatus.NOT_STARTED:
        return "radio-button-unchecked";
      case ItemStatus.IN_PROGRESS:
        return "schedule";
      case ItemStatus.COMPLETED:
        return "check-circle";
      case ItemStatus.ON_HOLD:
        return "pause-circle-filled";
      default:
        return "help";
    }
  };

  const getPriorityIcon = (priority: ItemPriority) => {
    switch (priority) {
      case ItemPriority.MUST_DO:
        return "priority-high";
      case ItemPriority.WANT_TO_DO:
        return "bookmark";
      case ItemPriority.SOMEDAY:
        return "schedule";
      default:
        return "help";
    }
  };

  const renderItem = ({ item }: { item: BucketListItem }) => (
    <TouchableOpacity
      style={styles.itemCard}
      onPress={() => handleItemPress(item)}
    >
      <View style={styles.itemHeader}>
        <View style={styles.itemTitleRow}>
          <Text style={styles.itemTitle} numberOfLines={2}>
            {item.title}
          </Text>
          <Icon
            name={getPriorityIcon(item.priority)}
            size={20}
            color={PRIORITY_COLORS[item.priority]}
          />
        </View>
        <View style={styles.itemStatus}>
          <Icon
            name={getStatusIcon(item.status)}
            size={16}
            color={STATUS_COLORS[item.status]}
          />
          <Text
            style={[styles.statusText, { color: STATUS_COLORS[item.status] }]}
          >
            {item.status.replace("_", " ").toUpperCase()}
          </Text>
        </View>
      </View>

      {item.description && (
        <Text style={styles.itemDescription} numberOfLines={2}>
          {item.description}
        </Text>
      )}

      {item.progress.percentage > 0 && (
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                { width: `${item.progress.percentage}%` },
              ]}
            />
          </View>
          <Text style={styles.progressText}>{item.progress.percentage}%</Text>
        </View>
      )}

      <View style={styles.itemFooter}>
        <View style={styles.tagsContainer}>
          {item.tags.slice(0, 3).map((tag, index) => (
            <View key={index} style={styles.tag}>
              <Text style={styles.tagText}>{tag}</Text>
            </View>
          ))}
          {item.tags.length > 3 && (
            <Text style={styles.moreTagsText}>+{item.tags.length - 3}</Text>
          )}
        </View>

        {(item.targetDate || item.estimatedCost) && (
          <View style={styles.itemMeta}>
            {item.targetDate && (
              <Text style={styles.metaText}>
                📅 {item.targetDate.toLocaleDateString()}
              </Text>
            )}
            {item.estimatedCost && (
              <Text style={styles.metaText}>
                💰 ${item.estimatedCost.toLocaleString()}
              </Text>
            )}
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderFilterChips = () => (
    <View style={styles.filtersContainer}>
      <Text style={styles.filterLabel}>Status:</Text>
      <TouchableOpacity
        style={[
          styles.filterChip,
          selectedStatus === "all" && styles.filterChipActive,
        ]}
        onPress={() => setSelectedStatus("all")}
      >
        <Text
          style={[
            styles.filterChipText,
            selectedStatus === "all" && styles.filterChipTextActive,
          ]}
        >
          All
        </Text>
      </TouchableOpacity>
      {Object.values(ItemStatus).map((status) => (
        <TouchableOpacity
          key={status}
          style={[
            styles.filterChip,
            selectedStatus === status && styles.filterChipActive,
          ]}
          onPress={() => setSelectedStatus(status)}
        >
          <Text
            style={[
              styles.filterChipText,
              selectedStatus === status && styles.filterChipTextActive,
            ]}
          >
            {status.replace("_", " ")}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Loading your bucket list...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>My Bucket List</Text>
        <TouchableOpacity style={styles.addButton} onPress={handleAddItem}>
          <Icon name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      <View style={styles.searchContainer}>
        <Icon name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search your dreams..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          autoCapitalize="none"
          autoCorrect={false}
        />
      </View>

      {renderFilterChips()}

      <FlatList
        data={filteredItems}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="list" size={64} color="#ccc" />
            <Text style={styles.emptyTitle}>No items found</Text>
            <Text style={styles.emptySubtitle}>
              {searchQuery ||
              selectedStatus !== "all" ||
              selectedPriority !== "all"
                ? "Try adjusting your filters"
                : "Start by adding your first dream!"}
            </Text>
            {!searchQuery &&
              selectedStatus === "all" &&
              selectedPriority === "all" && (
                <TouchableOpacity
                  style={styles.emptyButton}
                  onPress={handleAddItem}
                >
                  <Text style={styles.emptyButtonText}>
                    Add Your First Dream
                  </Text>
                </TouchableOpacity>
              )}
          </View>
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e1e5e9",
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1a1a1a",
  },
  addButton: {
    backgroundColor: "#007AFF",
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    marginHorizontal: 20,
    marginVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#e1e5e9",
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 12,
  },
  filtersContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingBottom: 12,
    flexWrap: "wrap",
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: "#666",
    marginRight: 8,
  },
  filterChip: {
    backgroundColor: "#f0f0f0",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 4,
  },
  filterChipActive: {
    backgroundColor: "#007AFF",
  },
  filterChipText: {
    fontSize: 12,
    color: "#666",
    textTransform: "capitalize",
  },
  filterChipTextActive: {
    color: "#fff",
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  itemCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: "#e1e5e9",
  },
  itemHeader: {
    marginBottom: 8,
  },
  itemTitleRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 4,
  },
  itemTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
    flex: 1,
    marginRight: 8,
  },
  itemStatus: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusText: {
    fontSize: 12,
    fontWeight: "500",
    marginLeft: 4,
  },
  itemDescription: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
    marginBottom: 12,
  },
  progressContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  progressBar: {
    flex: 1,
    height: 4,
    backgroundColor: "#e1e5e9",
    borderRadius: 2,
    marginRight: 8,
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#007AFF",
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    color: "#666",
    fontWeight: "500",
  },
  itemFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
  },
  tagsContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  tag: {
    backgroundColor: "#f0f8ff",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginRight: 6,
  },
  tagText: {
    fontSize: 11,
    color: "#007AFF",
    fontWeight: "500",
  },
  moreTagsText: {
    fontSize: 11,
    color: "#666",
    fontStyle: "italic",
  },
  itemMeta: {
    alignItems: "flex-end",
  },
  metaText: {
    fontSize: 11,
    color: "#666",
    marginBottom: 2,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#666",
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: "#999",
    textAlign: "center",
    marginBottom: 24,
  },
  emptyButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});
