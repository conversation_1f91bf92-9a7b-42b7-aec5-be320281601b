import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  RefreshControl,
} from "react-native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../navigation/AppNavigator";
import {
  BucketListItem,
  ItemStatus,
  Achievement,
  AchievementType,
  AchievementRarity,
} from "@dreamvault/types";
import { MaterialIcons as Icon } from "@expo/vector-icons";
import { analyticsApi } from "../utils/api";

type ProgressScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  "Main"
>;

interface Props {
  navigation: ProgressScreenNavigationProp;
}

interface ProgressStats {
  totalItems: number;
  completedItems: number;
  inProgressItems: number;
  completionRate: number;
  monthlyProgress: number;
  streakDays: number;
}

interface MonthlyData {
  month: string;
  completed: number;
  added: number;
}

export default function ProgressScreen({ navigation }: Props) {
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState<ProgressStats>({
    totalItems: 0,
    completedItems: 0,
    inProgressItems: 0,
    completionRate: 0,
    monthlyProgress: 0,
    streakDays: 0,
  });
  const [recentAchievements, setRecentAchievements] = useState<Achievement[]>(
    [],
  );
  const [monthlyData, setMonthlyData] = useState<MonthlyData[]>([]);
  const [recentCompletions, setRecentCompletions] = useState<BucketListItem[]>(
    [],
  );
  const [selectedTimeframe, setSelectedTimeframe] = useState<
    "week" | "month" | "year"
  >("month");

  useEffect(() => {
    loadProgressData();
  }, [selectedTimeframe]);

  const loadProgressData = async () => {
    try {
      // Load real analytics data from API
      const [dashboardResponse, progressResponse] = await Promise.all([
        analyticsApi.getDashboard({ timeframe: selectedTimeframe }),
        analyticsApi.getProgress({ timeframe: selectedTimeframe })
      ]);

      if (dashboardResponse.success && dashboardResponse.data) {
        const dashboardData = dashboardResponse.data;

        setStats({
          totalItems: dashboardData.totalItems || 0,
          completedItems: dashboardData.completedItems || 0,
          inProgressItems: dashboardData.inProgressItems || 0,
          completionRate: dashboardData.totalItems > 0
            ? Math.round((dashboardData.completedItems / dashboardData.totalItems) * 100)
            : 0,
          monthlyProgress: dashboardData.monthlyProgress || 0,
          streakDays: progressResponse.data?.streakData?.currentStreak || 0,
        });
      }

      // Mock achievements
      setRecentAchievements([
        {
          id: "1",
          userId: "user1",
          type: AchievementType.STREAK,
          title: "Week Warrior",
          description: "Completed items for 7 days straight",
          iconUrl: "whatshot",
          earnedAt: new Date(),
          metadata: { streak: 7 },
          rarity: AchievementRarity.COMMON,
        },
        {
          id: "2",
          userId: "user1",
          type: AchievementType.CATEGORY_MASTER,
          title: "Travel Explorer",
          description: "Completed 5 travel-related bucket list items",
          iconUrl: "flight",
          earnedAt: new Date(Date.now() - 86400000 * 2),
          metadata: { category: "travel", count: 5 },
          rarity: AchievementRarity.UNCOMMON,
        },
        {
          id: "3",
          userId: "user1",
          type: AchievementType.FIRST_COMPLETION,
          title: "First Steps",
          description: "Completed your first bucket list item",
          iconUrl: "star",
          earnedAt: new Date(Date.now() - 86400000 * 5),
          metadata: { count: 1 },
          rarity: AchievementRarity.COMMON,
        },
      ]);

      // Set monthly data from progress response or use mock data
      if (progressResponse.success && progressResponse.data?.progressTrend) {
        const monthlyData = progressResponse.data.progressTrend.map((item: any) => ({
          month: new Date(item.month).toLocaleDateString('en-US', { month: 'short' }),
          completed: parseInt(item.completed_count) || 0,
          added: parseInt(item.avg_completion) || 0
        }));
        setMonthlyData(monthlyData);
      } else {
        // Fallback to mock data
        setMonthlyData([
          { month: "Jan", completed: 2, added: 5 },
          { month: "Feb", completed: 1, added: 3 },
          { month: "Mar", completed: 3, added: 4 },
          { month: "Apr", completed: 2, added: 6 },
          { month: "May", completed: 0, added: 2 },
          { month: "Jun", completed: 0, added: 5 },
        ]);
      }

      // Mock recent completions
      setRecentCompletions([
        {
          id: "1",
          userId: "user1",
          title: "Learn to cook pasta from scratch",
          description: "Master the art of homemade pasta",
          category: "learning" as any,
          priority: "want_to_do" as any,
          status: ItemStatus.COMPLETED,
          tags: ["cooking", "skill"],
          media: [],
          progress: {
            percentage: 100,
            milestones: [
              { id: "1", title: "Bought ingredients", createdAt: new Date() },
              { id: "2", title: "First attempt", createdAt: new Date() },
              { id: "3", title: "Perfect pasta!", createdAt: new Date() },
            ],
            photos: [],
            notes: [],
            analytics: {
              streakDays: 0,
              completionTrend: "stable" as const,
            },
          },
          createdAt: new Date(Date.now() - 86400000 * 30),
          updatedAt: new Date(Date.now() - 86400000 * 2),
        },
        {
          id: "2",
          userId: "user1",
          title: "Read 12 books this year",
          description: "Expand knowledge through reading",
          category: "learning" as any,
          priority: "must_do" as any,
          status: ItemStatus.COMPLETED,
          tags: ["reading", "books", "knowledge"],
          media: [],
          progress: {
            percentage: 100,
            milestones: [
              { id: "1", title: "Book 1", createdAt: new Date() },
              { id: "2", title: "Book 6", createdAt: new Date() },
              { id: "3", title: "Book 12", createdAt: new Date() },
            ],
            photos: [],
            notes: [],
            analytics: {
              streakDays: 0,
              completionTrend: "stable" as const,
            },
          },
          createdAt: new Date(Date.now() - 86400000 * 365),
          updatedAt: new Date(Date.now() - 86400000 * 7),
        },
      ]);
    } catch (error) {
      console.error("Failed to load progress data:", error);
    } finally {
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadProgressData();
  };

  const handleItemPress = (item: BucketListItem) => {
    navigation.navigate("ItemDetail", { itemId: item.id });
  };

  const getAchievementIcon = (iconName: string) => {
    const iconMap: { [key: string]: string } = {
      "whatshot": "whatshot",
      flight: "flight",
      star: "star",
      "emoji-events": "emoji-events",
      "trending-up": "trending-up",
    };
    return iconMap[iconName] || "emoji-events";
  };

  const renderStatsCard = () => (
    <View style={styles.statsCard}>
      <Text style={styles.statsTitle}>Your Progress</Text>

      <View style={styles.mainStat}>
        <Text style={styles.mainStatNumber}>{stats.completionRate}%</Text>
        <Text style={styles.mainStatLabel}>Completion Rate</Text>
      </View>

      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{stats.totalItems}</Text>
          <Text style={styles.statLabel}>Total Dreams</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: "#34C759" }]}>
            {stats.completedItems}
          </Text>
          <Text style={styles.statLabel}>Completed</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: "#007AFF" }]}>
            {stats.inProgressItems}
          </Text>
          <Text style={styles.statLabel}>In Progress</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: "#FF9500" }]}>
            {stats.streakDays}
          </Text>
          <Text style={styles.statLabel}>Day Streak</Text>
        </View>
      </View>
    </View>
  );

  const renderProgressChart = () => {
    const maxValue = Math.max(
      ...monthlyData.map((d) => Math.max(d.completed, d.added)),
    );

    return (
      <View style={styles.chartCard}>
        <View style={styles.chartHeader}>
          <Text style={styles.chartTitle}>Monthly Activity</Text>
          <View style={styles.timeframeSelector}>
            {(["week", "month", "year"] as const).map((timeframe) => (
              <TouchableOpacity
                key={timeframe}
                style={[
                  styles.timeframeButton,
                  selectedTimeframe === timeframe &&
                    styles.timeframeButtonActive,
                ]}
                onPress={() => setSelectedTimeframe(timeframe)}
              >
                <Text
                  style={[
                    styles.timeframeButtonText,
                    selectedTimeframe === timeframe &&
                      styles.timeframeButtonTextActive,
                  ]}
                >
                  {timeframe}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.chart}>
          {monthlyData.map((data, index) => (
            <View key={index} style={styles.chartBar}>
              <View style={styles.barContainer}>
                <View
                  style={[
                    styles.bar,
                    styles.completedBar,
                    { height: `${(data.completed / maxValue) * 100}%` },
                  ]}
                />
                <View
                  style={[
                    styles.bar,
                    styles.addedBar,
                    { height: `${(data.added / maxValue) * 100}%` },
                  ]}
                />
              </View>
              <Text style={styles.barLabel}>{data.month}</Text>
            </View>
          ))}
        </View>

        <View style={styles.chartLegend}>
          <View style={styles.legendItem}>
            <View
              style={[styles.legendColor, { backgroundColor: "#34C759" }]}
            />
            <Text style={styles.legendText}>Completed</Text>
          </View>
          <View style={styles.legendItem}>
            <View
              style={[styles.legendColor, { backgroundColor: "#007AFF" }]}
            />
            <Text style={styles.legendText}>Added</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderAchievements = () => (
    <View style={styles.achievementsCard}>
      <Text style={styles.sectionTitle}>Recent Achievements</Text>

      {recentAchievements.map((achievement) => (
        <View key={achievement.id} style={styles.achievementItem}>
          <View style={styles.achievementIcon}>
            <Icon
              name={getAchievementIcon(achievement.iconUrl || "star") as any}
              size={24}
              color="#FFD700"
            />
          </View>
          <View style={styles.achievementContent}>
            <Text style={styles.achievementTitle}>{achievement.title}</Text>
            <Text style={styles.achievementDescription}>
              {achievement.description}
            </Text>
            <Text style={styles.achievementDate}>
              {achievement.earnedAt.toLocaleDateString()}
            </Text>
          </View>
        </View>
      ))}

      <TouchableOpacity style={styles.viewAllButton}>
        <Text style={styles.viewAllButtonText}>View All Achievements</Text>
        <Icon name="chevron-right" size={16} color="#007AFF" />
      </TouchableOpacity>
    </View>
  );

  const renderRecentCompletions = () => (
    <View style={styles.completionsCard}>
      <Text style={styles.sectionTitle}>Recent Completions</Text>

      {recentCompletions.map((item) => (
        <TouchableOpacity
          key={item.id}
          style={styles.completionItem}
          onPress={() => handleItemPress(item)}
        >
          <View style={styles.completionIcon}>
            <Icon name="check-circle" size={20} color="#34C759" />
          </View>
          <View style={styles.completionContent}>
            <Text style={styles.completionTitle} numberOfLines={1}>
              {item.title}
            </Text>
            <Text style={styles.completionDate}>
              Completed {item.updatedAt.toLocaleDateString()}
            </Text>
          </View>
          <Icon name="chevron-right" size={16} color="#ccc" />
        </TouchableOpacity>
      ))}

      <TouchableOpacity style={styles.viewAllButton}>
        <Text style={styles.viewAllButtonText}>View All Completed</Text>
        <Icon name="chevron-right" size={16} color="#007AFF" />
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Progress</Text>
        <TouchableOpacity>
          <Icon name="insights" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderStatsCard()}
        {renderProgressChart()}
        {renderAchievements()}
        {renderRecentCompletions()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e1e5e9",
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1a1a1a",
  },
  scrollView: {
    flex: 1,
  },
  statsCard: {
    backgroundColor: "#fff",
    margin: 20,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: "#e1e5e9",
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 20,
  },
  mainStat: {
    alignItems: "center",
    marginBottom: 24,
  },
  mainStatNumber: {
    fontSize: 48,
    fontWeight: "bold",
    color: "#007AFF",
  },
  mainStatLabel: {
    fontSize: 16,
    color: "#666",
    marginTop: 4,
  },
  statsGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  statItem: {
    alignItems: "center",
    flex: 1,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1a1a1a",
  },
  statLabel: {
    fontSize: 12,
    color: "#666",
    marginTop: 4,
    textAlign: "center",
  },
  chartCard: {
    backgroundColor: "#fff",
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: "#e1e5e9",
  },
  chartHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
  },
  timeframeSelector: {
    flexDirection: "row",
    backgroundColor: "#f0f0f0",
    borderRadius: 8,
    padding: 2,
  },
  timeframeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  timeframeButtonActive: {
    backgroundColor: "#007AFF",
  },
  timeframeButtonText: {
    fontSize: 12,
    color: "#666",
    textTransform: "capitalize",
  },
  timeframeButtonTextActive: {
    color: "#fff",
  },
  chart: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
    height: 120,
    marginBottom: 16,
  },
  chartBar: {
    alignItems: "center",
    flex: 1,
  },
  barContainer: {
    flexDirection: "row",
    alignItems: "flex-end",
    height: 100,
    marginBottom: 8,
  },
  bar: {
    width: 8,
    marginHorizontal: 1,
    borderRadius: 4,
    minHeight: 4,
  },
  completedBar: {
    backgroundColor: "#34C759",
  },
  addedBar: {
    backgroundColor: "#007AFF",
  },
  barLabel: {
    fontSize: 10,
    color: "#666",
  },
  chartLegend: {
    flexDirection: "row",
    justifyContent: "center",
  },
  legendItem: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 12,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 6,
  },
  legendText: {
    fontSize: 12,
    color: "#666",
  },
  achievementsCard: {
    backgroundColor: "#fff",
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: "#e1e5e9",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 16,
  },
  achievementItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  achievementIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "#fff3cd",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  achievementContent: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 2,
  },
  achievementDescription: {
    fontSize: 14,
    color: "#666",
    marginBottom: 2,
  },
  achievementDate: {
    fontSize: 12,
    color: "#999",
  },
  completionsCard: {
    backgroundColor: "#fff",
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: "#e1e5e9",
  },
  completionItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  completionIcon: {
    marginRight: 12,
  },
  completionContent: {
    flex: 1,
  },
  completionTitle: {
    fontSize: 16,
    fontWeight: "500",
    color: "#1a1a1a",
    marginBottom: 2,
  },
  completionDate: {
    fontSize: 12,
    color: "#666",
  },
  viewAllButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    marginTop: 8,
  },
  viewAllButtonText: {
    fontSize: 14,
    color: "#007AFF",
    fontWeight: "500",
    marginRight: 4,
  },
});
