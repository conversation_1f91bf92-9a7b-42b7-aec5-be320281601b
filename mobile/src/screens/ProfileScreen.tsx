import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Image,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from "react-native";
import { useAuth } from "../contexts/AuthContext";
import { MaterialIcons as Icon } from "@expo/vector-icons";
import { bucketListApi, achievementsApi } from "../utils/api";
import { useApi } from "../hooks/useApi";

interface ProfileStats {
  totalItems: number;
  completedItems: number;
  achievementsCount: number;
  streakDays: number;
}

export default function ProfileScreen() {
  const { user, logout } = useAuth();
  const [stats, setStats] = useState<ProfileStats>({
    totalItems: 0,
    completedItems: 0,
    achievementsCount: 0,
    streakDays: 0,
  });

  // Use API hooks for data fetching
  const {
    data: progressData,
    loading: progressLoading,
    error: progressError,
    execute: loadProgress,
  } = useApi(bucketListApi.getProgressAnalytics);

  const {
    data: achievementsData,
    loading: achievementsLoading,
    error: achievementsError,
    execute: loadAchievements,
  } = useApi(achievementsApi.getUserAchievements);

  const loading = progressLoading || achievementsLoading;

  useEffect(() => {
    loadUserProfile();
  }, []);

  const loadUserProfile = async () => {
    try {
      // Load progress analytics and achievements in parallel
      await Promise.all([loadProgress(), loadAchievements()]);
    } catch (error) {
      console.error("Failed to load user profile:", error);
      Alert.alert(
        "Error",
        "Failed to load profile data. Please check your connection and try again.",
      );
    }
  };

  // Update stats when data is loaded
  useEffect(() => {
    if (progressData && achievementsData) {
      setStats({
        totalItems: progressData.summary.totalItems,
        completedItems: progressData.summary.completedItems,
        achievementsCount: achievementsData.totalEarned,
        streakDays: progressData.summary.currentStreak,
      });
    }
  }, [progressData, achievementsData]);

  const handleEditProfile = () => {
    // TODO: Navigate to edit profile screen
    Alert.alert("Coming Soon", "Profile editing will be available soon!");
  };

  const handleSettings = () => {
    // TODO: Navigate to settings screen
    Alert.alert("Coming Soon", "Settings will be available soon!");
  };

  const handleLogout = () => {
    Alert.alert("Logout", "Are you sure you want to logout?", [
      { text: "Cancel", style: "cancel" },
      {
        text: "Logout",
        style: "destructive",
        onPress: async () => {
          try {
            await logout();
          } catch {
            Alert.alert("Error", "Failed to logout. Please try again.");
          }
        },
      },
    ]);
  };

  const handleShareProfile = () => {
    // TODO: Implement profile sharing
    Alert.alert("Coming Soon", "Profile sharing will be available soon!");
  };

  const handleViewAchievements = () => {
    // TODO: Navigate to achievements screen
    Alert.alert("Coming Soon", "Achievements view will be available soon!");
  };

  const handleViewCompletedItems = () => {
    // TODO: Navigate to completed items
    Alert.alert("Coming Soon", "Completed items view will be available soon!");
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading your profile...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Show error banner if there are API errors but we have some data
  const hasErrors = progressError || achievementsError;

  if (!user) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text>Failed to load profile</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Profile</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            onPress={handleShareProfile}
            style={styles.headerButton}
          >
            <Icon name="share" size={24} color="#007AFF" />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleSettings}
            style={styles.headerButton}
          >
            <Icon name="settings" size={24} color="#007AFF" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={loading}
            onRefresh={loadUserProfile}
            colors={["#007AFF"]}
            tintColor="#007AFF"
          />
        }
      >
        {/* Error Banner */}
        {hasErrors && (
          <View style={styles.errorBanner}>
            <Icon name="warning" size={16} color="#FF9500" />
            <Text style={styles.errorBannerText}>
              Some data may be outdated. Pull to refresh.
            </Text>
          </View>
        )}
        {/* Profile Header */}
        <View style={styles.profileHeader}>
          <View style={styles.avatarContainer}>
            {user.profile.avatar ? (
              <Image
                source={{ uri: user.profile.avatar }}
                style={styles.avatar}
              />
            ) : (
              <View style={styles.avatarPlaceholder}>
                <Icon name="person" size={48} color="#666" />
              </View>
            )}
          </View>

          <Text style={styles.displayName}>{user.profile.displayName}</Text>

          {user.profile.location && (
            <View style={styles.locationContainer}>
              <Icon name="location-on" size={16} color="#666" />
              <Text style={styles.location}>{user.profile.location}</Text>
            </View>
          )}

          {user.profile.bio && (
            <Text style={styles.bio}>{user.profile.bio}</Text>
          )}

          <TouchableOpacity
            style={styles.editProfileButton}
            onPress={handleEditProfile}
          >
            <Text style={styles.editProfileButtonText}>Edit Profile</Text>
          </TouchableOpacity>
        </View>

        {/* Stats */}
        <View style={styles.statsContainer}>
          <TouchableOpacity
            style={styles.statItem}
            onPress={handleViewCompletedItems}
          >
            <Text style={styles.statNumber}>{stats.completedItems}</Text>
            <Text style={styles.statLabel}>Completed</Text>
          </TouchableOpacity>

          <View style={styles.statDivider} />

          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{stats.totalItems}</Text>
            <Text style={styles.statLabel}>Total Dreams</Text>
          </View>

          <View style={styles.statDivider} />

          <TouchableOpacity
            style={styles.statItem}
            onPress={handleViewAchievements}
          >
            <Text style={styles.statNumber}>{stats.achievementsCount}</Text>
            <Text style={styles.statLabel}>Achievements</Text>
          </TouchableOpacity>

          <View style={styles.statDivider} />

          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: "#FF9500" }]}>
              {stats.streakDays}
            </Text>
            <Text style={styles.statLabel}>Day Streak</Text>
          </View>
        </View>

        {/* Interests */}
        {user.profile.interests && user.profile.interests.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Interests</Text>
            <View style={styles.interestsContainer}>
              {user.profile.interests.map((interest: string, index: number) => (
                <View key={index} style={styles.interestChip}>
                  <Text style={styles.interestChipText}>{interest}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>

          <TouchableOpacity
            style={styles.actionItem}
            onPress={handleViewAchievements}
          >
            <View style={styles.actionIcon}>
              <Icon name="emoji-events" size={24} color="#FFD700" />
            </View>
            <View style={styles.actionContent}>
              <Text style={styles.actionTitle}>View Achievements</Text>
              <Text style={styles.actionSubtitle}>
                See all your unlocked badges
              </Text>
            </View>
            <Icon name="chevron-right" size={20} color="#ccc" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionItem}
            onPress={handleViewCompletedItems}
          >
            <View style={styles.actionIcon}>
              <Icon name="check-circle" size={24} color="#34C759" />
            </View>
            <View style={styles.actionContent}>
              <Text style={styles.actionTitle}>Completed Dreams</Text>
              <Text style={styles.actionSubtitle}>
                Relive your accomplishments
              </Text>
            </View>
            <Icon name="chevron-right" size={20} color="#ccc" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionItem}>
            <View style={styles.actionIcon}>
              <Icon name="backup" size={24} color="#007AFF" />
            </View>
            <View style={styles.actionContent}>
              <Text style={styles.actionTitle}>Export Data</Text>
              <Text style={styles.actionSubtitle}>
                Download your bucket list
              </Text>
            </View>
            <Icon name="chevron-right" size={20} color="#ccc" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionItem}>
            <View style={styles.actionIcon}>
              <Icon name="help" size={24} color="#666" />
            </View>
            <View style={styles.actionContent}>
              <Text style={styles.actionTitle}>Help & Support</Text>
              <Text style={styles.actionSubtitle}>
                Get help using DreamVault
              </Text>
            </View>
            <Icon name="chevron-right" size={20} color="#ccc" />
          </TouchableOpacity>
        </View>

        {/* Account */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>

          <View style={styles.accountItem}>
            <Text style={styles.accountLabel}>Email</Text>
            <Text style={styles.accountValue}>{user.email}</Text>
          </View>

          <View style={styles.accountItem}>
            <Text style={styles.accountLabel}>Privacy</Text>
            <Text style={styles.accountValue}>
              {user.preferences.privacy.profileVisibility === "private"
                ? "Private"
                : user.preferences.privacy.profileVisibility === "friends"
                  ? "Friends Only"
                  : "Public"}
            </Text>
          </View>

          <View style={styles.accountItem}>
            <Text style={styles.accountLabel}>Member Since</Text>
            <Text style={styles.accountValue}>
              {user.createdAt.toLocaleDateString()}
            </Text>
          </View>
        </View>

        {/* Logout */}
        <View style={styles.section}>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Icon name="logout" size={20} color="#FF3B30" />
            <Text style={styles.logoutButtonText}>Logout</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e1e5e9",
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1a1a1a",
  },
  headerActions: {
    flexDirection: "row",
  },
  headerButton: {
    marginLeft: 16,
  },
  scrollView: {
    flex: 1,
  },
  profileHeader: {
    backgroundColor: "#fff",
    alignItems: "center",
    paddingVertical: 32,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#e1e5e9",
  },
  avatarContainer: {
    marginBottom: 16,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: "#f0f0f0",
    justifyContent: "center",
    alignItems: "center",
  },
  displayName: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 8,
  },
  locationContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  location: {
    fontSize: 14,
    color: "#666",
    marginLeft: 4,
  },
  bio: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    lineHeight: 22,
    marginBottom: 20,
  },
  editProfileButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 24,
    paddingVertical: 10,
    borderRadius: 20,
  },
  editProfileButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  statsContainer: {
    flexDirection: "row",
    backgroundColor: "#fff",
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#e1e5e9",
  },
  statItem: {
    flex: 1,
    alignItems: "center",
  },
  statDivider: {
    width: 1,
    backgroundColor: "#e1e5e9",
    marginVertical: 8,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: "#666",
  },
  section: {
    backgroundColor: "#fff",
    marginTop: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 16,
  },
  interestsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  interestChip: {
    backgroundColor: "#f0f8ff",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  interestChipText: {
    fontSize: 12,
    color: "#007AFF",
    fontWeight: "500",
  },
  actionItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  actionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#f8f9fa",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: "500",
    color: "#1a1a1a",
    marginBottom: 2,
  },
  actionSubtitle: {
    fontSize: 14,
    color: "#666",
  },
  accountItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  accountLabel: {
    fontSize: 16,
    color: "#1a1a1a",
  },
  accountValue: {
    fontSize: 16,
    color: "#666",
  },
  logoutButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
  },
  logoutButtonText: {
    fontSize: 16,
    color: "#FF3B30",
    fontWeight: "500",
    marginLeft: 8,
  },
  loadingText: {
    fontSize: 16,
    color: "#666",
    marginTop: 12,
  },
  errorBanner: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFF3CD",
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginHorizontal: 20,
    marginTop: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#FFEAA7",
  },
  errorBannerText: {
    fontSize: 14,
    color: "#856404",
    marginLeft: 8,
    flex: 1,
  },
});
