import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  RefreshControl,
  Dimensions,
  Alert,
  ActivityIndicator,
} from "react-native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../navigation/AppNavigator";
import {
  BucketListItem,
  ItemStatus,
  ItemCategory,
  ItemPriority,
} from "@dreamvault/types";
import { MaterialIcons as Icon } from "@expo/vector-icons";
import { bucketListApi } from "../lib/supabase-api";
import { useApi } from "../hooks/useApi";
import { getErrorMessage } from "../utils/errorHandler";

type HomeScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  "Main"
>;

interface Props {
  navigation: HomeScreenNavigationProp;
}

const { width } = Dimensions.get("window");

export default function HomeScreen({ navigation }: Props) {
  // Use custom hooks for API calls
  const {
    data: recentItemsData,
    loading: itemsLoading,
    error: itemsError,
    execute: loadRecentItems,
  } = useApi(() => bucketListApi.getRecent(5));

  const {
    data: statsData,
    loading: statsLoading,
    error: statsError,
    execute: loadStats,
  } = useApi(bucketListApi.getStats);

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadHomeData();
  }, []);

  const loadHomeData = async () => {
    try {
      // Load recent items and stats in parallel
      await Promise.all([
        loadRecentItems({
          limit: 5,
          sortBy: "updatedAt",
          sortOrder: "desc",
        }),
        loadStats(),
      ]);
    } catch (error) {
      console.error("Failed to load home data:", error);
    } finally {
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadHomeData();
  };

  // Extract data with fallbacks
  const recentItems = recentItemsData?.items || [];
  const stats = statsData || {
    total: 0,
    completed: 0,
    inProgress: 0,
    completionRate: 0,
  };

  const isLoading = itemsLoading || statsLoading;
  const hasError = itemsError || statsError;

  const handleItemPress = (item: BucketListItem) => {
    navigation.navigate("ItemDetail", { itemId: item.id });
  };

  const handleAddItem = () => {
    navigation.navigate("AddItem");
  };

  const renderRecentItem = ({ item }: { item: BucketListItem }) => (
    <TouchableOpacity
      style={styles.itemCard}
      onPress={() => handleItemPress(item)}
    >
      <Text style={styles.itemTitle}>{item.title}</Text>
      <Text style={styles.itemDescription} numberOfLines={2}>
        {item.description || "No description"}
      </Text>
      <View style={styles.itemFooter}>
        <Text style={styles.itemStatus}>{item.status.replace("_", " ")}</Text>
        <Text style={styles.itemProgress}>
          {item.progress?.percentage || 0}%
        </Text>
      </View>
    </TouchableOpacity>
  );

  const renderLoadingState = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color="#007AFF" />
      <Text style={styles.loadingText}>Loading your dreams...</Text>
    </View>
  );

  const renderErrorState = () => (
    <View style={styles.errorContainer}>
      <Icon name="error-outline" size={48} color="#FF3B30" />
      <Text style={styles.errorTitle}>Something went wrong</Text>
      <Text style={styles.errorMessage}>{getErrorMessage(hasError!)}</Text>
      <TouchableOpacity style={styles.retryButton} onPress={loadHomeData}>
        <Text style={styles.retryButtonText}>Try Again</Text>
      </TouchableOpacity>
    </View>
  );

  // Show loading state on initial load
  if (isLoading && !refreshing && recentItems.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        {renderLoadingState()}
      </SafeAreaView>
    );
  }

  // Show error state if there's an error and no data
  if (hasError && recentItems.length === 0 && !isLoading) {
    return (
      <SafeAreaView style={styles.container}>{renderErrorState()}</SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Welcome Back!</Text>
          <TouchableOpacity style={styles.addButton} onPress={handleAddItem}>
            <Icon name="add" size={24} color="#fff" />
          </TouchableOpacity>
        </View>

        {/* Show error banner if there's an error but we have cached data */}
        {hasError && recentItems.length > 0 && (
          <View style={styles.errorBanner}>
            <Icon name="warning" size={16} color="#FF9500" />
            <Text style={styles.errorBannerText}>
              Some data may be outdated. Pull to refresh.
            </Text>
          </View>
        )}

        {/* Stats Overview */}
        <View style={styles.statsCard}>
          <Text style={styles.statsTitle}>Your Progress</Text>
          {statsLoading ? (
            <View style={styles.statsLoading}>
              <ActivityIndicator size="small" color="#007AFF" />
            </View>
          ) : (
            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{stats.total}</Text>
                <Text style={styles.statLabel}>Total Dreams</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={[styles.statNumber, { color: "#34C759" }]}>
                  {stats.completed}
                </Text>
                <Text style={styles.statLabel}>Completed</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={[styles.statNumber, { color: "#007AFF" }]}>
                  {stats.inProgress}
                </Text>
                <Text style={styles.statLabel}>In Progress</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={[styles.statNumber, { color: "#FF9500" }]}>
                  {Math.round(stats.completionRate)}%
                </Text>
                <Text style={styles.statLabel}>Completion</Text>
              </View>
            </View>
          )}
        </View>

        {/* Recent Items */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Dreams</Text>
          {itemsLoading && recentItems.length === 0 ? (
            <View style={styles.sectionLoading}>
              <ActivityIndicator size="small" color="#007AFF" />
              <Text style={styles.loadingText}>Loading recent dreams...</Text>
            </View>
          ) : recentItems.length > 0 ? (
            recentItems.map((item) => (
              <View key={item.id}>{renderRecentItem({ item })}</View>
            ))
          ) : (
            <View style={styles.emptyState}>
              <Icon name="lightbulb-outline" size={48} color="#ccc" />
              <Text style={styles.emptyStateText}>No dreams yet</Text>
              <Text style={styles.emptyStateSubtext}>
                Start by adding your first dream!
              </Text>
            </View>
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionsGrid}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleAddItem}
            >
              <Icon name="add-circle" size={32} color="#007AFF" />
              <Text style={styles.actionText}>Add Dream</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => {
                // Navigate to Main tab navigator, then to Search tab
                navigation.navigate("Main");
              }}
            >
              <Icon name="search" size={32} color="#007AFF" />
              <Text style={styles.actionText}>Search</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => {
                // Navigate to Main tab navigator, then to Progress tab
                navigation.navigate("Main");
              }}
            >
              <Icon name="trending-up" size={32} color="#007AFF" />
              <Text style={styles.actionText}>Progress</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Icon name="emoji-events" size={32} color="#007AFF" />
              <Text style={styles.actionText}>Achievements</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#1a1a1a",
  },
  addButton: {
    backgroundColor: "#007AFF",
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
  },
  statsCard: {
    backgroundColor: "#fff",
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: "#e1e5e9",
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  statItem: {
    alignItems: "center",
    flex: 1,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1a1a1a",
  },
  statLabel: {
    fontSize: 12,
    color: "#666",
    marginTop: 4,
    textAlign: "center",
  },
  section: {
    marginHorizontal: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 16,
  },
  itemCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: "#e1e5e9",
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 4,
  },
  itemDescription: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
    marginBottom: 12,
  },
  itemFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  itemStatus: {
    fontSize: 12,
    color: "#007AFF",
    fontWeight: "500",
    textTransform: "capitalize",
  },
  itemProgress: {
    fontSize: 12,
    color: "#666",
    fontWeight: "500",
  },
  actionsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  actionButton: {
    width: (width - 60) / 2,
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    alignItems: "center",
    marginBottom: 12,
    borderWidth: 1,
    borderColor: "#e1e5e9",
  },
  actionText: {
    fontSize: 14,
    color: "#1a1a1a",
    fontWeight: "500",
    marginTop: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 60,
  },
  loadingText: {
    fontSize: 16,
    color: "#666",
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#1a1a1a",
    marginTop: 16,
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    lineHeight: 22,
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  errorBanner: {
    backgroundColor: "#FFF3CD",
    marginHorizontal: 20,
    marginBottom: 16,
    padding: 12,
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#FFEAA7",
  },
  errorBannerText: {
    fontSize: 14,
    color: "#856404",
    marginLeft: 8,
    flex: 1,
  },
  statsLoading: {
    height: 60,
    justifyContent: "center",
    alignItems: "center",
  },
  sectionLoading: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 20,
  },
  emptyState: {
    alignItems: "center",
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: "600",
    color: "#666",
    marginTop: 16,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: "#999",
    marginTop: 4,
    textAlign: "center",
  },
});
