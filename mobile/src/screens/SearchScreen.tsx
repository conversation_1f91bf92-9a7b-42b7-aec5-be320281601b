import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  TextInput,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
} from "react-native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../navigation/AppNavigator";
import {
  BucketListItem,
  ItemStatus,
  ItemPriority,
  ItemCategory,
} from "@dreamvault/types";
import { MaterialIcons as Icon } from "@expo/vector-icons";
import { bucketListApi } from "../lib/supabase-api";

type SearchScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  "Main"
>;

interface Props {
  navigation: SearchScreenNavigationProp;
}

interface SearchFilters {
  category: string;
  status: ItemStatus | "all";
  priority: ItemPriority | "all";
  dateRange: "all" | "this_week" | "this_month" | "this_year";
  hasLocation: boolean | null;
  hasCost: boolean | null;
}

const CATEGORIES = [
  "all",
  "travel",
  "career",
  "personal_growth",
  "relationships",
  "adventures",
  "learning",
  "fitness",
  "creative",
  "food",
];

const STATUS_COLORS = {
  [ItemStatus.NOT_STARTED]: "#666",
  [ItemStatus.IN_PROGRESS]: "#007AFF",
  [ItemStatus.COMPLETED]: "#34C759",
  [ItemStatus.ON_HOLD]: "#FF9500",
};

export default function SearchScreen({ navigation }: Props) {
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<BucketListItem[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [popularTags, setPopularTags] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    category: "all",
    status: "all",
    priority: "all",
    dateRange: "all",
    hasLocation: null,
    hasCost: null,
  });

  useEffect(() => {
    loadInitialData();
  }, []);

  const performSearch = useCallback(async () => {
    if (!searchQuery.trim()) {
      return;
    }

    setLoading(true);
    try {
      // Build search filters
      const searchFilters: any = {};

      if (filters.category !== "all") {
        searchFilters.category = filters.category;
      }
      if (filters.status !== "all") {
        searchFilters.status = filters.status;
      }
      if (filters.priority !== "all") {
        searchFilters.priority = filters.priority;
      }

      // Perform search using API
      const response = await bucketListApi.search(
        searchQuery,
        searchFilters,
      );

      setSearchResults(response.items || []);

      // Add to recent searches
      if (!recentSearches.includes(searchQuery)) {
        setRecentSearches((prev) => [searchQuery, ...prev.slice(0, 4)]);
      }
    } catch (error) {
      console.error("Search failed:", error);
      Alert.alert(
        "Error",
        "Search failed. Please check your connection and try again.",
      );
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  }, [searchQuery, filters, recentSearches]);

  useEffect(() => {
    if (searchQuery.trim()) {
      performSearch();
    } else {
      setSearchResults([]);
    }
  }, [searchQuery, filters, performSearch]);

  const loadInitialData = async () => {
    try {
      // TODO: Load from AsyncStorage or API
      setRecentSearches([
        "Tokyo travel",
        "guitar lessons",
        "marathon training",
      ]);
      setPopularTags([
        "travel",
        "fitness",
        "learning",
        "adventure",
        "food",
        "music",
      ]);
    } catch (error) {
      console.error("Failed to load initial data:", error);
    }
  };

  const handleItemPress = (item: BucketListItem) => {
    navigation.navigate("ItemDetail", { itemId: item.id });
  };

  const handleRecentSearchPress = (query: string) => {
    setSearchQuery(query);
  };

  const handleTagPress = (tag: string) => {
    setSearchQuery(tag);
  };

  const clearSearch = () => {
    setSearchQuery("");
    setSearchResults([]);
  };

  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  const resetFilters = () => {
    setFilters({
      category: "all",
      status: "all",
      priority: "all",
      dateRange: "all",
      hasLocation: null,
      hasCost: null,
    });
  };

  const getStatusIcon = (status: ItemStatus) => {
    switch (status) {
      case ItemStatus.NOT_STARTED:
        return "radio-button-unchecked";
      case ItemStatus.IN_PROGRESS:
        return "schedule";
      case ItemStatus.COMPLETED:
        return "check-circle";
      case ItemStatus.ON_HOLD:
        return "pause-circle-filled";
      default:
        return "help";
    }
  };

  const renderSearchResult = ({ item }: { item: BucketListItem }) => (
    <TouchableOpacity
      style={styles.resultCard}
      onPress={() => handleItemPress(item)}
    >
      <View style={styles.resultHeader}>
        <Text style={styles.resultTitle} numberOfLines={2}>
          {item.title}
        </Text>
        <Icon
          name={getStatusIcon(item.status)}
          size={20}
          color={STATUS_COLORS[item.status]}
        />
      </View>

      {item.description && (
        <Text style={styles.resultDescription} numberOfLines={2}>
          {item.description}
        </Text>
      )}

      <View style={styles.resultFooter}>
        <View style={styles.resultTags}>
          {item.tags.slice(0, 3).map((tag, index) => (
            <View key={index} style={styles.resultTag}>
              <Text style={styles.resultTagText}>{tag}</Text>
            </View>
          ))}
        </View>

        {item.progress.percentage > 0 && (
          <Text style={styles.resultProgress}>
            {item.progress.percentage}% complete
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderFilters = () => {
    if (!showFilters) {
      return null;
    }

    return (
      <View style={styles.filtersContainer}>
        <View style={styles.filtersHeader}>
          <Text style={styles.filtersTitle}>Filters</Text>
          <TouchableOpacity onPress={resetFilters}>
            <Text style={styles.resetFiltersText}>Reset</Text>
          </TouchableOpacity>
        </View>

        {/* Category Filter */}
        <View style={styles.filterSection}>
          <Text style={styles.filterLabel}>Category</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.filterChips}>
              {CATEGORIES.map((category) => (
                <TouchableOpacity
                  key={category}
                  style={[
                    styles.filterChip,
                    filters.category === category && styles.filterChipActive,
                  ]}
                  onPress={() => setFilters((prev) => ({ ...prev, category }))}
                >
                  <Text
                    style={[
                      styles.filterChipText,
                      filters.category === category &&
                        styles.filterChipTextActive,
                    ]}
                  >
                    {category.replace("_", " ")}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Status Filter */}
        <View style={styles.filterSection}>
          <Text style={styles.filterLabel}>Status</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.filterChips}>
              <TouchableOpacity
                style={[
                  styles.filterChip,
                  filters.status === "all" && styles.filterChipActive,
                ]}
                onPress={() =>
                  setFilters((prev) => ({ ...prev, status: "all" }))
                }
              >
                <Text
                  style={[
                    styles.filterChipText,
                    filters.status === "all" && styles.filterChipTextActive,
                  ]}
                >
                  All
                </Text>
              </TouchableOpacity>
              {Object.values(ItemStatus).map((status) => (
                <TouchableOpacity
                  key={status}
                  style={[
                    styles.filterChip,
                    filters.status === status && styles.filterChipActive,
                  ]}
                  onPress={() => setFilters((prev) => ({ ...prev, status }))}
                >
                  <Text
                    style={[
                      styles.filterChipText,
                      filters.status === status && styles.filterChipTextActive,
                    ]}
                  >
                    {status.replace("_", " ")}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Priority Filter */}
        <View style={styles.filterSection}>
          <Text style={styles.filterLabel}>Priority</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.filterChips}>
              <TouchableOpacity
                style={[
                  styles.filterChip,
                  filters.priority === "all" && styles.filterChipActive,
                ]}
                onPress={() =>
                  setFilters((prev) => ({ ...prev, priority: "all" }))
                }
              >
                <Text
                  style={[
                    styles.filterChipText,
                    filters.priority === "all" && styles.filterChipTextActive,
                  ]}
                >
                  All
                </Text>
              </TouchableOpacity>
              {[
                ItemPriority.MUST_DO,
                ItemPriority.WANT_TO_DO,
                ItemPriority.SOMEDAY,
              ].map((priority) => (
                <TouchableOpacity
                  key={priority}
                  style={[
                    styles.filterChip,
                    filters.priority === priority && styles.filterChipActive,
                  ]}
                  onPress={() => setFilters((prev) => ({ ...prev, priority }))}
                >
                  <Text
                    style={[
                      styles.filterChipText,
                      filters.priority === priority &&
                        styles.filterChipTextActive,
                    ]}
                  >
                    {priority.replace("_", " ")}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Search</Text>
        <TouchableOpacity onPress={toggleFilters}>
          <Icon
            name={showFilters ? "filter-list-off" : "filter-list"}
            size={24}
            color="#007AFF"
          />
        </TouchableOpacity>
      </View>

      <View style={styles.searchContainer}>
        <Icon name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search your dreams..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          autoCapitalize="none"
          autoCorrect={false}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={clearSearch}>
            <Icon name="clear" size={20} color="#666" />
          </TouchableOpacity>
        )}
      </View>

      {renderFilters()}

      {loading && (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Searching...</Text>
        </View>
      )}

      {!searchQuery.trim() && !loading && (
        <ScrollView style={styles.suggestionsContainer}>
          {recentSearches.length > 0 && (
            <View style={styles.suggestionSection}>
              <Text style={styles.suggestionTitle}>Recent Searches</Text>
              {recentSearches.map((search, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.suggestionItem}
                  onPress={() => handleRecentSearchPress(search)}
                >
                  <Icon name="history" size={16} color="#666" />
                  <Text style={styles.suggestionText}>{search}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}

          <View style={styles.suggestionSection}>
            <Text style={styles.suggestionTitle}>Popular Tags</Text>
            <View style={styles.tagsContainer}>
              {popularTags.map((tag, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.tagChip}
                  onPress={() => handleTagPress(tag)}
                >
                  <Text style={styles.tagChipText}>#{tag}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </ScrollView>
      )}

      {searchQuery.trim() && !loading && (
        <FlatList
          data={searchResults}
          renderItem={renderSearchResult}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.resultsContainer}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Icon name="search-off" size={64} color="#ccc" />
              <Text style={styles.emptyTitle}>No results found</Text>
              <Text style={styles.emptySubtitle}>
                Try different keywords or adjust your filters
              </Text>
            </View>
          }
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e1e5e9",
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1a1a1a",
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    marginHorizontal: 20,
    marginVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#e1e5e9",
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 12,
  },
  filtersContainer: {
    backgroundColor: "#fff",
    marginHorizontal: 20,
    marginBottom: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#e1e5e9",
    padding: 16,
  },
  filtersHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  filtersTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
  },
  resetFiltersText: {
    color: "#007AFF",
    fontSize: 14,
    fontWeight: "500",
  },
  filterSection: {
    marginBottom: 16,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: "#666",
    marginBottom: 8,
  },
  filterChips: {
    flexDirection: "row",
  },
  filterChip: {
    backgroundColor: "#f0f0f0",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  filterChipActive: {
    backgroundColor: "#007AFF",
  },
  filterChipText: {
    fontSize: 12,
    color: "#666",
    textTransform: "capitalize",
  },
  filterChipTextActive: {
    color: "#fff",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: 16,
    color: "#666",
  },
  suggestionsContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  suggestionSection: {
    marginBottom: 24,
  },
  suggestionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 12,
  },
  suggestionItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: "#fff",
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: "#e1e5e9",
  },
  suggestionText: {
    fontSize: 14,
    color: "#1a1a1a",
    marginLeft: 12,
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  tagChip: {
    backgroundColor: "#f0f8ff",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  tagChipText: {
    fontSize: 12,
    color: "#007AFF",
    fontWeight: "500",
  },
  resultsContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  resultCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: "#e1e5e9",
  },
  resultHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 8,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    flex: 1,
    marginRight: 8,
  },
  resultDescription: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
    marginBottom: 12,
  },
  resultFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  resultTags: {
    flexDirection: "row",
    flex: 1,
  },
  resultTag: {
    backgroundColor: "#f0f8ff",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginRight: 6,
  },
  resultTagText: {
    fontSize: 11,
    color: "#007AFF",
    fontWeight: "500",
  },
  resultProgress: {
    fontSize: 12,
    color: "#007AFF",
    fontWeight: "500",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#666",
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: "#999",
    textAlign: "center",
  },
});
