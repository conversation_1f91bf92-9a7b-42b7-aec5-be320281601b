import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Image,
} from "react-native";
import { RouteProp } from "@react-navigation/native";
import { AuthStackParamList } from "../../navigation/AuthNavigator";
import { MaterialIcons as Icon } from "@expo/vector-icons";
import { pickPhoto } from "../../utils/camera";

type ProfileSetupScreenRouteProp = RouteProp<
  AuthStackParamList,
  "ProfileSetup"
>;

interface Props {
  route: ProfileSetupScreenRouteProp;
}

const INTERESTS = [
  "Travel",
  "Adventure",
  "Food",
  "Sports",
  "Art",
  "Music",
  "Photography",
  "Reading",
  "Technology",
  "Nature",
  "Fitness",
  "Learning",
];

export default function ProfileSetupScreen({ route }: Props) {
  const [displayName, setDisplayName] = useState("");
  const [bio, setBio] = useState("");
  const [location, setLocation] = useState("");
  const [avatar, setAvatar] = useState<string | null>(null);
  const [selectedInterests, setSelectedInterests] = useState<string[]>([]);
  const [privacy, setPrivacy] = useState<"public" | "private">("private");
  const [loading, setLoading] = useState(false);

  const handleImagePicker = async () => {
    try {
      const result = await pickPhoto({
        quality: 0.8,
        maxWidth: 400,
        maxHeight: 400,
      });

      if (result) {
        setAvatar(result.uri);
      }
    } catch (error) {
      console.error("Failed to pick avatar:", error);
      Alert.alert("Error", "Failed to select photo. Please try again.");
    }
  };

  const toggleInterest = (interest: string) => {
    setSelectedInterests((prev) =>
      prev.includes(interest)
        ? prev.filter((i) => i !== interest)
        : [...prev, interest],
    );
  };

  const handleCompleteSetup = async () => {
    if (!displayName.trim()) {
      Alert.alert("Error", "Please enter a display name");
      return;
    }

    setLoading(true);
    try {
      // TODO: Implement actual profile setup logic
      const profileData = {
        userId: route.params.userId,
        displayName: displayName.trim(),
        bio: bio.trim(),
        location: location.trim(),
        avatar,
        interests: selectedInterests,
        privacy,
      };

      console.log("Profile setup:", profileData);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      Alert.alert("Success", "Profile setup complete!", [
        {
          text: "OK",
          onPress: () => {
            // TODO: Navigate to main app or trigger auth state change
            console.log("Profile setup completed");
          },
        },
      ]);
    } catch (error) {
      console.error("Profile setup error:", error);
      Alert.alert("Error", "Failed to setup profile. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.content}>
            <View style={styles.header}>
              <Text style={styles.title}>Complete Your Profile</Text>
              <Text style={styles.subtitle}>Tell us a bit about yourself</Text>
            </View>

            <View style={styles.form}>
              {/* Avatar Section */}
              <View style={styles.avatarSection}>
                <TouchableOpacity
                  style={styles.avatarContainer}
                  onPress={handleImagePicker}
                >
                  {avatar ? (
                    <Image source={{ uri: avatar }} style={styles.avatar} />
                  ) : (
                    <View style={styles.avatarPlaceholder}>
                      <Icon name="camera-alt" size={32} color="#666" />
                    </View>
                  )}
                </TouchableOpacity>
                <Text style={styles.avatarText}>Tap to add photo</Text>
              </View>

              {/* Basic Info */}
              <TextInput
                style={styles.input}
                placeholder="Display Name *"
                value={displayName}
                onChangeText={setDisplayName}
                autoCapitalize="words"
                maxLength={50}
              />

              <TextInput
                style={[styles.input, styles.textArea]}
                placeholder="Bio (optional)"
                value={bio}
                onChangeText={setBio}
                multiline
                numberOfLines={3}
                maxLength={200}
                textAlignVertical="top"
              />

              <TextInput
                style={styles.input}
                placeholder="Location (optional)"
                value={location}
                onChangeText={setLocation}
                autoCapitalize="words"
                maxLength={100}
              />

              {/* Interests */}
              <Text style={styles.sectionTitle}>Interests</Text>
              <View style={styles.interestsContainer}>
                {INTERESTS.map((interest) => (
                  <TouchableOpacity
                    key={interest}
                    style={[
                      styles.interestChip,
                      selectedInterests.includes(interest) &&
                        styles.interestChipSelected,
                    ]}
                    onPress={() => toggleInterest(interest)}
                  >
                    <Text
                      style={[
                        styles.interestChipText,
                        selectedInterests.includes(interest) &&
                          styles.interestChipTextSelected,
                      ]}
                    >
                      {interest}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Privacy Settings */}
              <Text style={styles.sectionTitle}>Privacy</Text>
              <View style={styles.privacyContainer}>
                <TouchableOpacity
                  style={[
                    styles.privacyOption,
                    privacy === "private" && styles.privacyOptionSelected,
                  ]}
                  onPress={() => setPrivacy("private")}
                >
                  <Icon
                    name="lock"
                    size={20}
                    color={privacy === "private" ? "#007AFF" : "#666"}
                  />
                  <Text
                    style={[
                      styles.privacyOptionText,
                      privacy === "private" && styles.privacyOptionTextSelected,
                    ]}
                  >
                    Private
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.privacyOption,
                    privacy === "public" && styles.privacyOptionSelected,
                  ]}
                  onPress={() => setPrivacy("public")}
                >
                  <Icon
                    name="public"
                    size={20}
                    color={privacy === "public" ? "#007AFF" : "#666"}
                  />
                  <Text
                    style={[
                      styles.privacyOptionText,
                      privacy === "public" && styles.privacyOptionTextSelected,
                    ]}
                  >
                    Public
                  </Text>
                </TouchableOpacity>
              </View>

              <TouchableOpacity
                style={[styles.button, styles.primaryButton]}
                onPress={handleCompleteSetup}
                disabled={loading}
              >
                <Text style={styles.primaryButtonText}>
                  {loading ? "Setting up..." : "Complete Setup"}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    padding: 24,
  },
  header: {
    alignItems: "center",
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
  form: {
    flex: 1,
  },
  avatarSection: {
    alignItems: "center",
    marginBottom: 32,
  },
  avatarContainer: {
    marginBottom: 8,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: "#f0f0f0",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#ddd",
    borderStyle: "dashed",
  },
  avatarText: {
    color: "#666",
    fontSize: 14,
  },
  input: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    marginBottom: 16,
    backgroundColor: "#f9f9f9",
  },
  textArea: {
    height: 80,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 16,
    marginTop: 8,
  },
  interestsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: 24,
  },
  interestChip: {
    backgroundColor: "#f0f0f0",
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    margin: 4,
    borderWidth: 1,
    borderColor: "#ddd",
  },
  interestChipSelected: {
    backgroundColor: "#007AFF",
    borderColor: "#007AFF",
  },
  interestChipText: {
    color: "#666",
    fontSize: 14,
  },
  interestChipTextSelected: {
    color: "#fff",
  },
  privacyContainer: {
    flexDirection: "row",
    marginBottom: 32,
  },
  privacyOption: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 12,
    marginHorizontal: 4,
    backgroundColor: "#f9f9f9",
  },
  privacyOptionSelected: {
    borderColor: "#007AFF",
    backgroundColor: "#f0f8ff",
  },
  privacyOptionText: {
    marginLeft: 8,
    fontSize: 16,
    color: "#666",
  },
  privacyOptionTextSelected: {
    color: "#007AFF",
  },
  button: {
    borderRadius: 12,
    padding: 16,
    alignItems: "center",
    marginTop: "auto",
  },
  primaryButton: {
    backgroundColor: "#007AFF",
  },
  primaryButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});
