const { getDefaultConfig } = require('@expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Add support for shared types package
const projectRoot = __dirname;
const workspaceRoot = path.resolve(projectRoot, '..');

config.watchFolders = [
  path.resolve(workspaceRoot, 'packages/types')
];

config.resolver.nodeModulesPaths = [
  path.resolve(projectRoot, 'node_modules'),
  path.resolve(workspaceRoot, 'node_modules'),
];

// Map @dreamvault/types to the local package
config.resolver.alias = {
  '@dreamvault/types': path.resolve(workspaceRoot, 'packages/types/src'),
};

module.exports = config;
