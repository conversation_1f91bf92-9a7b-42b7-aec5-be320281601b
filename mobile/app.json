{"expo": {"name": "DreamVault Mobile", "slug": "dreamvault-mobile", "version": "1.0.0", "orientation": "portrait", "jsEngine": "jsc", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.dreamvault.mobile", "jsEngine": "jsc", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.dreamvault.mobile", "jsEngine": "jsc", "permissions": ["android.permission.RECORD_AUDIO", "android.permission.CAMERA"]}, "web": {"favicon": "./assets/favicon.png"}, "scheme": "dreamvault", "plugins": ["expo-secure-store"], "extra": {"eas": {"projectId": "4ae92c25-bc70-4e4f-8deb-d607a334ac5e"}}, "owner": "sti<PERSON><PERSON>"}}