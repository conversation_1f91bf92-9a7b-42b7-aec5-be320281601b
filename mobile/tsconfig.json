{"extends": "@tsconfig/react-native/tsconfig.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "@dreamvault/types": ["../packages/types/src"]}, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "target": "ES2020", "module": "ESNext", "moduleResolution": "node"}, "include": ["src/**/*", "index.js"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}