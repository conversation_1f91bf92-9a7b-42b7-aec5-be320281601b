{"name": "@dreamvault/mobile", "version": "0.0.1", "main": "index.js", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "expo start", "start-tunnel": "expo start --tunnel", "test": "jest --passWithNoTests", "type-check": "tsc --noEmit"}, "dependencies": {"@dreamvault/types": "file:../packages/types", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-camera-roll/camera-roll": "^7.10.1", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "expo": "~53.0.20", "expo-secure-store": "~14.2.3", "metro": "^0.82.5", "metro-config": "^0.82.5", "metro-resolver": "^0.82.5", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "@supabase/supabase-js": "^2.39.0", "react-native-gesture-handler": "~2.24.0", "react-native-keychain": "^10.0.0", "react-native-permissions": "^5.4.2", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-vector-icons": "^10.0.2", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-transform-export-namespace-from": "^7.27.1", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@expo/metro-config": "^0.20.17", "@react-native-community/cli": "^19.1.1", "@react-native/babel-preset": "^0.80.2", "@react-native/eslint-config": "^0.72.2", "@react-native/js-polyfills": "^0.80.2", "@react-native/metro-babel-transformer": "^0.80.2", "@tsconfig/react-native": "^3.0.0", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "babel-jest": "^29.2.1", "babel-plugin-module-resolver": "^5.0.0", "eslint": "^8.19.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-prettier": "^5.5.3", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^3.6.2", "react-test-renderer": "18.2.0", "typescript": "~5.8.3"}, "engines": {"node": ">=16"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false}}}}