'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { AppLayout } from '@/components/layout/app-layout'
import { BucketListGrid } from '@/components/bucket-list/bucket-list-grid'
import { useAuth } from '@/components/auth/auth-provider'
import { bucketListApi, ApiError } from '@/lib/api'
import { Plus, Search, Filter, Target, Calendar, MapPin, DollarSign } from 'lucide-react'
import type { BucketListItem } from '@/components/bucket-list/bucket-list-item'

export default function GoalsPage() {
  const { user } = useAuth()
  const router = useRouter()

  const [bucketListItems, setBucketListItems] = useState<BucketListItem[]>([])
  const [filteredItems, setFilteredItems] = useState<BucketListItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Filter states
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedPriority, setSelectedPriority] = useState<string>('all')

  // Load bucket list items from API
  useEffect(() => {
    const loadBucketListItems = async () => {
      try {
        setLoading(true)
        setError(null)
        const response = await bucketListApi.getAll()
        setBucketListItems(response.items)
        setFilteredItems(response.items)
      } catch (err) {
        if (err instanceof ApiError) {
          setError(err.message)
        } else {
          setError('Failed to load bucket list items')
        }
        console.error('Error loading bucket list items:', err)
      } finally {
        setLoading(false)
      }
    }

    if (user) {
      loadBucketListItems()
    }
  }, [user])

  // Filter items based on search and filters
  useEffect(() => {
    let filtered = bucketListItems

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(item =>
        item.title.toLowerCase().includes(query) ||
        item.description?.toLowerCase().includes(query) ||
        item.location?.toLowerCase().includes(query) ||
        item.notes?.toLowerCase().includes(query)
      )
    }

    // Category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === selectedCategory)
    }

    // Status filter
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(item => item.status === selectedStatus)
    }

    // Priority filter
    if (selectedPriority !== 'all') {
      filtered = filtered.filter(item => item.priority === selectedPriority)
    }

    setFilteredItems(filtered)
  }, [bucketListItems, searchQuery, selectedCategory, selectedStatus, selectedPriority])

  const handleAddNew = () => {
    router.push('/goals/new')
  }

  const handleItemClick = (item: BucketListItem) => {
    router.push(`/goals/${item.id}`)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PLAYING': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'IN_PROGRESS': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'COMPLETED': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'PAUSED': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'MEDIUM': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
      case 'LOW': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  const stats = {
    total: bucketListItems.length,
    playing: bucketListItems.filter(item => item.status === 'PLAYING').length,
    inProgress: bucketListItems.filter(item => item.status === 'IN_PROGRESS').length,
    completed: bucketListItems.filter(item => item.status === 'COMPLETED').length,
    paused: bucketListItems.filter(item => item.status === 'PAUSED').length,
  }

  return (
    <AppLayout>
      <div className="container-app">
        {/* Header */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-gradient-brand rounded-xl shadow-lg">
                  <Target className="h-8 w-8 text-primary-foreground" />
                </div>
                <div>
                  <h1 className="text-heading-1 gradient-text">My Goals</h1>
                  <p className="text-body-large text-muted-foreground">
                    Manage and track your bucket list items
                  </p>
                </div>
              </div>
              <Button
                onClick={handleAddNew}
                variant="gradient"
                size="lg"
                className="flex items-center gap-2"
              >
                <Plus className="h-5 w-5" />
                Add New Goal
              </Button>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-heading-2 text-foreground">{stats.total}</div>
                <div className="text-body-small text-muted-foreground">Total Goals</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-heading-2 text-primary">{stats.playing}</div>
                <div className="text-body-small text-muted-foreground">Planning</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-heading-2 text-warning">{stats.inProgress}</div>
                <div className="text-body-small text-muted-foreground">In Progress</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-heading-2 text-success">{stats.completed}</div>
                <div className="text-body-small text-muted-foreground">Completed</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-heading-2 text-muted-foreground">{stats.paused}</div>
                <div className="text-body-small text-muted-foreground">Paused</div>
              </CardContent>
            </Card>
            </div>

            {/* Search and Filters */}
            <div className="flex flex-col md:flex-row gap-4 mb-6 p-4 glass-subtle rounded-xl">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search goals..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="TRAVEL">Travel</SelectItem>
                  <SelectItem value="CAREER">Career</SelectItem>
                  <SelectItem value="PERSONAL">Personal</SelectItem>
                  <SelectItem value="RELATIONSHIPS">Relationships</SelectItem>
                  <SelectItem value="ADVENTURES">Adventures</SelectItem>
                  <SelectItem value="LEARNING">Learning</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="PLAYING">Planning</SelectItem>
                  <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                  <SelectItem value="PAUSED">Paused</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedPriority} onValueChange={setSelectedPriority}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priority</SelectItem>
                  <SelectItem value="HIGH">High</SelectItem>
                  <SelectItem value="MEDIUM">Medium</SelectItem>
                  <SelectItem value="LOW">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
            </div>
          </CardContent>
        </Card>

        {/* Content */}
        {loading ? (
          <div className="text-center py-12">
            <div className="text-lg">Loading your goals...</div>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="text-red-600 mb-4">{error}</div>
            <Button onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        ) : filteredItems.length === 0 ? (
          <div className="text-center py-12">
            <Target className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">
              {bucketListItems.length === 0 ? 'No goals yet' : 'No goals match your filters'}
            </h3>
            <p className="text-muted-foreground mb-6">
              {bucketListItems.length === 0 
                ? 'Start building your bucket list by adding your first goal!'
                : 'Try adjusting your search or filter criteria.'
              }
            </p>
            {bucketListItems.length === 0 && (
              <Button onClick={handleAddNew}>
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Goal
              </Button>
            )}
          </div>
        ) : (
          <BucketListGrid
            items={filteredItems}
          />
        )}
      </div>
    </AppLayout>
  )
}
