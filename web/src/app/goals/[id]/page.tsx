'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { AppLayout } from '@/components/layout/app-layout'
import { useAuth } from '@/components/auth/auth-provider'
import { bucketListApi, ApiError } from '@/lib/api'
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Calendar, 
  MapPin, 
  DollarSign, 
  Target,
  Clock,
  CheckCircle,
  PlayCircle,
  PauseCircle,
  Flag
} from 'lucide-react'
import type { BucketListItem } from '@/components/bucket-list/bucket-list-item'

export default function GoalDetailPage() {
  const { user } = useAuth()
  const router = useRouter()
  const params = useParams()
  const goalId = params.id as string

  const [goal, setGoal] = useState<BucketListItem | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load goal details from API
  useEffect(() => {
    const loadGoal = async () => {
      try {
        setLoading(true)
        setError(null)
        const goalData = await bucketListApi.getById(goalId)
        setGoal(goalData.item)
      } catch (err) {
        if (err instanceof ApiError) {
          setError(err.message)
        } else {
          setError('Failed to load goal details')
        }
        console.error('Error loading goal:', err)
      } finally {
        setLoading(false)
      }
    }

    if (user && goalId) {
      loadGoal()
    }
  }, [user, goalId])

  const handleBack = () => {
    router.back()
  }

  const handleEdit = () => {
    router.push(`/goals/${goalId}/edit`)
  }

  const handleDelete = async () => {
    if (!goal) return
    
    if (confirm('Are you sure you want to delete this goal? This action cannot be undone.')) {
      try {
        await bucketListApi.delete(goal.id)
        router.push('/goals')
      } catch (err) {
        console.error('Error deleting goal:', err)
        alert('Failed to delete goal. Please try again.')
      }
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PLAYING': return <PlayCircle className="h-5 w-5" />
      case 'IN_PROGRESS': return <Clock className="h-5 w-5" />
      case 'COMPLETED': return <CheckCircle className="h-5 w-5" />
      case 'PAUSED': return <PauseCircle className="h-5 w-5" />
      default: return <Target className="h-5 w-5" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PLAYING': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'IN_PROGRESS': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'COMPLETED': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'PAUSED': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'MEDIUM': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
      case 'LOW': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  if (loading) {
    return (
      <AppLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <div className="text-lg">Loading goal details...</div>
          </div>
        </div>
      </AppLayout>
    )
  }

  if (error || !goal) {
    return (
      <AppLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <div className="text-red-600 mb-4">{error || 'Goal not found'}</div>
            <Button onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout>
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={handleBack}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Goals
          </Button>
          
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                {getStatusIcon(goal.status)}
                <h1 className="text-3xl font-bold">{goal.title}</h1>
              </div>
              <div className="flex items-center gap-2 mb-4">
                <Badge className={getStatusColor(goal.status)}>
                  {goal.status.replace('_', ' ')}
                </Badge>
                <Badge variant="outline" className={getPriorityColor(goal.priority)}>
                  <Flag className="h-3 w-3 mr-1" />
                  {goal.priority}
                </Badge>
                <Badge variant="outline">
                  {goal.category}
                </Badge>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleEdit}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button variant="destructive" onClick={handleDelete}>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Description */}
            {goal.description && (
              <Card>
                <CardHeader>
                  <CardTitle>Description</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">
                    {goal.description}
                  </p>
                </CardContent>
              </Card>
            )}

            {/* Notes */}
            {goal.notes && (
              <Card>
                <CardHeader>
                  <CardTitle>Notes</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed whitespace-pre-wrap">
                    {goal.notes}
                  </p>
                </CardContent>
              </Card>
            )}

            {/* Progress Section - Placeholder for future implementation */}
            <Card>
              <CardHeader>
                <CardTitle>Progress</CardTitle>
                <CardDescription>
                  Track your journey towards achieving this goal
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Progress tracking coming soon!</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Goal Details */}
            <Card>
              <CardHeader>
                <CardTitle>Goal Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {goal.target_date && (
                  <div className="flex items-center gap-3">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="text-sm font-medium">Target Date</div>
                      <div className="text-sm text-muted-foreground">
                        {formatDate(goal.target_date)}
                      </div>
                    </div>
                  </div>
                )}

                {goal.location && (
                  <div className="flex items-center gap-3">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="text-sm font-medium">Location</div>
                      <div className="text-sm text-muted-foreground">
                        {goal.location}
                      </div>
                    </div>
                  </div>
                )}

                {(goal.estimated_cost || goal.actual_cost) && (
                  <div className="flex items-center gap-3">
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="text-sm font-medium">Cost</div>
                      <div className="text-sm text-muted-foreground">
                        {goal.estimated_cost && (
                          <div>Estimated: {formatCurrency(goal.estimated_cost)}</div>
                        )}
                        {goal.actual_cost && (
                          <div>Actual: {formatCurrency(goal.actual_cost)}</div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                <Separator />

                <div className="text-xs text-muted-foreground">
                  <div>Created: {formatDate(goal.created_at)}</div>
                  <div>Updated: {formatDate(goal.updated_at)}</div>
                  {goal.completed_date && (
                    <div>Completed: {formatDate(goal.completed_date)}</div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Tags */}
            {goal.tags && goal.tags.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Tags</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {goal.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
