'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { AppLayout } from '@/components/layout/app-layout'
import { useAuth } from '@/components/auth/auth-provider'
import { bucketListApi, ApiError } from '@/lib/api'
import { 
  Trophy, 
  Star, 
  Target, 
  Calendar, 
  Award,
  Medal,
  Crown,
  Zap,
  CheckCircle,
  Lock,
  Gift,
  Sparkles
} from 'lucide-react'
import type { BucketListItem } from '@/components/bucket-list/bucket-list-item'

interface Achievement {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  category: string
  points: number
  unlocked: boolean
  unlockedAt?: string
  progress?: number
  maxProgress?: number
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
}

export default function AchievementsPage() {
  const { user } = useAuth()
  const router = useRouter()

  const [bucketListItems, setBucketListItems] = useState<BucketListItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load bucket list items from API
  useEffect(() => {
    const loadBucketListItems = async () => {
      try {
        setLoading(true)
        setError(null)
        const response = await bucketListApi.getAll()
        setBucketListItems(response.items)
      } catch (err) {
        if (err instanceof ApiError) {
          setError(err.message)
        } else {
          setError('Failed to load achievements data')
        }
        console.error('Error loading bucket list items:', err)
      } finally {
        setLoading(false)
      }
    }

    if (user) {
      loadBucketListItems()
    }
  }, [user])

  // Calculate achievement progress based on bucket list items
  const stats = {
    total: bucketListItems.length,
    completed: bucketListItems.filter(item => item.status === 'COMPLETED').length,
    categories: new Set(bucketListItems.map(item => item.category)).size,
    thisMonth: bucketListItems.filter(item => {
      if (!item.completed_date) return false
      const completedDate = new Date(item.completed_date)
      const now = new Date()
      return completedDate.getMonth() === now.getMonth() && completedDate.getFullYear() === now.getFullYear()
    }).length
  }

  // Define achievements based on user progress
  const achievements: Achievement[] = [
    // Beginner achievements
    {
      id: 'first-goal',
      title: 'Dream Starter',
      description: 'Create your first bucket list goal',
      icon: <Target className="h-6 w-6" />,
      category: 'Getting Started',
      points: 10,
      unlocked: stats.total >= 1,
      rarity: 'common'
    },
    {
      id: 'first-completion',
      title: 'First Victory',
      description: 'Complete your first bucket list goal',
      icon: <CheckCircle className="h-6 w-6" />,
      category: 'Completion',
      points: 25,
      unlocked: stats.completed >= 1,
      rarity: 'common'
    },
    
    // Progress achievements
    {
      id: 'goal-collector',
      title: 'Goal Collector',
      description: 'Create 10 bucket list goals',
      icon: <Star className="h-6 w-6" />,
      category: 'Collection',
      points: 50,
      unlocked: stats.total >= 10,
      progress: Math.min(stats.total, 10),
      maxProgress: 10,
      rarity: 'rare'
    },
    {
      id: 'achiever',
      title: 'Achiever',
      description: 'Complete 5 bucket list goals',
      icon: <Award className="h-6 w-6" />,
      category: 'Completion',
      points: 100,
      unlocked: stats.completed >= 5,
      progress: Math.min(stats.completed, 5),
      maxProgress: 5,
      rarity: 'rare'
    },
    {
      id: 'dream-chaser',
      title: 'Dream Chaser',
      description: 'Complete 25 bucket list goals',
      icon: <Medal className="h-6 w-6" />,
      category: 'Completion',
      points: 250,
      unlocked: stats.completed >= 25,
      progress: Math.min(stats.completed, 25),
      maxProgress: 25,
      rarity: 'epic'
    },
    
    // Category achievements
    {
      id: 'explorer',
      title: 'Life Explorer',
      description: 'Have goals in 4 different categories',
      icon: <Sparkles className="h-6 w-6" />,
      category: 'Diversity',
      points: 75,
      unlocked: stats.categories >= 4,
      progress: Math.min(stats.categories, 4),
      maxProgress: 4,
      rarity: 'rare'
    },
    
    // Time-based achievements
    {
      id: 'monthly-achiever',
      title: 'Monthly Achiever',
      description: 'Complete 3 goals in a single month',
      icon: <Calendar className="h-6 w-6" />,
      category: 'Momentum',
      points: 150,
      unlocked: stats.thisMonth >= 3,
      progress: Math.min(stats.thisMonth, 3),
      maxProgress: 3,
      rarity: 'epic'
    },
    
    // Legendary achievements
    {
      id: 'legend',
      title: 'Bucket List Legend',
      description: 'Complete 100 bucket list goals',
      icon: <Crown className="h-6 w-6" />,
      category: 'Mastery',
      points: 1000,
      unlocked: stats.completed >= 100,
      progress: Math.min(stats.completed, 100),
      maxProgress: 100,
      rarity: 'legendary'
    }
  ]

  const unlockedAchievements = achievements.filter(a => a.unlocked)
  const lockedAchievements = achievements.filter(a => !a.unlocked)
  const totalPoints = unlockedAchievements.reduce((sum, a) => sum + a.points, 0)

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
      case 'rare': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'epic': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'legendary': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  const getRarityBorder = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'border-gray-200'
      case 'rare': return 'border-blue-200'
      case 'epic': return 'border-purple-200'
      case 'legendary': return 'border-yellow-200'
      default: return 'border-gray-200'
    }
  }

  return (
    <AppLayout>
      <div className="container-app">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <div className="p-3 bg-gradient-to-r from-warning via-accent to-warning rounded-xl shadow-lg">
              <Trophy className="h-8 w-8 text-warning-foreground" />
            </div>
            <div>
              <h1 className="text-heading-1 gradient-text">Achievements</h1>
              <p className="text-body-large text-muted-foreground">
                Celebrate your progress and unlock rewards
              </p>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="text-center py-12 spacing-content">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <div className="text-body text-muted-foreground">Loading your achievements...</div>
          </div>
        ) : error ? (
          <div className="text-center py-12 spacing-content">
            <div className="text-destructive text-body mb-4">{error}</div>
            <Button onClick={() => window.location.reload()} variant="outline">
              Try Again
            </Button>
          </div>
        ) : (
          <div className="spacing-section">
            {/* Achievement Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-body-small font-medium text-muted-foreground">Total Points</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-heading-2 text-warning">{totalPoints}</div>
                  <p className="text-body-small text-muted-foreground">Achievement points earned</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-body-small font-medium text-muted-foreground">Unlocked</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-heading-2 text-success">{unlockedAchievements.length}</div>
                  <p className="text-body-small text-muted-foreground">
                    of {achievements.length} achievements
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-body-small font-medium text-muted-foreground">Goals Completed</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-heading-2 text-primary">{stats.completed}</div>
                  <p className="text-body-small text-muted-foreground">Dreams achieved</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-body-small font-medium text-muted-foreground">This Month</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-heading-2 text-secondary">{stats.thisMonth}</div>
                  <p className="text-body-small text-muted-foreground">Goals completed</p>
                </CardContent>
              </Card>
            </div>

            {/* Unlocked Achievements */}
            {unlockedAchievements.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-heading-2">Unlocked Achievements</CardTitle>
                  <CardDescription className="text-body">
                    Congratulations on your progress! Here are the achievements you've earned.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {unlockedAchievements.map((achievement) => (
                      <div
                        key={achievement.id}
                        className={`p-4 border-2 rounded-lg ${getRarityBorder(achievement.rarity)} bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800`}
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className="text-yellow-500">
                            {achievement.icon}
                          </div>
                          <Badge className={getRarityColor(achievement.rarity)}>
                            {achievement.rarity}
                          </Badge>
                        </div>
                        <h3 className="font-semibold mb-1">{achievement.title}</h3>
                        <p className="text-sm text-muted-foreground mb-2">
                          {achievement.description}
                        </p>
                        <div className="flex items-center justify-between">
                          <Badge variant="outline">
                            <Zap className="h-3 w-3 mr-1" />
                            {achievement.points} pts
                          </Badge>
                          <div className="text-xs text-muted-foreground">
                            {achievement.category}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Locked Achievements */}
            <Card>
              <CardHeader>
                <CardTitle className="text-heading-2">Upcoming Achievements</CardTitle>
                <CardDescription className="text-body">
                  Keep working towards your goals to unlock these achievements!
                </CardDescription>
              </CardHeader>
              <CardContent>
                {lockedAchievements.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Trophy className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Amazing! You've unlocked all available achievements!</p>
                    <p className="text-sm">More achievements coming soon...</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {lockedAchievements.map((achievement) => (
                      <div
                        key={achievement.id}
                        className="p-4 border rounded-lg bg-muted/30 opacity-75"
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className="text-muted-foreground">
                            <Lock className="h-6 w-6" />
                          </div>
                          <Badge variant="outline" className={getRarityColor(achievement.rarity)}>
                            {achievement.rarity}
                          </Badge>
                        </div>
                        <h3 className="font-semibold mb-1">{achievement.title}</h3>
                        <p className="text-sm text-muted-foreground mb-3">
                          {achievement.description}
                        </p>
                        
                        {achievement.progress !== undefined && achievement.maxProgress && (
                          <div className="mb-3">
                            <div className="flex justify-between text-xs mb-1">
                              <span>Progress</span>
                              <span>{achievement.progress}/{achievement.maxProgress}</span>
                            </div>
                            <Progress 
                              value={(achievement.progress / achievement.maxProgress) * 100} 
                              className="h-2"
                            />
                          </div>
                        )}
                        
                        <div className="flex items-center justify-between">
                          <Badge variant="outline">
                            <Gift className="h-3 w-3 mr-1" />
                            {achievement.points} pts
                          </Badge>
                          <div className="text-xs text-muted-foreground">
                            {achievement.category}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Call to Action */}
            {stats.total === 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-heading-2">Start Your Achievement Journey</CardTitle>
                  <CardDescription className="text-body">
                    Create your first bucket list goal to begin unlocking achievements!
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center py-6">
                  <Button onClick={() => router.push('/goals/new')} size="lg" variant="gradient">
                    <Target className="h-4 w-4 mr-2" />
                    Create Your First Goal
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>
    </AppLayout>
  )
}
