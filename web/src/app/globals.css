@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Core UI Colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 207 90% 54%;
    --radius: 0.75rem;
    
    /* Brand Colors */
    --primary: 207 90% 54%;
    --primary-foreground: 210 40% 98%;
    --secondary: 270 95% 75%;
    --secondary-foreground: 222.2 84% 4.9%;
    --accent: 25 95% 53%;
    --accent-foreground: 222.2 84% 4.9%;
    
    /* Semantic Colors */
    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    
    /* UI Element Colors */
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    
    /* Glassmorphism Variables */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: rgba(0, 0, 0, 0.1);
    
    /* Gradient Variables */
    --gradient-primary: linear-gradient(135deg, hsl(207, 90%, 54%) 0%, hsl(270, 95%, 75%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(270, 95%, 75%) 0%, hsl(25, 95%, 53%) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(207, 90%, 54%) 0%, hsl(270, 95%, 75%) 50%, hsl(25, 95%, 53%) 100%);
    --gradient-subtle: linear-gradient(135deg, hsl(207, 90%, 96%) 0%, hsl(270, 95%, 96%) 100%);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    
    /* Brand Colors - Dark Mode */
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 270 95% 80%;
    --secondary-foreground: 222.2 84% 4.9%;
    --accent: 25 95% 60%;
    --accent-foreground: 222.2 84% 4.9%;
    
    /* Semantic Colors - Dark Mode */
    --success: 142 76% 45%;
    --success-foreground: 210 40% 98%;
    --warning: 38 92% 60%;
    --warning-foreground: 222.2 84% 4.9%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    
    /* UI Element Colors - Dark Mode */
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    
    /* Glassmorphism Variables - Dark Mode */
    --glass-bg: rgba(0, 0, 0, 0.2);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: rgba(0, 0, 0, 0.3);
    
    /* Gradient Variables - Dark Mode */
    --gradient-primary: linear-gradient(135deg, hsl(217, 91%, 60%) 0%, hsl(270, 95%, 80%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(270, 95%, 80%) 0%, hsl(25, 95%, 60%) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(217, 91%, 60%) 0%, hsl(270, 95%, 80%) 50%, hsl(25, 95%, 60%) 100%);
    --gradient-subtle: linear-gradient(135deg, hsl(217, 32%, 18%) 0%, hsl(270, 32%, 20%) 100%);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Smooth transitions */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Focus styles */
.focus-visible:focus-visible {
  @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
}

/* Animation utilities */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Loading spinner */
.spinner {
  @apply inline-block w-4 h-4 border-2 border-current border-r-transparent rounded-full animate-spin;
}

/* Typography Utilities */
.text-display {
  @apply text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight;
}

.text-heading-1 {
  @apply text-3xl md:text-4xl font-bold tracking-tight;
}

.text-heading-2 {
  @apply text-2xl md:text-3xl font-semibold tracking-tight;
}

.text-heading-3 {
  @apply text-xl md:text-2xl font-semibold;
}

.text-body-large {
  @apply text-lg leading-relaxed;
}

.text-body {
  @apply text-base leading-relaxed;
}

.text-body-small {
  @apply text-sm leading-relaxed;
}

/* Gradient text */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.gradient-text-secondary {
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Glass morphism effects */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px var(--glass-shadow);
}

.glass-card {
  @apply glass rounded-xl;
}

.glass-subtle {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Background Utilities */
.bg-gradient-brand {
  background: var(--gradient-primary);
}

.bg-gradient-secondary {
  background: var(--gradient-secondary);
}

.bg-gradient-hero {
  background: var(--gradient-hero);
}

.bg-gradient-subtle {
  background: var(--gradient-subtle);
}

.bg-page {
  background: var(--gradient-subtle);
}

/* Layout Utilities */
.container-app {
  @apply container mx-auto px-4 py-8 max-w-7xl;
}

.container-narrow {
  @apply container mx-auto px-4 py-8 max-w-4xl;
}

.spacing-section {
  @apply space-y-8;
}

.spacing-card {
  @apply space-y-6;
}

.spacing-content {
  @apply space-y-4;
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-4px);
}

/* Button loading state */
.btn-loading {
  position: relative;
  color: transparent;
  transition: none;
}

.btn-loading:hover {
  color: transparent;
}

.btn-loading::after {
  content: '';
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: auto;
}

/* Form validation styles */
.field-error {
  border-color: hsl(var(--destructive));
}

.field-error:focus {
  ring-color: hsl(var(--destructive));
}

.field-success {
  border-color: #22c55e;
}

.field-success:focus {
  ring-color: #22c55e;
}

/* Progress bar animations */
.progress-bar {
  transition: all 0.5s ease-out;
}

/* Notification styles */
.notification-enter {
  opacity: 0;
  transform: translateX(100%);
}

.notification-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s ease;
}

.notification-exit {
  opacity: 1;
  transform: translateX(0);
}

.notification-exit-active {
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease;
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  .mobile-padding {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .mobile-text {
    font-size: 0.875rem;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .high-contrast {
    border: 2px solid hsl(var(--foreground));
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
