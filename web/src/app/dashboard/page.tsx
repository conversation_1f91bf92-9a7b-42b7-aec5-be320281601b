'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AppLayout } from '@/components/layout/app-layout'
import { BucketListGrid } from '@/components/bucket-list/bucket-list-grid'
import { useAuth } from '@/components/auth/auth-provider'
import { bucketListApi, ApiError } from '@/lib/api'
import { Plus, Target, TrendingUp, Award, Users } from 'lucide-react'
import type { BucketListItem } from '@/components/bucket-list/bucket-list-item'

export default function DashboardPage() {
  const { user } = useAuth()
  const router = useRouter()

  const [bucketListItems, setBucketListItems] = useState<BucketListItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load bucket list items from API
  useEffect(() => {
    const loadBucketListItems = async () => {
      try {
        setLoading(true)
        setError(null)
        const response = await bucketListApi.getAll()
        setBucketListItems(response.items)
      } catch (err) {
        if (err instanceof ApiError) {
          setError(err.message)
        } else {
          setError('Failed to load bucket list items')
        }
        console.error('Error loading bucket list items:', err)
      } finally {
        setLoading(false)
      }
    }

    if (user) {
      loadBucketListItems()
    }
  }, [user])

  const handleAddNew = () => {
    router.push('/goals/new')
  }

  const handleItemEdit = (item: BucketListItem) => {
    router.push(`/goals/${item.id}/edit`)
  }

  const handleItemDelete = async (id: string) => {
    try {
      await bucketListApi.delete(id)
      setBucketListItems(prev => prev.filter(item => item.id !== id))
    } catch (err) {
      console.error('Error deleting item:', err)
      setError('Failed to delete item')
    }
  }

  const handleItemStatusChange = async (id: string, status: BucketListItem['status']) => {
    try {
      const response = await bucketListApi.updateStatus(id, status)
      setBucketListItems(prev =>
        prev.map(item => item.id === id ? response.item : item)
      )
    } catch (err) {
      console.error('Error updating status:', err)
      setError('Failed to update status')
    }
  }

  const getStatusCounts = () => {
    return {
      total: bucketListItems.length,
      planning: bucketListItems.filter(item => item.status === 'PLAYING').length,
      in_progress: bucketListItems.filter(item => item.status === 'IN_PROGRESS').length,
      completed: bucketListItems.filter(item => item.status === 'COMPLETED').length,
      paused: bucketListItems.filter(item => item.status === 'PAUSED').length,
    }
  }

  const statusCounts = getStatusCounts()

  return (
    <AppLayout>
      <div className="container-app">
        <div className="spacing-section">
          {/* Welcome Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-heading-1 flex items-center gap-4">
                <div className="p-3 bg-gradient-brand rounded-xl shadow-lg">
                  <Target className="h-8 w-8 text-primary-foreground" />
                </div>
                <span className="gradient-text">Your Dream Journey</span>
              </CardTitle>
              <CardDescription className="text-body-large">
                Welcome back, <span className="font-semibold text-primary">{user?.user_metadata?.full_name || user?.email}</span>! Track your progress towards achieving your dreams.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  onClick={handleAddNew}
                  variant="gradient"
                  size="lg"
                  className="flex items-center gap-2"
                >
                  <Plus className="h-5 w-5" />
                  <span>Add New Goal</span>
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => router.push('/goals')}
                  className="flex items-center gap-2"
                >
                  <Target className="h-5 w-5" />
                  <span>View All Goals</span>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Stats Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-body-small font-medium text-muted-foreground">Total Goals</CardTitle>
                <div className="p-2 bg-gradient-brand rounded-lg">
                  <Target className="h-4 w-4 text-primary-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-heading-1 text-foreground">{statusCounts.total}</div>
                <p className="text-body-small text-muted-foreground mt-1">
                  {statusCounts.total === 0 ? 'Start by adding your first goal' : 'Goals in your bucket list'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-body-small font-medium text-muted-foreground">In Progress</CardTitle>
                <div className="p-2 bg-gradient-to-r from-warning to-accent rounded-lg">
                  <TrendingUp className="h-4 w-4 text-warning-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-heading-1 text-foreground">{statusCounts.in_progress}</div>
                <p className="text-body-small text-muted-foreground mt-1">
                  Goals you're actively working on
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-body-small font-medium text-muted-foreground">Completed</CardTitle>
                <div className="p-2 bg-gradient-to-r from-success to-success-600 rounded-lg">
                  <Award className="h-4 w-4 text-success-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-heading-1 text-foreground">{statusCounts.completed}</div>
                <p className="text-body-small text-muted-foreground mt-1">
                  Dreams you've achieved
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-body-small font-medium text-muted-foreground">Planning</CardTitle>
                <div className="p-2 bg-gradient-secondary rounded-lg">
                  <Users className="h-4 w-4 text-secondary-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-heading-1 text-foreground">{statusCounts.planning}</div>
                <p className="text-body-small text-muted-foreground mt-1">
                  Goals in planning phase
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Goals */}
          <Card>
            <CardHeader>
              <CardTitle className="text-heading-2 flex items-center gap-4">
                <div className="p-3 bg-gradient-brand rounded-xl shadow-lg">
                  <Target className="h-6 w-6 text-primary-foreground" />
                </div>
                <span>Recent Goals</span>
              </CardTitle>
              <CardDescription className="text-body">
                Your latest bucket list items and progress updates
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-12 spacing-content">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="text-muted-foreground">Loading your goals...</p>
                </div>
              ) : error ? (
                <div className="text-center py-12 spacing-content">
                  <Target className="h-16 w-16 mx-auto text-destructive/30" />
                  <p className="text-destructive text-body">{error}</p>
                  <Button onClick={() => window.location.reload()} variant="outline">
                    Try Again
                  </Button>
                </div>
              ) : bucketListItems.length === 0 ? (
                <div className="text-center py-12 spacing-content">
                  <Target className="h-16 w-16 mx-auto text-muted-foreground/30" />
                  <p className="text-muted-foreground text-body">No goals yet. Start by adding your first bucket list item!</p>
                  <Button onClick={handleAddNew} variant="gradient">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Your First Goal
                  </Button>
                </div>
              ) : (
                <BucketListGrid
                  items={bucketListItems.slice(0, 6)} // Show only first 6 items on dashboard
                  onItemEdit={handleItemEdit}
                  onItemDelete={handleItemDelete}
                  onItemStatusChange={handleItemStatusChange}
                  onAddNew={handleAddNew}
                />
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  )
}
