'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/components/auth/auth-provider'
import { AuthForm } from '@/components/auth/auth-form'
import { 
  Target, 
  Star, 
  Users, 
  Trophy,
  ArrowRight,
  CheckCircle,
  Sparkles,
  Heart,
  Globe,
  X,
  Zap,
  Rocket,
  ShieldCheck
} from 'lucide-react'

export default function LandingPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [showAuth, setShowAuth] = useState(false)

  // Redirect authenticated users to dashboard
  useEffect(() => {
    if (!loading && user) {
      router.push('/dashboard')
    }
  }, [user, loading, router])

  const handleGetStarted = () => {
    setShowAuth(true)
  }

  const handleSignIn = () => {
    setShowAuth(true)
  }

  const handleAuthSuccess = () => {
    setShowAuth(false)
    router.push('/dashboard')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-page flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  // Don't show landing page to authenticated users
  if (user) {
    return null
  }

  return (
    <div className="min-h-screen bg-page relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-secondary/20 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-accent/10 rounded-full blur-3xl"></div>
      </div>

      {/* Navigation */}
      <nav className="relative z-10 container-app">
        <div className="flex items-center justify-between py-4">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-gradient-brand rounded-xl shadow-lg">
              <Target className="h-7 w-7 text-primary-foreground" />
            </div>
            <span className="text-heading-2 gradient-text font-bold">DreamVault</span>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={handleSignIn} className="hidden sm:flex">
              Sign In
            </Button>
            <Button onClick={handleGetStarted} variant="gradient" size="lg">
              Get Started
            </Button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative z-10 container-narrow text-center pt-16 pb-24">
        <div className="spacing-section">
          <div className="flex items-center justify-center gap-3 mb-8">
            <div className="p-2 bg-gradient-to-r from-accent to-secondary rounded-full">
              <Target className="h-6 w-6 text-white" />
            </div>
            <span className="text-body font-medium text-muted-foreground uppercase tracking-wider bg-white/10 px-4 py-2 rounded-full backdrop-blur-sm">
              Your Ultimate Bucket List
            </span>
          </div>
          
          <h1 className="text-6xl md:text-8xl font-bold mb-8 leading-tight">
            <span className="gradient-text">Create Your</span>
            <br />
            <span className="gradient-text-secondary">Dream Bucket List</span>
          </h1>
          
          <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-4xl mx-auto leading-relaxed">
            Track adventures, career goals, travel dreams, and personal milestones. 
            DreamVault helps you organize, achieve, and celebrate your <span className="gradient-text font-semibold">life's greatest experiences</span>.
          </p>

          {/* Sample Bucket List Items Preview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-5xl mx-auto mb-12">
            <Card className="glass-card hover:scale-105 transition-all duration-300 text-left">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div className="p-1 bg-blue-100 rounded-full">
                    <Globe className="h-4 w-4 text-blue-600" />
                  </div>
                  <span className="text-sm font-medium text-blue-600">Travel</span>
                </div>
                <h3 className="font-semibold mb-1">Visit Machu Picchu</h3>
                <p className="text-sm text-muted-foreground">Experience ancient Incan ruins</p>
                <div className="mt-2 flex items-center gap-1">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  <span className="text-xs text-green-600">In Progress</span>
                </div>
              </CardContent>
            </Card>

            <Card className="glass-card hover:scale-105 transition-all duration-300 text-left">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div className="p-1 bg-purple-100 rounded-full">
                    <Heart className="h-4 w-4 text-purple-600" />
                  </div>
                  <span className="text-sm font-medium text-purple-600">Personal</span>
                </div>
                <h3 className="font-semibold mb-1">Learn a New Language</h3>
                <p className="text-sm text-muted-foreground">Master Spanish fluently</p>
                <div className="mt-2 flex items-center gap-1">
                  <Trophy className="h-3 w-3 text-green-500" />
                  <span className="text-xs text-green-600">Completed</span>
                </div>
              </CardContent>
            </Card>

            <Card className="glass-card hover:scale-105 transition-all duration-300 text-left">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div className="p-1 bg-orange-100 rounded-full">
                    <Rocket className="h-4 w-4 text-orange-600" />
                  </div>
                  <span className="text-sm font-medium text-orange-600">Adventure</span>
                </div>
                <h3 className="font-semibold mb-1">Run a Marathon</h3>
                <p className="text-sm text-muted-foreground">Complete 26.2 miles</p>
                <div className="mt-2 flex items-center gap-1">
                  <Star className="h-3 w-3 text-yellow-500" />
                  <span className="text-xs text-yellow-600">Planning</span>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-16">
            <Button size="xl" onClick={handleGetStarted} variant="gradient" className="text-lg px-12 py-6 shadow-2xl hover:shadow-primary/25">
              <Target className="mr-3 h-6 w-6" />
              Start Your Bucket List
              <ArrowRight className="ml-3 h-6 w-6" />
            </Button>
            <Button variant="glass" size="xl" onClick={handleSignIn} className="text-lg px-12 py-6">
              <ShieldCheck className="mr-3 h-6 w-6" />
              Sign In
            </Button>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-4xl font-bold gradient-text mb-2">10K+</div>
              <div className="text-muted-foreground">Bucket List Items Completed</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold gradient-text mb-2">25K+</div>
              <div className="text-muted-foreground">Goal Achievers</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold gradient-text mb-2">50K+</div>
              <div className="text-muted-foreground">Dreams in Progress</div>
            </div>
          </div>

        </div>
      </section>

      {/* Bucket List Categories */}
      <section className="relative z-10 container-app py-24">
        <div className="text-center mb-16">
          <h2 className="text-heading-1 mb-6 gradient-text">Organize Your Dreams by Category</h2>
          <p className="text-body-large text-muted-foreground max-w-2xl mx-auto">
            From travel adventures to personal milestones, categorize and track every aspect of your bucket list
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
          <Card className="text-center group hover:scale-105 transition-all duration-300 p-6">
            <div className="p-3 bg-blue-100 rounded-2xl w-fit mx-auto mb-4 group-hover:scale-110 transition-transform">
              <Globe className="h-8 w-8 text-blue-600" />
            </div>
            <h3 className="font-semibold text-blue-600 mb-1">Travel</h3>
            <p className="text-sm text-muted-foreground">Explore the world</p>
          </Card>

          <Card className="text-center group hover:scale-105 transition-all duration-300 p-6">
            <div className="p-3 bg-orange-100 rounded-2xl w-fit mx-auto mb-4 group-hover:scale-110 transition-transform">
              <Rocket className="h-8 w-8 text-orange-600" />
            </div>
            <h3 className="font-semibold text-orange-600 mb-1">Adventure</h3>
            <p className="text-sm text-muted-foreground">Push your limits</p>
          </Card>

          <Card className="text-center group hover:scale-105 transition-all duration-300 p-6">
            <div className="p-3 bg-green-100 rounded-2xl w-fit mx-auto mb-4 group-hover:scale-110 transition-transform">
              <Target className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="font-semibold text-green-600 mb-1">Career</h3>
            <p className="text-sm text-muted-foreground">Professional goals</p>
          </Card>

          <Card className="text-center group hover:scale-105 transition-all duration-300 p-6">
            <div className="p-3 bg-purple-100 rounded-2xl w-fit mx-auto mb-4 group-hover:scale-110 transition-transform">
              <Heart className="h-8 w-8 text-purple-600" />
            </div>
            <h3 className="font-semibold text-purple-600 mb-1">Personal</h3>
            <p className="text-sm text-muted-foreground">Self improvement</p>
          </Card>

          <Card className="text-center group hover:scale-105 transition-all duration-300 p-6">
            <div className="p-3 bg-pink-100 rounded-2xl w-fit mx-auto mb-4 group-hover:scale-110 transition-transform">
              <Users className="h-8 w-8 text-pink-600" />
            </div>
            <h3 className="font-semibold text-pink-600 mb-1">Social</h3>
            <p className="text-sm text-muted-foreground">Relationships</p>
          </Card>

          <Card className="text-center group hover:scale-105 transition-all duration-300 p-6">
            <div className="p-3 bg-yellow-100 rounded-2xl w-fit mx-auto mb-4 group-hover:scale-110 transition-transform">
              <Star className="h-8 w-8 text-yellow-600" />
            </div>
            <h3 className="font-semibold text-yellow-600 mb-1">Learning</h3>
            <p className="text-sm text-muted-foreground">New skills</p>
          </Card>
        </div>
      </section>

      {/* Mobile App Showcase */}
      <section className="relative z-10 container-app py-24 bg-gradient-hero/10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-heading-1 mb-6 gradient-text">Take Your Dreams Anywhere</h2>
            <p className="text-body-large text-muted-foreground mb-8">
              Access your bucket list on the go with our beautiful mobile app. Track progress, 
              add new goals, and celebrate achievements wherever you are.
            </p>
            
            <div className="space-y-6">
              <div className="flex items-start gap-4">
                <div className="p-2 bg-gradient-brand rounded-lg">
                  <CheckCircle className="h-6 w-6 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="font-semibold mb-1">Offline Access</h3>
                  <p className="text-muted-foreground">View and edit your bucket list even without internet</p>
                </div>
              </div>
              
              <div className="flex items-start gap-4">
                <div className="p-2 bg-gradient-brand rounded-lg">
                  <Target className="h-6 w-6 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="font-semibold mb-1">Smart Reminders</h3>
                  <p className="text-muted-foreground">Get notified about upcoming goals and milestones</p>
                </div>
              </div>
              
              <div className="flex items-start gap-4">
                <div className="p-2 bg-gradient-brand rounded-lg">
                  <Heart className="h-6 w-6 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="font-semibold mb-1">Share Achievements</h3>
                  <p className="text-muted-foreground">Celebrate your wins with friends and family</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="relative">
            {/* Mobile Mockup */}
            <div className="relative mx-auto w-64 h-96 bg-gray-900 rounded-3xl p-2 shadow-2xl">
              <div className="w-full h-full bg-white rounded-2xl overflow-hidden">
                {/* Mock Mobile Interface */}
                <div className="bg-gradient-brand p-4 text-white">
                  <div className="flex items-center gap-3">
                    <Target className="h-6 w-6" />
                    <span className="font-semibold">DreamVault</span>
                  </div>
                </div>
                
                <div className="p-4 space-y-3">
                  <div className="bg-blue-50 p-3 rounded-lg border-l-4 border-blue-500">
                    <div className="flex items-center gap-2 mb-1">
                      <Globe className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-600">Travel</span>
                    </div>
                    <p className="font-semibold text-sm">Visit Japan</p>
                    <p className="text-xs text-gray-600">Experience cherry blossoms</p>
                  </div>
                  
                  <div className="bg-green-50 p-3 rounded-lg border-l-4 border-green-500">
                    <div className="flex items-center gap-2 mb-1">
                      <Trophy className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium text-green-600">Completed</span>
                    </div>
                    <p className="font-semibold text-sm">Learn Photography</p>
                    <p className="text-xs text-gray-600">Master the basics</p>
                  </div>
                  
                  <div className="bg-purple-50 p-3 rounded-lg border-l-4 border-purple-500">
                    <div className="flex items-center gap-2 mb-1">
                      <Heart className="h-4 w-4 text-purple-600" />
                      <span className="text-sm font-medium text-purple-600">Personal</span>
                    </div>
                    <p className="font-semibold text-sm">Read 50 Books</p>
                    <p className="text-xs text-gray-600">Expand knowledge</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Feature Preview */}
      <section className="relative z-10 container-app py-24">
        <div className="text-center mb-16">
          <h2 className="text-heading-1 mb-6 gradient-text">Everything You Need to Achieve Your Dreams</h2>
          <p className="text-body-large text-muted-foreground max-w-2xl mx-auto">
            Powerful bucket list features designed to help you track, achieve, and celebrate life's greatest experiences
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <Card className="text-center group hover:scale-105 transition-all duration-300">
            <CardHeader>
              <div className="p-4 bg-gradient-brand rounded-2xl w-fit mx-auto mb-6 group-hover:scale-110 transition-transform">
                <Target className="h-10 w-10 text-primary-foreground" />
              </div>
              <CardTitle className="text-heading-3 gradient-text">Smart Goal Organization</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-body">
                Organize your bucket list with categories, priorities, and deadlines. 
                Track progress and stay motivated on your journey.
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="text-center group hover:scale-105 transition-all duration-300">
            <CardHeader>
              <div className="p-4 bg-gradient-to-r from-warning via-accent to-warning rounded-2xl w-fit mx-auto mb-6 group-hover:scale-110 transition-transform">
                <Trophy className="h-10 w-10 text-warning-foreground" />
              </div>
              <CardTitle className="text-heading-3 gradient-text">Achievement Tracking</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-body">
                Celebrate every milestone with our achievement system. 
                Share your wins and inspire others in the community.
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="text-center group hover:scale-105 transition-all duration-300">
            <CardHeader>
              <div className="p-4 bg-gradient-secondary rounded-2xl w-fit mx-auto mb-6 group-hover:scale-110 transition-transform">
                <Users className="h-10 w-10 text-secondary-foreground" />
              </div>
              <CardTitle className="text-heading-3 gradient-text">Dream Community</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-body">
                Connect with fellow dreamers, share bucket list ideas, 
                and get inspired by others' amazing journeys.
              </CardDescription>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Success Stories */}
      <section className="relative z-10 container-app py-24 bg-muted/30">
        <div className="text-center mb-16">
          <h2 className="text-heading-1 mb-6 gradient-text">Real Dreams, Real Achievements</h2>
          <p className="text-body-large text-muted-foreground max-w-2xl mx-auto">
            See how DreamVault users are turning their bucket list dreams into incredible life experiences
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <Card className="text-left">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 bg-gradient-brand rounded-full flex items-center justify-center text-white font-semibold">
                  S
                </div>
                <div>
                  <h4 className="font-semibold">Sarah M.</h4>
                  <p className="text-sm text-muted-foreground">Adventure Seeker</p>
                </div>
              </div>
              <p className="text-muted-foreground mb-4">
                "I completed 15 items from my bucket list last year! DreamVault helped me organize everything from skydiving to learning Italian."
              </p>
              <div className="flex items-center gap-2">
                <Trophy className="h-4 w-4 text-yellow-500" />
                <span className="text-sm font-medium">15 Dreams Achieved</span>
              </div>
            </CardContent>
          </Card>

          <Card className="text-left">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 bg-gradient-secondary rounded-full flex items-center justify-center text-white font-semibold">
                  M
                </div>
                <div>
                  <h4 className="font-semibold">Mike R.</h4>
                  <p className="text-sm text-muted-foreground">Travel Enthusiast</p>
                </div>
              </div>
              <p className="text-muted-foreground mb-4">
                "Visited 23 countries in 2 years! The mobile app was perfect for tracking my adventures on the road."
              </p>
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">23 Countries Explored</span>
              </div>
            </CardContent>
          </Card>

          <Card className="text-left">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-accent to-warning rounded-full flex items-center justify-center text-white font-semibold">
                  E
                </div>
                <div>
                  <h4 className="font-semibold">Emma L.</h4>
                  <p className="text-sm text-muted-foreground">Career Builder</p>
                </div>
              </div>
              <p className="text-muted-foreground mb-4">
                "Got my dream job and ran my first marathon! Having clear goals and tracking progress made all the difference."
              </p>
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">12 Goals Completed</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 container-narrow text-center py-24">
        <Card className="bg-gradient-hero border-0 shadow-2xl text-white relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-secondary/20 to-accent/20 backdrop-blur-sm"></div>
          <CardContent className="relative z-10 p-16">
            <div className="flex items-center justify-center gap-3 mb-6">
              <Target className="h-8 w-8" />
              <Trophy className="h-8 w-8" />
              <Heart className="h-8 w-8" />
            </div>
            <h2 className="text-heading-1 mb-6 text-white">Ready to Build Your Ultimate Bucket List?</h2>
            <p className="text-xl text-white/80 mb-8 max-w-2xl mx-auto">
              Join thousands of dreamers who are already turning their bucket lists into reality. 
              Start your journey to an extraordinary life today.
            </p>
            <Button size="xl" onClick={handleGetStarted} variant="glass" className="text-lg px-12 py-6 bg-white/20 hover:bg-white/30 text-white border-white/30">
              <Target className="mr-3 h-6 w-6" />
              Create My Bucket List
              <ArrowRight className="ml-3 h-6 w-6" />
            </Button>
          </CardContent>
        </Card>
      </section>

      {/* Footer */}
      <footer className="relative z-10 border-t glass-subtle">
        <div className="container-app py-8">
          <div className="flex items-center justify-center">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-brand rounded-lg">
                <Target className="h-5 w-5 text-primary-foreground" />
              </div>
              <span className="text-heading-3 gradient-text">DreamVault</span>
              <span className="text-muted-foreground">- Your Bucket List Companion</span>
            </div>
          </div>
        </div>
      </footer>

      {/* Auth Modal */}
      {showAuth && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/60 backdrop-blur-sm">
          <div className="relative w-full max-w-md mx-auto animate-scale-in">
            <Button
              variant="ghost"
              size="icon"
              className="absolute -top-4 -right-4 z-10 bg-white rounded-full shadow-lg hover:bg-gray-100"
              onClick={() => setShowAuth(false)}
            >
              <X className="h-5 w-5" />
            </Button>
            <div className="bg-white rounded-2xl shadow-2xl border-0 overflow-hidden">
              <AuthForm onSuccess={handleAuthSuccess} />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
