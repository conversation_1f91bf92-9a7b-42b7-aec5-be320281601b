// API Response types that match the actual backend response structure
// These differ from the shared types which represent the ideal data structure

import { ItemCategory, ItemPriority, ItemStatus } from '@dreamvault/types'

export interface Location {
  name: string;
  latitude?: number;
  longitude?: number;
  address?: string;
  country?: string;
  city?: string;
}

export interface MediaFile {
  id: string;
  fileUrl: string;
  fileType: string;
  caption?: string;
  uploadedAt: string;
}

export interface ProgressEntry {
  id: string;
  entryDate: string;
  description: string;
  entryType: string;
}

export interface Milestone {
  id: string;
  title: string;
  description?: string;
  completedAt?: string;
}

// This interface matches the actual API response structure
export interface BucketListItemApiResponse {
  id: string;
  title: string;
  description?: string;
  category: ItemCategory;
  priority: ItemPriority;
  status: ItemStatus;
  targetDate?: string;
  estimatedCost?: number;
  location?: Location;
  tags: string[];
  completionPercentage: number; // Note: This is flat, not nested in progress object
  completedAt?: string;
  createdAt: string;
  updatedAt: string;
  mediaFiles: MediaFile[];
  progressEntries: ProgressEntry[];
  milestones: Milestone[];
}

export interface BucketListItemsResponse {
  items: BucketListItemApiResponse[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

export interface SingleBucketListItemResponse {
  item: BucketListItemApiResponse;
}

export interface ApiError {
  error: {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
    requestId: string;
  };
}
