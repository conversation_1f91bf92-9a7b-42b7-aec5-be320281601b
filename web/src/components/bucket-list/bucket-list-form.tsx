'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Loader2, 
  Save, 
  X, 
  Calendar, 
  MapPin, 
  Target,
  Star,
  Upload,
  Image as ImageIcon
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { BucketListItem } from './bucket-list-item'

interface BucketListFormProps {
  item?: Partial<BucketListItem>
  onSave: (data: Partial<BucketListItem>) => Promise<void>
  onCancel: () => void
  loading?: boolean
  className?: string
}

export function BucketListForm({
  item,
  onSave,
  onCancel,
  loading = false,
  className
}: BucketListFormProps) {
  const [formData, setFormData] = useState({
    title: item?.title || '',
    description: item?.description || '',
    category: item?.category || 'personal',
    status: item?.status || 'planning',
    priority: item?.priority || 'medium',
    target_date: item?.target_date ? item.target_date.split('T')[0] : '',
    location: item?.location || '',
  })
  
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  const categories = [
    { value: 'travel', label: 'Travel', icon: '✈️' },
    { value: 'career', label: 'Career', icon: '💼' },
    { value: 'personal', label: 'Personal', icon: '🌟' },
    { value: 'relationships', label: 'Relationships', icon: '❤️' },
    { value: 'adventures', label: 'Adventures', icon: '🏔️' },
    { value: 'learning', label: 'Learning', icon: '📚' },
  ]

  const statuses = [
    { value: 'planning', label: 'Planning', icon: '📋' },
    { value: 'in_progress', label: 'In Progress', icon: '🚀' },
    { value: 'completed', label: 'Completed', icon: '✅' },
    { value: 'paused', label: 'Paused', icon: '⏸️' },
  ]

  const priorities = [
    { value: 'low', label: 'Low Priority', icon: '🔵' },
    { value: 'medium', label: 'Medium Priority', icon: '🟡' },
    { value: 'high', label: 'High Priority', icon: '🔴' },
  ]

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required'
    } else if (formData.title.length < 3) {
      newErrors.title = 'Title must be at least 3 characters'
    } else if (formData.title.length > 100) {
      newErrors.title = 'Title must be less than 100 characters'
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters'
    }

    if (formData.target_date) {
      const targetDate = new Date(formData.target_date)
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      if (targetDate < today) {
        newErrors.target_date = 'Target date cannot be in the past'
      }
    }

    if (formData.location && formData.location.length > 100) {
      newErrors.location = 'Location must be less than 100 characters'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setMessage(null)

    if (!validateForm()) {
      return
    }

    try {
      const submitData = {
        ...formData,
        target_date: formData.target_date ? new Date(formData.target_date).toISOString() : undefined,
        category: formData.category as 'TRAVEL' | 'CAREER' | 'PERSONAL' | 'RELATIONSHIPS' | 'ADVENTURES' | 'LEARNING',
        status: formData.status as 'PLAYING' | 'IN_PROGRESS' | 'COMPLETED' | 'PAUSED',
        priority: formData.priority as 'LOW' | 'MEDIUM' | 'HIGH',
      }

      await onSave(submitData)
      setMessage({ type: 'success', text: 'Goal saved successfully!' })
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : 'Failed to save goal' 
      })
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  return (
    <Card className={cn("w-full max-w-2xl mx-auto", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="h-5 w-5 text-blue-600" />
          {item?.id ? 'Edit Goal' : 'Add New Goal'}
        </CardTitle>
        <CardDescription>
          {item?.id 
            ? 'Update your bucket list item details'
            : 'Create a new item for your bucket list'
          }
        </CardDescription>
      </CardHeader>

      <CardContent>
        {message && (
          <Alert className={`mb-6 ${message.type === 'error' ? 'border-red-500' : 'border-green-500'}`}>
            <AlertDescription>{message.text}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="What do you want to achieve?"
              className={errors.title ? 'border-red-500' : ''}
            />
            {errors.title && (
              <p className="text-sm text-red-600">{errors.title}</p>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe your goal in more detail..."
              rows={3}
              className={errors.description ? 'border-red-500' : ''}
            />
            {errors.description && (
              <p className="text-sm text-red-600">{errors.description}</p>
            )}
          </div>

          {/* Category, Status, Priority Row */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category.value} value={category.value}>
                      <span className="flex items-center gap-2">
                        <span>{category.icon}</span>
                        {category.label}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {statuses.map(status => (
                    <SelectItem key={status.value} value={status.value}>
                      <span className="flex items-center gap-2">
                        <span>{status.icon}</span>
                        {status.label}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="priority">Priority</Label>
              <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  {priorities.map(priority => (
                    <SelectItem key={priority.value} value={priority.value}>
                      <span className="flex items-center gap-2">
                        <span>{priority.icon}</span>
                        {priority.label}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Target Date and Location Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="target_date">Target Date</Label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="target_date"
                  type="date"
                  value={formData.target_date}
                  onChange={(e) => handleInputChange('target_date', e.target.value)}
                  className={cn("pl-10", errors.target_date ? 'border-red-500' : '')}
                />
              </div>
              {errors.target_date && (
                <p className="text-sm text-red-600">{errors.target_date}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  placeholder="Where will this happen?"
                  className={cn("pl-10", errors.location ? 'border-red-500' : '')}
                />
              </div>
              {errors.location && (
                <p className="text-sm text-red-600">{errors.location}</p>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button type="submit" disabled={loading} className="flex-1">
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              <Save className="mr-2 h-4 w-4" />
              {item?.id ? 'Update Goal' : 'Create Goal'}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel} disabled={loading}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
