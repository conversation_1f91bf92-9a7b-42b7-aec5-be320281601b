'use client'

import { useState } from 'react'
import { BucketListItemComponent, type BucketListItem } from './bucket-list-item'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Filter, 
  Plus, 
  Grid3X3, 
  List, 
  SortAsc, 
  SortDesc,
  Target
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface BucketListGridProps {
  items: BucketListItem[]
  onItemEdit?: (item: BucketListItem) => void
  onItemDelete?: (id: string) => void
  onItemStatusChange?: (id: string, status: BucketListItem['status']) => void
  onAddNew?: () => void
  loading?: boolean
  className?: string
}

type ViewMode = 'grid' | 'list'
type SortField = 'created_at' | 'updated_at' | 'title' | 'priority' | 'target_date'
type SortOrder = 'asc' | 'desc'

export function BucketListGrid({
  items,
  onItemEdit,
  onItemDelete,
  onItemStatusChange,
  onAddNew,
  loading = false,
  className
}: BucketListGridProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [sortField, setSortField] = useState<SortField>('updated_at')
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc')

  // Filter and sort items
  const filteredAndSortedItems = items
    .filter(item => {
      const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           item.description?.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory
      const matchesStatus = selectedStatus === 'all' || item.status === selectedStatus
      
      return matchesSearch && matchesCategory && matchesStatus
    })
    .sort((a, b) => {
      let aValue: any = a[sortField]
      let bValue: any = b[sortField]

      // Handle priority sorting
      if (sortField === 'priority') {
        const priorityOrder = { HIGH: 3, MEDIUM: 2, LOW: 1 }
        aValue = priorityOrder[a.priority as keyof typeof priorityOrder]
        bValue = priorityOrder[b.priority as keyof typeof priorityOrder]
      }

      // Handle date sorting
      if (sortField === 'created_at' || sortField === 'updated_at' || sortField === 'target_date') {
        aValue = new Date(aValue || 0).getTime()
        bValue = new Date(bValue || 0).getTime()
      }

      // Handle string sorting
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
      }
    })

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'travel', label: 'Travel' },
    { value: 'career', label: 'Career' },
    { value: 'personal', label: 'Personal' },
    { value: 'relationships', label: 'Relationships' },
    { value: 'adventures', label: 'Adventures' },
    { value: 'learning', label: 'Learning' },
  ]

  const statuses = [
    { value: 'all', label: 'All Statuses' },
    { value: 'planning', label: 'Planning' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'completed', label: 'Completed' },
    { value: 'paused', label: 'Paused' },
  ]

  const sortOptions = [
    { value: 'updated_at', label: 'Last Updated' },
    { value: 'created_at', label: 'Date Created' },
    { value: 'title', label: 'Title' },
    { value: 'priority', label: 'Priority' },
    { value: 'target_date', label: 'Target Date' },
  ]

  const getStatusCounts = () => {
    return {
      total: items.length,
      planning: items.filter(item => item.status === 'PLAYING').length,
      in_progress: items.filter(item => item.status === 'IN_PROGRESS').length,
      completed: items.filter(item => item.status === 'COMPLETED').length,
      paused: items.filter(item => item.status === 'PAUSED').length,
    }
  }

  const statusCounts = getStatusCounts()

  if (loading) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header with Stats */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-6 glass rounded-xl border-0 shadow-lg bg-white/60 dark:bg-gray-800/60 backdrop-blur-lg">
        <div>
          <h2 className="text-3xl font-bold gradient-text flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
              <Target className="h-6 w-6 text-white" />
            </div>
            Your Bucket List
          </h2>
          <div className="flex flex-wrap gap-2 mt-3">
            <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 border-0">{statusCounts.total} total</Badge>
            <Badge className="bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300 border-0">{statusCounts.in_progress} in progress</Badge>
            <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 border-0">{statusCounts.completed} completed</Badge>
          </div>
        </div>
        <Button
          onClick={onAddNew}
          className="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
        >
          <Plus className="h-4 w-4" />
          Add New Goal
        </Button>
      </div>

      {/* Filters and Controls */}
      <div className="flex flex-col lg:flex-row gap-4 p-4 glass rounded-xl border-0 shadow-md bg-white/40 dark:bg-gray-800/40 backdrop-blur-md">
        <div className="flex-1 flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search your goals..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Category Filter */}
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map(category => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Status Filter */}
          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              {statuses.map(status => (
                <SelectItem key={status.value} value={status.value}>
                  {status.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* View and Sort Controls */}
        <div className="flex gap-2">
          {/* Sort */}
          <Select value={sortField} onValueChange={(value) => setSortField(value as SortField)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            size="icon"
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
          >
            {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
          </Button>

          {/* View Mode */}
          <div className="flex border rounded-lg">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="rounded-r-none"
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-l-none"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Items Grid/List */}
      {filteredAndSortedItems.length === 0 ? (
        <div className="text-center py-12">
          <Target className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {items.length === 0 ? 'No goals yet' : 'No goals match your filters'}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {items.length === 0 
              ? 'Start building your bucket list by adding your first goal!'
              : 'Try adjusting your search or filters to find what you\'re looking for.'
            }
          </p>
          {items.length === 0 && (
            <Button onClick={onAddNew}>
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Goal
            </Button>
          )}
        </div>
      ) : (
        <div className={cn(
          viewMode === 'grid' 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            : "space-y-4"
        )}>
          {filteredAndSortedItems.map((item) => (
            <BucketListItemComponent
              key={item.id}
              item={item}
              onEdit={onItemEdit}
              onDelete={onItemDelete}
              onStatusChange={onItemStatusChange}
              className={viewMode === 'list' ? 'max-w-none' : ''}
            />
          ))}
        </div>
      )}
    </div>
  )
}
