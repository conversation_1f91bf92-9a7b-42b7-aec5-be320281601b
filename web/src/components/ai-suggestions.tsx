'use client'

import React, { useState } from 'react'
import { Brain, MapPin, Tag, Shield, Sparkles, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

interface AISuggestionsProps {
  title: string
  description: string
  onSuggestionsReceived?: (suggestions: any) => void
}

export function AISuggestions({ title, description, onSuggestionsReceived }: AISuggestionsProps) {
  const [loading, setLoading] = useState(false)
  const [suggestions, setSuggestions] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('smart')

  const callAIEndpoint = async (endpoint: string, data: any) => {
    const response = await fetch(`/api/ai/${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error?.message || 'AI request failed')
    }

    return response.json()
  }

  const generateSmartSuggestions = async () => {
    if (!title.trim()) {
      setError('Please provide a title for your bucket list item')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const result = await callAIEndpoint('smart-suggestions', {
        title: title.trim(),
        description: description.trim() || undefined
      })

      setSuggestions(result.suggestions)
      onSuggestionsReceived?.(result.suggestions)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate suggestions')
    } finally {
      setLoading(false)
    }
  }

  const generateCategorySuggestions = async () => {
    if (!title.trim()) {
      setError('Please provide a title for your bucket list item')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const result = await callAIEndpoint('suggest-categories', {
        title: title.trim(),
        description: description.trim() || undefined
      })

      setSuggestions({ categories: result.suggestions })
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate category suggestions')
    } finally {
      setLoading(false)
    }
  }

  const generateTagSuggestions = async () => {
    if (!title.trim()) {
      setError('Please provide a title for your bucket list item')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const result = await callAIEndpoint('suggest-tags', {
        title: title.trim(),
        description: description.trim() || undefined
      })

      setSuggestions({ tags: result.suggestions })
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate tag suggestions')
    } finally {
      setLoading(false)
    }
  }

  const extractLocations = async () => {
    const text = `${title} ${description}`.trim()
    if (!text) {
      setError('Please provide some text to extract locations from')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const result = await callAIEndpoint('extract-locations', { text })
      setSuggestions({ locations: result.extraction })
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to extract locations')
    } finally {
      setLoading(false)
    }
  }

  const moderateContent = async () => {
    const text = `${title} ${description}`.trim()
    if (!text) {
      setError('Please provide some text to moderate')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const result = await callAIEndpoint('moderate-content', { text })
      setSuggestions({ moderation: result.moderation })
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to moderate content')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          AI-Powered Suggestions
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5 h-14 p-2 bg-gradient-to-r from-white/90 to-white/80 backdrop-blur-sm border-2 border-white/40 shadow-lg">
            <TabsTrigger value="smart" className="flex items-center gap-2 text-sm font-bold py-3 px-3 rounded-xl transition-all duration-300 hover:scale-105">
              <Sparkles className="h-4 w-4" />
              <span className="hidden sm:inline">Smart</span>
            </TabsTrigger>
            <TabsTrigger value="categories" className="flex items-center gap-2 text-sm font-bold py-3 px-3 rounded-xl transition-all duration-300 hover:scale-105">
              <Brain className="h-4 w-4" />
              <span className="hidden sm:inline">Categories</span>
            </TabsTrigger>
            <TabsTrigger value="tags" className="flex items-center gap-2 text-sm font-bold py-3 px-3 rounded-xl transition-all duration-300 hover:scale-105">
              <Tag className="h-4 w-4" />
              <span className="hidden sm:inline">Tags</span>
            </TabsTrigger>
            <TabsTrigger value="locations" className="flex items-center gap-2 text-sm font-bold py-3 px-3 rounded-xl transition-all duration-300 hover:scale-105">
              <MapPin className="h-4 w-4" />
              <span className="hidden sm:inline">Locations</span>
            </TabsTrigger>
            <TabsTrigger value="moderation" className="flex items-center gap-2 text-sm font-bold py-3 px-3 rounded-xl transition-all duration-300 hover:scale-105">
              <Shield className="h-4 w-4" />
              <span className="hidden sm:inline">Moderate</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="smart" className="space-y-4">
            <div>
              <h3 className="font-medium mb-2">Comprehensive AI Analysis</h3>
              <p className="text-sm text-gray-600 mb-4">
                Get categories, tags, locations, and insights all at once.
              </p>
              <Button onClick={generateSmartSuggestions} disabled={loading}>
                {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Sparkles className="h-4 w-4 mr-2" />}
                Generate Smart Suggestions
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="categories" className="space-y-4">
            <div>
              <h3 className="font-medium mb-2">Category Suggestions</h3>
              <p className="text-sm text-gray-600 mb-4">
                Get AI-suggested categories for your bucket list item.
              </p>
              <Button onClick={generateCategorySuggestions} disabled={loading}>
                {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Brain className="h-4 w-4 mr-2" />}
                Suggest Categories
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="tags" className="space-y-4">
            <div>
              <h3 className="font-medium mb-2">Tag Suggestions</h3>
              <p className="text-sm text-gray-600 mb-4">
                Generate relevant tags to help organize your item.
              </p>
              <Button onClick={generateTagSuggestions} disabled={loading}>
                {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Tag className="h-4 w-4 mr-2" />}
                Suggest Tags
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="locations" className="space-y-4">
            <div>
              <h3 className="font-medium mb-2">Location Extraction</h3>
              <p className="text-sm text-gray-600 mb-4">
                Extract and identify locations mentioned in your text.
              </p>
              <Button onClick={extractLocations} disabled={loading}>
                {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <MapPin className="h-4 w-4 mr-2" />}
                Extract Locations
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="moderation" className="space-y-4">
            <div>
              <h3 className="font-medium mb-2">Content Moderation</h3>
              <p className="text-sm text-gray-600 mb-4">
                Check if your content is appropriate and safe.
              </p>
              <Button onClick={moderateContent} disabled={loading}>
                {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Shield className="h-4 w-4 mr-2" />}
                Moderate Content
              </Button>
            </div>
          </TabsContent>
        </Tabs>

        {/* Results Display */}
        {suggestions && (
          <div className="mt-6 space-y-4">
            <h3 className="font-medium">AI Suggestions</h3>
            
            {/* Categories */}
            {suggestions.categories && (
              <div>
                <h4 className="text-sm font-medium mb-2">Categories</h4>
                <div className="flex flex-wrap gap-2">
                  {suggestions.categories.map((cat: any, index: number) => (
                    <Badge key={index} variant="secondary">
                      {cat.category} ({Math.round(cat.confidence * 100)}%)
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Tags */}
            {suggestions.tags && (
              <div>
                <h4 className="text-sm font-medium mb-2">Tags</h4>
                <div className="flex flex-wrap gap-2">
                  {suggestions.tags.map((tag: any, index: number) => (
                    <Badge key={index} variant="outline">
                      {tag.tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Locations */}
            {suggestions.locations && suggestions.locations.locations && (
              <div>
                <h4 className="text-sm font-medium mb-2">Locations</h4>
                <div className="space-y-1">
                  {suggestions.locations.locations.map((loc: any, index: number) => (
                    <div key={index} className="flex items-center gap-2">
                      <MapPin className="h-3 w-3" />
                      <span className="text-sm">{loc.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {loc.type}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Insights */}
            {(suggestions.estimatedDuration || suggestions.estimatedCost || suggestions.bestTimeToVisit || suggestions.difficulty) && (
              <div>
                <h4 className="text-sm font-medium mb-2">Insights</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  {suggestions.estimatedDuration && (
                    <div>
                      <span className="font-medium">Duration:</span> {suggestions.estimatedDuration}
                    </div>
                  )}
                  {suggestions.estimatedCost && (
                    <div>
                      <span className="font-medium">Cost:</span> {suggestions.estimatedCost}
                    </div>
                  )}
                  {suggestions.bestTimeToVisit && (
                    <div>
                      <span className="font-medium">Best Time:</span> {suggestions.bestTimeToVisit}
                    </div>
                  )}
                  {suggestions.difficulty && (
                    <div>
                      <span className="font-medium">Difficulty:</span> {suggestions.difficulty}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Moderation */}
            {suggestions.moderation && (
              <div>
                <h4 className="text-sm font-medium mb-2">Content Moderation</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant={suggestions.moderation.isAppropriate ? "default" : "destructive"}>
                      {suggestions.moderation.isAppropriate ? "Appropriate" : "Flagged"}
                    </Badge>
                    <Badge variant="outline">
                      {suggestions.moderation.severity}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">{suggestions.moderation.explanation}</p>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
