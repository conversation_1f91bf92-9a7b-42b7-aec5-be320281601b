'use client'

import { useState } from 'react'
import { createSupabaseClient } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Mail, Lock, User, Target, CheckCircle, AlertCircle, Eye, EyeOff } from 'lucide-react'

interface AuthFormProps {
  onSuccess?: () => void
}

export function AuthForm({ onSuccess }: AuthFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  const [activeTab, setActiveTab] = useState('signin')
  const supabase = createSupabaseClient()

  const handleSignUp = async (formData: FormData) => {
    setIsLoading(true)
    setMessage(null)

    const email = formData.get('email') as string
    const password = formData.get('password') as string
    const fullName = formData.get('fullName') as string

    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      })

      if (error) {
        setMessage({ type: 'error', text: error.message })
      } else if (data.user) {
        setMessage({ 
          type: 'success', 
          text: 'Check your email for the confirmation link!' 
        })
      }
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: 'An unexpected error occurred. Please try again.' 
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSignIn = async (formData: FormData) => {
    setIsLoading(true)
    setMessage(null)

    const email = formData.get('email') as string
    const password = formData.get('password') as string

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        setMessage({ type: 'error', text: error.message })
      } else if (data.user) {
        setMessage({ type: 'success', text: 'Welcome back!' })
        onSuccess?.()
      }
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: 'An unexpected error occurred. Please try again.' 
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleResetPassword = async (formData: FormData) => {
    setIsLoading(true)
    setMessage(null)

    const email = formData.get('email') as string

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      })

      if (error) {
        setMessage({ type: 'error', text: error.message })
      } else {
        setMessage({ 
          type: 'success', 
          text: 'Check your email for the password reset link!' 
        })
      }
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: 'An unexpected error occurred. Please try again.' 
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <Card className="border-0 shadow-none bg-transparent">
        <CardHeader className="text-center pb-8">
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="p-3 bg-gradient-brand rounded-xl shadow-lg">
              <Target className="h-8 w-8 text-primary-foreground" />
            </div>
          </div>
          <CardTitle className="text-heading-1 gradient-text mb-2">Welcome to DreamVault</CardTitle>
          <CardDescription className="text-body text-muted-foreground">
            Your personal bucket list companion
          </CardDescription>
        </CardHeader>
        <CardContent className="px-8 pb-8">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-8 bg-muted/50 p-1">
              <TabsTrigger value="signin" className="data-[state=active]:bg-gradient-brand data-[state=active]:text-primary-foreground">Sign In</TabsTrigger>
              <TabsTrigger value="signup" className="data-[state=active]:bg-gradient-brand data-[state=active]:text-primary-foreground">Sign Up</TabsTrigger>
              <TabsTrigger value="reset" className="data-[state=active]:bg-gradient-brand data-[state=active]:text-primary-foreground">Reset</TabsTrigger>
            </TabsList>

            {message && (
              <Alert className={`mb-6 ${message.type === 'error' ? 'border-destructive bg-destructive/10' : 'border-success bg-success/10'}`}>
                <div className="flex items-center gap-2">
                  {message.type === 'error' ? (
                    <AlertCircle className="h-4 w-4 text-destructive" />
                  ) : (
                    <CheckCircle className="h-4 w-4 text-success" />
                  )}
                  <AlertDescription className={message.type === 'error' ? 'text-destructive' : 'text-success'}>
                    {message.text}
                  </AlertDescription>
                </div>
              </Alert>
            )}

            <TabsContent value="signin" className="space-y-6">
              <form action={handleSignIn} className="space-y-6">
                <div className="space-y-3">
                  <Label htmlFor="signin-email" className="text-body font-medium">Email Address</Label>
                  <div className="relative">
                    <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                    <Input
                      id="signin-email"
                      name="email"
                      type="email"
                      placeholder="Enter your email address"
                      className="pl-12 py-6 text-body rounded-xl border-2 focus:border-primary transition-colors"
                      required
                    />
                  </div>
                </div>
                <div className="space-y-3">
                  <Label htmlFor="signin-password" className="text-body font-medium">Password</Label>
                  <div className="relative">
                    <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                    <Input
                      id="signin-password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter your password"
                      className="pl-12 pr-12 py-6 text-body rounded-xl border-2 focus:border-primary transition-colors"
                      required
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
                <Button type="submit" variant="gradient" size="lg" className="w-full py-6 text-body font-semibold" disabled={isLoading}>
                  {isLoading && <Loader2 className="mr-2 h-5 w-5 animate-spin" />}
                  Sign In to DreamVault
                </Button>
              </form>
            </TabsContent>

            <TabsContent value="signup" className="space-y-6">
              <form action={handleSignUp} className="space-y-5">
                <div className="space-y-3">
                  <Label htmlFor="signup-name" className="text-body font-medium">Full Name</Label>
                  <div className="relative">
                    <User className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                    <Input
                      id="signup-name"
                      name="fullName"
                      type="text"
                      placeholder="Enter your full name"
                      className="pl-12 py-6 text-body rounded-xl border-2 focus:border-primary transition-colors"
                      required
                    />
                  </div>
                </div>
                <div className="space-y-3">
                  <Label htmlFor="signup-email" className="text-body font-medium">Email Address</Label>
                  <div className="relative">
                    <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                    <Input
                      id="signup-email"
                      name="email"
                      type="email"
                      placeholder="Enter your email address"
                      className="pl-12 py-6 text-body rounded-xl border-2 focus:border-primary transition-colors"
                      required
                    />
                  </div>
                </div>
                <div className="space-y-3">
                  <Label htmlFor="signup-password" className="text-body font-medium">Password</Label>
                  <div className="relative">
                    <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                    <Input
                      id="signup-password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Create a secure password"
                      className="pl-12 pr-12 py-6 text-body rounded-xl border-2 focus:border-primary transition-colors"
                      minLength={6}
                      required
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                  <p className="text-body-small text-muted-foreground">Password must be at least 6 characters long</p>
                </div>
                <Button type="submit" variant="gradient" size="lg" className="w-full py-6 text-body font-semibold" disabled={isLoading}>
                  {isLoading && <Loader2 className="mr-2 h-5 w-5 animate-spin" />}
                  Create Account
                </Button>
              </form>
            </TabsContent>

            <TabsContent value="reset" className="space-y-6">
              <div className="text-center mb-6">
                <h3 className="text-heading-3 mb-2">Forgot Your Password?</h3>
                <p className="text-body-small text-muted-foreground">
                  No worries! Enter your email address and we'll send you a link to reset your password.
                </p>
              </div>
              <form action={handleResetPassword} className="space-y-6">
                <div className="space-y-3">
                  <Label htmlFor="reset-email" className="text-body font-medium">Email Address</Label>
                  <div className="relative">
                    <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                    <Input
                      id="reset-email"
                      name="email"
                      type="email"
                      placeholder="Enter your email address"
                      className="pl-12 py-6 text-body rounded-xl border-2 focus:border-primary transition-colors"
                      required
                    />
                  </div>
                </div>
                <Button type="submit" variant="gradient" size="lg" className="w-full py-6 text-body font-semibold" disabled={isLoading}>
                  {isLoading && <Loader2 className="mr-2 h-5 w-5 animate-spin" />}
                  Send Reset Link
                </Button>
                <div className="text-center">
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={() => setActiveTab('signin')}
                    className="text-body-small text-muted-foreground hover:text-primary"
                  >
                    Remember your password? Sign in instead
                  </Button>
                </div>
              </form>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
