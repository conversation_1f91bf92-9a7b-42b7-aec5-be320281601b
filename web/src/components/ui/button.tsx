import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cn } from "@/lib/utils"

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | 'gradient' | 'gradient-secondary' | 'premium' | 'glow' | 'glass' | 'neon'
  size?: 'default' | 'sm' | 'lg' | 'xl' | 'icon'
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = "default", size = "default", asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(
          "inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-semibold transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden",
          {
            // Enhanced default with brand gradient
            "bg-gradient-brand text-primary-foreground hover:opacity-90 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-300": variant === "default",

            // Enhanced destructive 
            "bg-destructive text-destructive-foreground hover:opacity-90 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-300": variant === "destructive",

            // Modern outline with glassmorphism
            "border-2 border-primary/20 glass-subtle hover:bg-primary/10 text-primary hover:text-primary/80 shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-300": variant === "outline",

            // Enhanced secondary 
            "bg-secondary text-secondary-foreground hover:opacity-90 shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-300": variant === "secondary",

            // Modern ghost with better hover
            "hover:bg-primary/10 text-primary hover:text-primary/80 transition-all duration-300": variant === "ghost",

            // Enhanced link
            "text-primary hover:text-primary/80 underline-offset-4 hover:underline font-medium transition-all duration-300": variant === "link",

            // Premium gradient with glow
            "bg-gradient-brand text-primary-foreground hover:shadow-2xl hover:shadow-primary/25 transform hover:scale-105 active:scale-95 shadow-xl before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/20 before:to-transparent before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300": variant === "gradient",

            // Secondary gradient
            "bg-gradient-secondary text-secondary-foreground hover:shadow-2xl hover:shadow-secondary/25 transform hover:scale-105 active:scale-95 shadow-xl before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/20 before:to-transparent before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300": variant === "gradient-secondary",

            // Premium luxury button
            "bg-gradient-to-r from-warning via-warning-500 to-warning-600 text-warning-foreground hover:opacity-90 shadow-2xl hover:shadow-warning/30 transform hover:scale-105 active:scale-95 font-bold border border-warning/30 before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/30 before:to-transparent before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300": variant === "premium",

            // Glass morphism effect
            "glass text-foreground hover:bg-opacity-30 shadow-xl hover:shadow-2xl transform hover:scale-[1.02] active:scale-[0.98] before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/10 before:to-transparent before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300": variant === "glass",

            // Glowing effect
            "bg-gradient-brand text-primary-foreground shadow-2xl shadow-primary/50 hover:shadow-primary/60 transform hover:scale-105 active:scale-95 border border-primary/50 before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/20 before:to-transparent before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300": variant === "glow",

            // Neon style
            "bg-gradient-to-r from-secondary via-accent to-secondary text-secondary-foreground shadow-2xl shadow-secondary/50 hover:shadow-accent/60 transform hover:scale-105 active:scale-95 border border-secondary/50 before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/20 before:to-transparent before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300": variant === "neon",
          },
          {
            "h-10 px-4 py-2 text-sm": size === "default",
            "h-8 rounded-lg px-3 text-xs": size === "sm",
            "h-12 rounded-xl px-6 text-base": size === "lg",
            "h-14 rounded-2xl px-8 text-lg font-bold": size === "xl",
            "h-10 w-10 rounded-xl": size === "icon",
          },
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button }