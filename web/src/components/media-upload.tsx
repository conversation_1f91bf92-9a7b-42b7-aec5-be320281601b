'use client'

import React, { useState, useCallback } from 'react'
import Image from 'next/image'
import { useDropzone } from 'react-dropzone'
import { Upload, X, Image as ImageIcon, Video, Music, File, AlertCircle, CheckCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'

interface MediaUploadProps {
  itemId: string
  onUploadComplete?: (files: any[]) => void
  maxFiles?: number
  maxSize?: number
}

interface UploadFile {
  file: File
  id: string
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
  preview?: string
}

export function MediaUpload({ 
  itemId, 
  onUploadComplete, 
  maxFiles = 5, 
  maxSize = 100 * 1024 * 1024 // 100MB
}: MediaUploadProps) {
  const [files, setFiles] = useState<UploadFile[]>([])
  const [caption, setCaption] = useState('')
  const [isUploading, setIsUploading] = useState(false)

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: UploadFile[] = acceptedFiles.map(file => ({
      file,
      id: Math.random().toString(36).substr(2, 9),
      progress: 0,
      status: 'pending',
      preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined
    }))

    setFiles(prev => [...prev, ...newFiles].slice(0, maxFiles))
  }, [maxFiles])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp'],
      'video/*': ['.mp4', '.mov', '.webm'],
      'audio/*': ['.mp3', '.wav', '.webm', '.m4a']
    },
    maxSize,
    maxFiles: maxFiles - files.length,
    disabled: isUploading
  })

  const removeFile = (id: string) => {
    setFiles(prev => {
      const file = prev.find(f => f.id === id)
      if (file?.preview) {
        URL.revokeObjectURL(file.preview)
      }
      return prev.filter(f => f.id !== id)
    })
  }

  const uploadFiles = async () => {
    if (files.length === 0) return

    setIsUploading(true)

    try {
      const formData = new FormData()
      files.forEach(({ file }) => {
        formData.append('files', file)
      })
      
      if (caption.trim()) {
        formData.append('caption', caption.trim())
      }

      // Update progress for all files
      setFiles(prev => prev.map(f => ({ ...f, status: 'uploading' as const })))

      const response = await fetch(`/api/items/${itemId}/media`, {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || 'Upload failed')
      }

      const result = await response.json()

      // Mark all files as successful
      setFiles(prev => prev.map(f => ({ ...f, status: 'success' as const, progress: 100 })))

      // Call completion callback
      onUploadComplete?.(result.mediaFiles)

      // Clear form after successful upload
      setTimeout(() => {
        setFiles([])
        setCaption('')
      }, 2000)

    } catch (error) {
      console.error('Upload failed:', error)
      
      // Mark all files as failed
      setFiles(prev => prev.map(f => ({ 
        ...f, 
        status: 'error' as const, 
        error: error instanceof Error ? error.message : 'Upload failed'
      })))
    } finally {
      setIsUploading(false)
    }
  }

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) return <ImageIcon className="h-4 w-4" />
    if (file.type.startsWith('video/')) return <Video className="h-4 w-4" />
    if (file.type.startsWith('audio/')) return <Music className="h-4 w-4" />
    return <File className="h-4 w-4" />
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Upload Media Files
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Dropzone */}
        <div
          {...getRootProps()}
          className={`
            border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
            ${isDragActive ? 'border-primary bg-primary/5' : 'border-gray-300 hover:border-gray-400'}
            ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}
          `}
        >
          <input {...getInputProps()} />
          <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          {isDragActive ? (
            <p className="text-primary">Drop the files here...</p>
          ) : (
            <div>
              <p className="text-gray-600 mb-1">
                Drag & drop files here, or click to select
              </p>
              <p className="text-sm text-gray-400">
                Images, videos, and audio files up to {formatFileSize(maxSize)}
              </p>
            </div>
          )}
        </div>

        {/* Caption Input */}
        {files.length > 0 && (
          <div className="space-y-2">
            <Label htmlFor="caption">Caption (optional)</Label>
            <Textarea
              id="caption"
              placeholder="Add a caption for your media files..."
              value={caption}
              onChange={(e) => setCaption(e.target.value)}
              disabled={isUploading}
              rows={2}
            />
          </div>
        )}

        {/* File List */}
        {files.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium">Selected Files ({files.length}/{maxFiles})</h4>
            {files.map((uploadFile) => (
              <div key={uploadFile.id} className="flex items-center gap-3 p-3 border rounded-lg">
                {uploadFile.preview ? (
                  <Image
                    src={uploadFile.preview}
                    alt="Preview"
                    className="h-10 w-10 object-cover rounded"
                    width={40}
                    height={40}
                  />
                ) : (
                  <div className="h-10 w-10 bg-gray-100 rounded flex items-center justify-center">
                    {getFileIcon(uploadFile.file)}
                  </div>
                )}
                
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{uploadFile.file.name}</p>
                  <p className="text-xs text-gray-500">{formatFileSize(uploadFile.file.size)}</p>
                  
                  {uploadFile.status === 'uploading' && (
                    <Progress value={uploadFile.progress} className="mt-1" />
                  )}
                  
                  {uploadFile.status === 'error' && uploadFile.error && (
                    <Alert className="mt-1">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription className="text-xs">{uploadFile.error}</AlertDescription>
                    </Alert>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  {uploadFile.status === 'success' && (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  )}
                  {uploadFile.status === 'error' && (
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  )}
                  {!isUploading && uploadFile.status !== 'success' && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(uploadFile.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Upload Button */}
        {files.length > 0 && (
          <Button 
            onClick={uploadFiles} 
            disabled={isUploading || files.every(f => f.status === 'success')}
            className="w-full"
          >
            {isUploading ? 'Uploading...' : 'Upload Files'}
          </Button>
        )}
      </CardContent>
    </Card>
  )
}
