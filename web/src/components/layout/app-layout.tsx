'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Navigation } from './navigation'
import { useAuth } from '@/components/auth/auth-provider'
import { cn } from '@/lib/utils'

interface AppLayoutProps {
  children: React.ReactNode
  className?: string
  requireAuth?: boolean
}

export function AppLayout({ children, className, requireAuth = true }: AppLayoutProps) {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (requireAuth && !user && !loading) {
      router.push('/auth')
    }
  }, [user, loading, requireAuth, router])

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-page flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  // Redirect to auth if user is not authenticated and auth is required
  if (requireAuth && !user) {
    return null // Will redirect via useEffect
  }

  return (
    <div className="min-h-screen bg-page">
      {/* Navigation */}
      {user && <Navigation />}
      
      {/* Main Content */}
      <main className={cn("flex-1", className)}>
        {children}
      </main>
    </div>
  )
}
