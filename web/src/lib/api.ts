import { createSupabaseClient } from './supabase'
import type { BucketListItem } from '@/components/bucket-list/bucket-list-item'

class ApiError extends Error {
  constructor(public status: number, message: string) {
    super(message)
    this.name = 'ApiError'
  }
}

// Helper function to handle Supabase errors
const handleSupabaseError = (error: any, operation: string) => {
  console.error(`Supabase ${operation} error:`, error)
  throw new ApiError(500, error.message || `Failed to ${operation}`)
}

// Bucket List API functions using direct Supabase integration
export const bucketListApi = {
  // Get all bucket list items for the authenticated user
  getAll: async (): Promise<{ items: BucketListItem[] }> => {
    const supabase = createSupabaseClient()

    const { data: items, error } = await supabase
      .from('bucket_list_items')
      .select(`
        *,
        media_files (
          id,
          file_path,
          mime_type,
          file_name
        )
      `)
      .order('updated_at', { ascending: false })

    if (error) {
      handleSupabaseError(error, 'fetch bucket list items')
    }

    return { items: items || [] }
  },

  // Get a specific bucket list item
  getById: async (id: string): Promise<{ item: BucketListItem }> => {
    const supabase = createSupabaseClient()

    const { data: item, error } = await supabase
      .from('bucket_list_items')
      .select(`
        *,
        media_files (
          id,
          file_path,
          mime_type,
          file_name
        )
      `)
      .eq('id', id)
      .single()

    if (error) {
      handleSupabaseError(error, 'fetch bucket list item')
    }

    if (!item) {
      throw new ApiError(404, 'Bucket list item not found')
    }

    return { item }
  },

  // Create a new bucket list item
  create: async (data: Partial<BucketListItem>): Promise<{ item: BucketListItem }> => {
    const supabase = createSupabaseClient()

    // Get current user
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      throw new ApiError(401, 'User not authenticated')
    }

    const { data: item, error } = await supabase
      .from('bucket_list_items')
      .insert({
        ...data,
        user_id: user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      handleSupabaseError(error, 'create bucket list item')
    }

    return { item }
  },

  // Update a bucket list item
  update: async (id: string, data: Partial<BucketListItem>): Promise<{ item: BucketListItem }> => {
    const supabase = createSupabaseClient()

    const { data: item, error } = await supabase
      .from('bucket_list_items')
      .update({
        ...data,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      handleSupabaseError(error, 'update bucket list item')
    }

    return { item }
  },

  // Delete a bucket list item
  delete: async (id: string): Promise<{ message: string }> => {
    const supabase = createSupabaseClient()

    const { error } = await supabase
      .from('bucket_list_items')
      .delete()
      .eq('id', id)

    if (error) {
      handleSupabaseError(error, 'delete bucket list item')
    }

    return { message: 'Bucket list item deleted successfully' }
  },

  // Update status of a bucket list item
  updateStatus: async (id: string, status: BucketListItem['status']): Promise<{ item: BucketListItem }> => {
    return bucketListApi.update(id, { status })
  },
}

// Export the ApiError class for error handling
export { ApiError }