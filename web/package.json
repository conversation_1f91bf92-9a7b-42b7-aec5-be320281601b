{"name": "@dreamvault/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@alloc/quick-lru": "^5.2.0", "@dreamvault/types": "file:../packages/types", "@heroicons/react": "^2.0.18", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.1.5", "@sentry/nextjs": "^9.43.0", "@supabase/ssr": "^0.1.0", "@supabase/supabase-js": "^2.39.0", "@swc/helpers": "^0.5.5", "class-variance-authority": "^0.7.1", "client-only": "^0.0.1", "clsx": "^2.0.0", "date-fns": "^2.30.0", "dlv": "^1.1.3", "eslint-plugin-import": "^2.32.0", "lucide-react": "^0.294.0", "next": "^14.2.25", "next-themes": "^0.4.6", "object-hash": "^3.0.0", "postcss-selector-parser": "^6.1.2", "react": "^18", "react-dom": "^18", "react-dropzone": "^14.3.8", "react-hook-form": "^7.48.2", "sonner": "^2.0.6", "tailwind-merge": "^2.1.0", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.3", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}