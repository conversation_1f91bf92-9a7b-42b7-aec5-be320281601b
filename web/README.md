# 🌐 DreamVault Web Application

A modern, responsive web application for managing bucket lists and achieving life goals. Built with Next.js 14, TypeScript, and a beautiful custom design system featuring dark/light themes and glassmorphism effects.

## 🚀 Features

### 🎨 Modern UI/UX
- **Custom Theme System** - Complete dark/light/system theme support with localStorage persistence
- **Glassmorphism Design** - Beautiful glass effects with backdrop blur
- **Gradient Components** - Custom gradient buttons and backgrounds
- **Responsive Design** - Optimized for desktop, tablet, and mobile devices
- **Accessible Components** - WCAG-compliant with keyboard navigation support

### 🔐 Authentication & Security
- **Auth0 Integration** - Secure authentication with JWT tokens
- **Protected Routes** - Client and server-side route protection
- **User Profile Management** - Complete profile setup and preferences
- **Session Management** - Automatic token refresh and logout handling

### 📱 Core Functionality
- **Bucket List Management** - Create, edit, and organize life goals
- **Progress Tracking** - Visual progress indicators and completion tracking
- **Smart Categorization** - Organize by travel, adventure, career, personal, creative, fitness, education, and social
- **Priority System** - Set and manage priorities from low to urgent
- **Media Uploads** - Attach photos and documents to bucket list items

### 🤖 AI-Powered Features
- **Smart Suggestions** - AI-powered goal recommendations
- **Progress Insights** - Intelligent analysis of achievements
- **Goal Planning** - AI assistance for breaking down complex goals

### 🌐 Social & Sharing
- **List Sharing** - Share bucket lists with friends and family
- **Social Feed** - Discover and get inspired by others' goals
- **Achievement Celebrations** - Share milestones and celebrate together

### 📊 Analytics & Insights
- **Progress Analytics** - Visual insights into goal completion
- **Achievement System** - Gamified experience with badges
- **Timeline View** - Track progress over time
- **Export Options** - Export data in various formats

## 🏗️ Architecture

### Technology Stack
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework with dark mode support
- **Radix UI** - Accessible UI component primitives
- **Zustand** - Lightweight state management
- **React Hook Form** - Performant forms with validation
- **Auth0 Next.js SDK** - Authentication integration
- **Socket.io Client** - Real-time communication
- **Axios** - HTTP client for API requests
- **Lucide React** - Modern icon library

### Project Structure
```
src/
├── app/                    # Next.js App Router pages
│   ├── analytics/         # Analytics dashboard
│   ├── api/               # API routes (Auth0)
│   ├── bucket-list/       # Bucket list management pages
│   ├── dashboard/         # Main dashboard
│   ├── discovery/         # Social discovery features
│   ├── search/            # Search functionality
│   ├── settings/          # User preferences
│   ├── shared-lists/      # Social sharing features
│   ├── globals.css        # Global styles and custom CSS
│   ├── layout.tsx         # Root layout with theme provider
│   └── page.tsx           # Home/landing page
├── components/            # React components
│   ├── layout/           # Layout components
│   │   └── dashboard-layout.tsx # Main app layout
│   ├── social/           # Social feature components
│   │   ├── discovery-item-card.tsx
│   │   └── shared-list-card.tsx
│   ├── ui/               # Reusable UI components
│   │   ├── button.tsx    # Custom button with variants
│   │   ├── card.tsx      # Card component
│   │   ├── input.tsx     # Form input component
│   │   ├── select.tsx    # Select dropdown
│   │   └── ...           # Other UI primitives
│   ├── ai-suggestions.tsx # AI-powered recommendations
│   ├── error-boundary.tsx # Error handling
│   ├── media-upload.tsx   # File upload component
│   ├── providers.tsx      # App providers wrapper
│   └── theme-provider.tsx # Theme context and switching
├── lib/                   # Utilities and configurations
│   ├── api.ts            # API client configuration
│   ├── error-handling.ts # Error handling utilities
│   ├── socket.ts         # Socket.io client setup
│   ├── store.ts          # Zustand state management
│   └── utils.ts          # General utilities (cn helper)
└── types/                # Web-specific type definitions
    └── api.ts            # API response types
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js 18+ and npm
- Auth0 account for authentication
- Backend API server running (see `/backend/README.md`)

### Quick Start

1. **Install dependencies**
   ```bash
   cd web
   npm install
   ```

2. **Environment Configuration**
   
   Create a `.env.local` file in the web directory:
   ```bash
   # Auth0 Configuration
   AUTH0_SECRET='use [openssl rand -hex 32] to generate a 32 bytes value'
   AUTH0_BASE_URL='http://localhost:3000'
   AUTH0_ISSUER_BASE_URL='https://your-domain.auth0.com'
   AUTH0_CLIENT_ID='your-auth0-client-id'
   AUTH0_CLIENT_SECRET='your-auth0-client-secret'
   AUTH0_AUDIENCE='your-api-identifier'

   # API Configuration
   NEXT_PUBLIC_API_BASE_URL='http://localhost:3001/api'
   NEXT_PUBLIC_SOCKET_URL='http://localhost:3001'

   # Feature Flags
   NEXT_PUBLIC_ENABLE_AI_FEATURES='true'
   NEXT_PUBLIC_ENABLE_SOCIAL_FEATURES='true'
   NEXT_PUBLIC_ENABLE_ANALYTICS='true'

   # Sentry (Optional)
   SENTRY_DSN='your-sentry-dsn'
   NEXT_PUBLIC_SENTRY_DSN='your-public-sentry-dsn'
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

   The application will be available at `http://localhost:3000`

## 📜 Available Scripts

### Development
- `npm run dev` - Start development server (port 3000)
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run type-check` - Run TypeScript type checking
- `npm run lint` - Run ESLint with Next.js configuration

## 🎨 Design System

### Theme System
The application features a comprehensive theme system with:
- **Light Theme** - Clean, modern light interface
- **Dark Theme** - Easy-on-eyes dark interface  
- **System Theme** - Automatically follows OS preference
- **Persistent Settings** - Theme preference saved to localStorage

### Custom Components

**Button Component** (`components/ui/button.tsx`)
- Multiple variants: `default`, `destructive`, `outline`, `secondary`, `ghost`, `link`
- Enhanced variants: `gradient`, `gradient-secondary`, `premium`, `glow`, `glass`, `neon`
- Supports `asChild` prop for polymorphic behavior

**Card Components**
- Glassmorphism effects with backdrop-blur
- Gradient borders and shadows
- Responsive design patterns

### Styling Architecture
- **Tailwind CSS** - Utility-first CSS framework
- **Custom CSS Classes** - Additional design system classes in `globals.css`:
  - `.gradient-text` - Gradient text effects
  - `.bg-gradient-*` - Custom gradient backgrounds
  - `.shadow-glow*` - Custom glow effects
  - `.glass*` - Glassmorphism variants
- **Responsive Design** - Mobile-first approach with reduced animations on smaller screens

## 🔌 API Integration

### Authentication Flow
1. User authentication handled by Auth0 Next.js SDK
2. JWT tokens automatically included in API requests
3. Token refresh handled automatically
4. Protected routes redirect to login when needed

### API Client
The centralized API client (`lib/api.ts`) provides:
- Automatic token inclusion
- Request/response interceptors
- Error handling and retries
- TypeScript support with shared types

### Real-time Features
Socket.io client setup for:
- Live notifications
- Real-time updates
- Social features
- Progress synchronization

## 🧪 Testing

### Component Testing
```bash
# Run component tests (when implemented)
npm test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

### Type Safety
```bash
# Type check without emitting files
npm run type-check
```

### Linting
```bash
# Run ESLint
npm run lint

# Fix auto-fixable issues
npm run lint -- --fix
```

## 🚀 Deployment

### Build Process
```bash
# Create optimized production build
npm run build

# Test production build locally
npm run start
```

### Environment Variables for Production
```bash
# Auth0 Production Configuration
AUTH0_SECRET='production-secret-32-bytes'
AUTH0_BASE_URL='https://yourdomain.com'
AUTH0_ISSUER_BASE_URL='https://your-domain.auth0.com'
AUTH0_CLIENT_ID='production-client-id'
AUTH0_CLIENT_SECRET='production-client-secret'
AUTH0_AUDIENCE='production-api-identifier'

# API Configuration
NEXT_PUBLIC_API_BASE_URL='https://api.yourdomain.com/api'
NEXT_PUBLIC_SOCKET_URL='https://api.yourdomain.com'

# Feature Flags
NEXT_PUBLIC_ENABLE_AI_FEATURES='true'
NEXT_PUBLIC_ENABLE_SOCIAL_FEATURES='true'
NEXT_PUBLIC_ENABLE_ANALYTICS='true'

# Monitoring
SENTRY_DSN='production-sentry-dsn'
NEXT_PUBLIC_SENTRY_DSN='production-public-sentry-dsn'
```

### Deployment Platforms

**Vercel (Recommended)**
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy to Vercel
vercel

# Set environment variables in Vercel dashboard
```

**Docker**
```bash
# Build Docker image
docker build -t dreamvault-web .

# Run container
docker run -p 3000:3000 dreamvault-web
```

## 🔧 Configuration

### Next.js Configuration
The `next.config.js` includes:
- Sentry integration
- Image optimization settings
- Experimental features for performance
- Environment variable validation

### Tailwind Configuration
Custom Tailwind setup with:
- Extended color palette
- Custom animations
- Glassmorphism utilities
- Dark mode support
- Custom safelist for dynamic classes

## 🤝 Contributing

1. Follow the existing code style and conventions
2. Use TypeScript strict mode
3. Write tests for new components
4. Update documentation as needed
5. Ensure accessibility compliance
6. Test theme switching functionality

### Code Style
- Use meaningful component and variable names
- Follow React best practices
- Implement proper error boundaries
- Use proper TypeScript types from shared package

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](../LICENSE) file for details.

## 🔗 Related Projects

- [Backend API](../backend/README.md) - Express.js API server
- [Mobile App](../mobile/README.md) - React Native + Expo mobile application  
- [Shared Types](../packages/types/README.md) - TypeScript type definitions

---

**Made with ❤️ using Next.js, TypeScript, and modern web technologies**

*Building the future of goal achievement, one dream at a time.*