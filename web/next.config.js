/** @type {import('next').NextConfig} */
const nextConfig = {
  transpilePackages: ['@dreamvault/types'],
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
    NEXT_PUBLIC_SOCKET_URL: process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3001'
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 's.gravatar.com',
        port: '',
        pathname: '/avatar/**',
      },
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'avatars.githubusercontent.com',
        port: '',
        pathname: '/**',
      }
    ],
  },
  // Disable static optimization for development
  ...(process.env.NODE_ENV === 'development' && {
    experimental: {
      esmExternals: false
    }
  }),
  // Disable prerendering for error pages to avoid build issues
  generateBuildId: () => {
    return 'development'
  }
}

module.exports = nextConfig