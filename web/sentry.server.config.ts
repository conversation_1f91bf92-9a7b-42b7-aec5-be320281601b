import * as Sentry from '@sentry/nextjs'

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  
  // Adjust this value in production, or use tracesSampler for greater control
  tracesSampleRate: process.env.NODE_ENV === 'development' ? 1.0 : 0.1,
  
  // Setting this option to true will print useful information to the console while you're setting up Sentry.
  debug: process.env.NODE_ENV === 'development',
  
  // Filter out common non-error events
  beforeSend(event, hint) {
    // Don't send Auth0 callback errors to Sentry
    if (event.exception?.values?.[0]?.value?.includes('auth0')) {
      return null
    }
    
    return event
  },
  
  // Additional context
  initialScope: {
    tags: {
      component: 'web-server'
    }
  }
})
