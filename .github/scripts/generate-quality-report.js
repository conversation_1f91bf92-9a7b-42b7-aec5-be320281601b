#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

class QualityReportGenerator {
  constructor() {
    this.timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    this.reportPath = `.github/quality-reports/quality-report-${this.timestamp}.md`;
    this.projects = ['backend', 'web', 'mobile'];
    this.results = {};
    this.summary = {
      totalIssues: 0,
      criticalIssues: 0,
      passedChecks: 0,
      totalChecks: 0,
      overallStatus: 'PASS'
    };
  }

  async generateReport() {
    console.log('🔍 Generating code quality report...');
    
    // Parse all log files
    this.parseLogFiles();
    
    // Generate the markdown report
    const reportContent = this.buildMarkdownReport();
    
    // Write the report
    fs.writeFileSync(this.reportPath, reportContent);
    
    console.log(`✅ Quality report generated: ${this.reportPath}`);
    console.log(`📊 Overall status: ${this.summary.overallStatus}`);
    console.log(`🔢 Total issues found: ${this.summary.totalIssues}`);
  }

  parseLogFiles() {
    console.log('📋 Parsing quality check results...');
    
    for (const project of this.projects) {
      this.results[project] = {
        typecheck: this.parseCheck(project, 'typecheck'),
        lint: this.parseCheck(project, 'lint'),
        test: this.parseCheck(project, 'test'),
        build: this.parseCheck(project, 'build'),
        audit: this.parseCheck(project, 'audit')
      };
    }
  }

  parseCheck(project, checkType) {
    const result = {
      status: 'PASS',
      exitCode: 0,
      output: '',
      issues: [],
      summary: ''
    };

    try {
      // Read exit code - try multiple possible paths
      const possibleResultPaths = [
        `artifacts/quality-logs-${project}/${project}/${checkType}-result.env`,
        `artifacts/quality-logs-${project}/${checkType}-result.env`
      ];
      
      for (const resultEnvPath of possibleResultPaths) {
        if (fs.existsSync(resultEnvPath)) {
          const envContent = fs.readFileSync(resultEnvPath, 'utf8');
          const exitCodeMatch = envContent.match(/(\w+_)?EXIT_CODE=(\d+)/) || envContent.match(/EXIT_CODE=(\d+)/);
          if (exitCodeMatch) {
            result.exitCode = parseInt(exitCodeMatch[exitCodeMatch.length - 1]);
            result.status = result.exitCode === 0 ? 'PASS' : 'FAIL';
            break;
          }
        }
      }

      // Read output log - try multiple possible paths
      const possibleLogPaths = [
        `artifacts/quality-logs-${project}/${checkType}-${project}.log`,
        `${checkType}-${project}.log`
      ];
      
      let logPath = null;
      for (const path of possibleLogPaths) {
        if (fs.existsSync(path)) {
          logPath = path;
          break;
        }
      }
      if (logPath) {
        result.output = fs.readFileSync(logPath, 'utf8');
        result.issues = this.parseIssuesFromOutput(checkType, result.output);
      }

      // Update summary
      this.summary.totalChecks++;
      if (result.status === 'PASS') {
        this.summary.passedChecks++;
      } else {
        this.summary.totalIssues += result.issues.length;
        if (checkType === 'build' || checkType === 'test') {
          this.summary.criticalIssues += result.issues.length;
        }
      }

    } catch (error) {
      console.warn(`⚠️ Could not parse ${checkType} results for ${project}: ${error.message}`);
      result.status = 'UNKNOWN';
      result.summary = `Could not parse results: ${error.message}`;
    }

    return result;
  }

  parseIssuesFromOutput(checkType, output) {
    const issues = [];
    
    switch (checkType) {
      case 'typecheck':
        // Parse TypeScript errors
        const tsErrors = output.match(/(.+\.tsx?)\((\d+),(\d+)\): error TS\d+: (.+)/g) || [];
        tsErrors.forEach(error => {
          const match = error.match(/(.+\.tsx?)\((\d+),(\d+)\): error TS(\d+): (.+)/);
          if (match) {
            issues.push({
              file: match[1],
              line: match[2],
              column: match[3],
              code: `TS${match[4]}`,
              message: match[5],
              severity: 'error'
            });
          }
        });
        break;

      case 'lint':
        // Parse ESLint errors
        const lintLines = output.split('\n');
        let currentFile = '';
        lintLines.forEach(line => {
          if (line.includes('✖') && line.includes('problems')) {
            // Summary line - extract counts
            const match = line.match(/(\d+) problems? \((\d+) errors?, (\d+) warnings?\)/);
            if (match) {
              issues.push({
                type: 'summary',
                problems: match[1],
                errors: match[2],
                warnings: match[3]
              });
            }
          } else if (line.match(/^\s*\d+:\d+/)) {
            // Individual lint issue
            const match = line.match(/^\s*(\d+):(\d+)\s+(error|warning|info)\s+(.+?)\s+([a-z\-@/]+)$/);
            if (match) {
              issues.push({
                file: currentFile,
                line: match[1],
                column: match[2],
                severity: match[3],
                message: match[4],
                rule: match[5]
              });
            }
          } else if (line.trim() && !line.startsWith(' ') && line.includes('.')) {
            currentFile = line.trim();
          }
        });
        break;

      case 'test':
        // Parse test failures
        const testFailures = output.match(/FAIL .+/g) || [];
        const failedTests = output.match(/✕ .+/g) || [];
        
        testFailures.forEach(failure => {
          issues.push({
            type: 'test_failure',
            message: failure,
            severity: 'error'
          });
        });
        
        failedTests.forEach(test => {
          issues.push({
            type: 'failed_test',
            message: test,
            severity: 'error'
          });
        });
        break;

      case 'build':
        // Parse build errors
        const buildErrors = output.match(/ERROR.+/g) || [];
        const buildWarnings = output.match(/WARNING.+/g) || [];
        
        buildErrors.forEach(error => {
          issues.push({
            type: 'build_error',
            message: error,
            severity: 'error'
          });
        });
        
        buildWarnings.forEach(warning => {
          issues.push({
            type: 'build_warning',
            message: warning,
            severity: 'warning'
          });
        });
        break;

      case 'audit':
        // Parse security audit issues
        const auditIssues = output.match(/found \d+ vulnerabilities/g) || [];
        if (auditIssues.length > 0) {
          const match = output.match(/found (\d+) vulnerabilities \((\d+) low, (\d+) moderate, (\d+) high, (\d+) critical\)/);
          if (match) {
            issues.push({
              type: 'security_vulnerabilities',
              total: match[1],
              low: match[2],
              moderate: match[3],
              high: match[4],
              critical: match[5],
              severity: parseInt(match[5]) > 0 ? 'critical' : parseInt(match[4]) > 0 ? 'high' : 'moderate'
            });
          }
        }
        break;
    }

    return issues;
  }

  buildMarkdownReport() {
    // Determine overall status
    this.summary.overallStatus = this.summary.criticalIssues > 0 ? 'FAIL' : 
                                 this.summary.totalIssues > 0 ? 'PASS_WITH_WARNINGS' : 'PASS';

    const statusEmoji = {
      'PASS': '✅',
      'PASS_WITH_WARNINGS': '⚠️',
      'FAIL': '❌'
    };

    const report = `# 🔍 Code Quality Report

**Generated:** ${new Date().toISOString()}  
**Commit:** \`${process.env.GITHUB_SHA?.substring(0, 7) || 'unknown'}\`  
**Branch:** \`${process.env.GITHUB_REF?.replace('refs/heads/', '') || 'unknown'}\`  
**Triggered by:** ${process.env.GITHUB_ACTOR || 'unknown'}  

## Executive Summary

${statusEmoji[this.summary.overallStatus]} **Overall Status:** ${this.summary.overallStatus}

| Metric | Value |
|--------|-------|
| Total Checks | ${this.summary.totalChecks} |
| Passed Checks | ${this.summary.passedChecks} |
| Total Issues | ${this.summary.totalIssues} |
| Critical Issues | ${this.summary.criticalIssues} |
| Success Rate | ${((this.summary.passedChecks / this.summary.totalChecks) * 100).toFixed(1)}% |

${this.buildProjectSummaryTable()}

## Detailed Results

${this.projects.map(project => this.buildProjectSection(project)).join('\n\n')}

## Recommendations

${this.buildRecommendations()}

---

*This report was generated automatically by the DreamVault CI/CD pipeline.*
`;

    return report;
  }

  buildProjectSummaryTable() {
    const table = `
| Project | TypeScript | Lint | Tests | Build | Security |
|---------|------------|------|-------|-------|----------|
${this.projects.map(project => {
  const r = this.results[project];
  return `| **${project}** | ${this.getStatusIcon(r.typecheck.status)} | ${this.getStatusIcon(r.lint.status)} | ${this.getStatusIcon(r.test.status)} | ${this.getStatusIcon(r.build.status)} | ${this.getStatusIcon(r.audit.status)} |`;
}).join('\n')}
`;
    return table;
  }

  getStatusIcon(status) {
    switch (status) {
      case 'PASS': return '✅';
      case 'FAIL': return '❌';
      case 'UNKNOWN': return '❓';
      default: return '⚪';
    }
  }

  buildProjectSection(project) {
    const r = this.results[project];
    const projectStatus = Object.values(r).every(check => check.status === 'PASS') ? '✅' : '❌';
    
    return `### ${projectStatus} ${project.toUpperCase()} Project

#### Summary
${Object.entries(r).map(([checkType, result]) => 
  `- **${checkType}**: ${this.getStatusIcon(result.status)} ${result.status} (${result.issues.length} issues)`
).join('\n')}

${Object.entries(r).map(([checkType, result]) => 
  result.issues.length > 0 ? this.buildCheckSection(project, checkType, result) : ''
).filter(Boolean).join('\n')}`;
  }

  buildCheckSection(project, checkType, result) {
    if (result.issues.length === 0) return '';

    const sectionTitle = {
      typecheck: 'TypeScript Errors',
      lint: 'Linting Issues',
      test: 'Test Failures',
      build: 'Build Issues',
      audit: 'Security Vulnerabilities'
    };

    let issuesContent = '';
    
    if (result.issues.length <= 10) {
      // Show all issues if 10 or fewer
      issuesContent = result.issues.map(issue => this.formatIssue(issue)).join('\n');
    } else {
      // Show first 10 and summary
      issuesContent = result.issues.slice(0, 10).map(issue => this.formatIssue(issue)).join('\n');
      issuesContent += `\n*... and ${result.issues.length - 10} more issues*`;
    }

    return `
#### ${sectionTitle[checkType]}
${issuesContent}

<details>
<summary>📋 Full Output Log</summary>

\`\`\`
${result.output.substring(0, 2000)}${result.output.length > 2000 ? '\n... (truncated)' : ''}
\`\`\`
</details>
`;
  }

  formatIssue(issue) {
    if (issue.file && issue.line) {
      return `- **${issue.file}:${issue.line}${issue.column ? `:${issue.column}` : ''}** - ${issue.message} ${issue.code ? `(${issue.code})` : ''}`;
    } else if (issue.type === 'security_vulnerabilities') {
      return `- **Security Alert**: ${issue.total} vulnerabilities found (${issue.critical} critical, ${issue.high} high, ${issue.moderate} moderate, ${issue.low} low)`;
    } else {
      return `- ${issue.message}`;
    }
  }

  buildRecommendations() {
    const recommendations = [];

    if (this.summary.criticalIssues > 0) {
      recommendations.push("🚨 **Critical**: Fix build errors and test failures immediately - these prevent deployment.");
    }

    // Check for specific issues
    const hasTypeScriptErrors = Object.values(this.results).some(project => 
      project.typecheck.status === 'FAIL'
    );
    if (hasTypeScriptErrors) {
      recommendations.push("📝 **TypeScript**: Resolve type errors to improve code safety and maintainability.");
    }

    const hasLintIssues = Object.values(this.results).some(project => 
      project.lint.status === 'FAIL'
    );
    if (hasLintIssues) {
      recommendations.push("🎨 **Code Style**: Run `npm run lint -- --fix` to automatically fix formatting issues.");
    }

    const hasSecurityIssues = Object.values(this.results).some(project => 
      project.audit.status === 'FAIL'
    );
    if (hasSecurityIssues) {
      recommendations.push("🔒 **Security**: Run `npm audit fix` to resolve dependency vulnerabilities.");
    }

    if (recommendations.length === 0) {
      recommendations.push("🎉 **Great job!** All quality checks are passing. Keep up the excellent work!");
    }

    return recommendations.map(rec => rec).join('\n\n');
  }
}

// Run the generator
async function main() {
  try {
    const generator = new QualityReportGenerator();
    await generator.generateReport();
    process.exit(0);
  } catch (error) {
    console.error('❌ Failed to generate quality report:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = QualityReportGenerator;