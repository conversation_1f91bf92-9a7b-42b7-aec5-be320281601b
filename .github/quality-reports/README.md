# Quality Reports

This directory contains automated code quality reports generated by the CI/CD pipeline.

## How it works

1. **Trigger**: Quality checks run on every push to `main`/`develop` branches and on pull requests
2. **Analysis**: The workflow checks TypeScript, ESLint, tests, builds, and security for all projects (backend/web/mobile)
3. **Report Generation**: A comprehensive markdown report is generated with:
   - Executive summary with pass/fail status
   - Detailed breakdown by project
   - Specific issues with file locations and line numbers
   - Actionable recommendations for fixes
4. **Delivery**: Reports are uploaded as GitHub artifacts and linked in PR comments

## Report Format

Reports are named `quality-report-[timestamp].md` and include:

- **Executive Summary**: Overall status and key metrics
- **Project Breakdown**: Individual results for backend, web, and mobile
- **Detailed Issues**: Specific problems with file locations
- **Recommendations**: Prioritized action items for fixes

## Accessing Reports

- **Pull Requests**: Summary appears as a comment with link to full report
- **Main Branch**: Reports are committed to this directory
- **GitHub Actions**: Download artifacts from the workflow run

## Quick Commands

Fix common issues:
```bash
# Fix linting issues
npm run lint

# Check TypeScript types
npm run type-check

# Run tests
npm test

# Fix security vulnerabilities
npm audit fix
```