name: Code Quality Check

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch: # Allow manual triggers

env:
  NODE_VERSION: '18'
  REPORT_DIR: '.github/quality-reports'

jobs:
  quality-check:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
      actions: read
    
    strategy:
      fail-fast: false # Continue all checks even if some fail
      matrix:
        project: [backend, web, mobile]
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Cache project dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.npm
            ${{ matrix.project }}/node_modules
          key: ${{ runner.os }}-node-${{ hashFiles(format('{0}/package-lock.json', matrix.project)) }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Create reports directory
        run: mkdir -p ${{ env.REPORT_DIR }}

      - name: Install project dependencies
        run: |
          cd ${{ matrix.project }}
          if ! npm ci; then
            echo "npm ci failed, checking if lock file is out of sync..."
            if npm ci 2>&1 | grep -q "package.json and package-lock.json.*are in sync"; then
              echo "Lock file out of sync, regenerating with npm install..."
              rm -f package-lock.json
              npm install
            else
              echo "Different error, trying with --legacy-peer-deps..."
              npm ci --legacy-peer-deps
            fi
          fi

      - name: Run TypeScript type checking
        id: typecheck
        continue-on-error: true
        run: |
          cd ${{ matrix.project }}
          echo "Running TypeScript type check for ${{ matrix.project }}..."
          npm run type-check > ../typecheck-${{ matrix.project }}.log 2>&1
          echo "TYPE_CHECK_EXIT_CODE=$?" > typecheck-result.env

      - name: Run ESLint
        id: lint
        continue-on-error: true
        run: |
          cd ${{ matrix.project }}
          echo "Running ESLint for ${{ matrix.project }}..."
          if npm run lint --if-present > ../lint-${{ matrix.project }}.log 2>&1; then
            echo "LINT_EXIT_CODE=0" > lint-result.env
          else
            exit_code=$?
            echo "LINT_EXIT_CODE=$exit_code" > lint-result.env
            # Check if it's a configuration issue vs actual lint errors
            if grep -q "couldn't find a configuration file\|No ESLint configuration" ../lint-${{ matrix.project }}.log; then
              echo "ESLint not configured for ${{ matrix.project }}, skipping..." >> ../lint-${{ matrix.project }}.log
              echo "LINT_EXIT_CODE=0" > lint-result.env
            fi
          fi

      - name: Run tests
        id: test
        continue-on-error: true
        run: |
          cd ${{ matrix.project }}
          echo "Running tests for ${{ matrix.project }}..."
          if npm run test --if-present > ../test-${{ matrix.project }}.log 2>&1; then
            echo "TEST_EXIT_CODE=0" > test-result.env
          else
            exit_code=$?
            echo "TEST_EXIT_CODE=$exit_code" > test-result.env
          fi

      - name: Run build
        id: build
        continue-on-error: true
        run: |
          cd ${{ matrix.project }}
          echo "Running build for ${{ matrix.project }}..."
          if npm run build --if-present > ../build-${{ matrix.project }}.log 2>&1; then
            echo "BUILD_EXIT_CODE=0" > build-result.env
          else
            exit_code=$?
            echo "BUILD_EXIT_CODE=$exit_code" > build-result.env
          fi

      - name: Security audit
        id: audit
        continue-on-error: true
        run: |
          cd ${{ matrix.project }}
          echo "Running security audit for ${{ matrix.project }}..."
          npm audit --audit-level=moderate > ../audit-${{ matrix.project }}.log 2>&1
          echo "AUDIT_EXIT_CODE=$?" > audit-result.env

      - name: Upload project logs
        uses: actions/upload-artifact@v4
        with:
          name: quality-logs-${{ matrix.project }}
          path: |
            typecheck-${{ matrix.project }}.log
            lint-${{ matrix.project }}.log
            test-${{ matrix.project }}.log
            build-${{ matrix.project }}.log
            audit-${{ matrix.project }}.log
            ${{ matrix.project }}/typecheck-result.env
            ${{ matrix.project }}/lint-result.env
            ${{ matrix.project }}/test-result.env
            ${{ matrix.project }}/build-result.env
            ${{ matrix.project }}/audit-result.env

  generate-report:
    needs: quality-check
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
      actions: read
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Create reports directory
        run: mkdir -p ${{ env.REPORT_DIR }}

      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts/

      - name: Generate quality report
        run: |
          node .github/scripts/generate-quality-report.js
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GITHUB_REPOSITORY: ${{ github.repository }}
          GITHUB_SHA: ${{ github.sha }}
          GITHUB_REF: ${{ github.ref }}
          GITHUB_ACTOR: ${{ github.actor }}
          GITHUB_EVENT_NAME: ${{ github.event_name }}

      - name: Upload quality report
        uses: actions/upload-artifact@v4
        with:
          name: quality-report
          path: ${{ env.REPORT_DIR }}/quality-report-*.md

      - name: Comment on PR with report summary
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const path = require('path');
            
            // Find the latest report file
            const reportDir = '.github/quality-reports';
            const reportFiles = fs.readdirSync(reportDir).filter(f => f.startsWith('quality-report-'));
            const latestReport = reportFiles.sort().pop();
            
            if (latestReport) {
              const reportPath = path.join(reportDir, latestReport);
              const reportContent = fs.readFileSync(reportPath, 'utf8');
              
              // Extract summary section for PR comment
              const summaryMatch = reportContent.match(/## Executive Summary([\s\S]*?)## /);
              const summary = summaryMatch ? summaryMatch[1].trim() : 'Report generated successfully';
              
              const comment = `## 🔍 Code Quality Report

              ${summary}

              📋 **Full Report**: [Download quality-report artifact](https://github.com/${context.repo.owner}/${context.repo.repo}/actions/runs/${context.runId})

              *Generated on ${new Date().toISOString()}*`;

              await github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            }

      - name: Commit report to repository
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add ${{ env.REPORT_DIR }}/quality-report-*.md
          git commit -m "chore: add automated quality report [skip ci]" || exit 0
          git push