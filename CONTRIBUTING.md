# Contributing to DreamVault

Thank you for your interest in contributing to DreamVault! We welcome contributions from the community and are excited to see what you'll bring to the project.

## 🚀 Getting Started

### Prerequisites

Before you begin, ensure you have:
- Node.js 18+ installed
- PostgreSQL 14+ running
- Git configured
- A code editor (VS Code recommended)

### Development Setup

1. **Fork the repository** on GitHub
2. **Clone your fork** locally:
   ```bash
   git clone https://github.com/YOUR_USERNAME/DreamVault.git
   cd DreamVault
   ```
3. **Add upstream remote**:
   ```bash
   git remote add upstream https://github.com/Stijnus/DreamVault.git
   ```
4. **Install dependencies**:
   ```bash
   # Backend
   cd backend && npm install
   
   # Web
   cd ../web && npm install
   
   # Mobile
   cd ../DreamVaultExpo && npm install
   ```
5. **Set up environment** (see README.md for details)
6. **Run the development servers** (see README.md)

## 🎯 Ways to Contribute

### 🐛 Bug Reports

When filing a bug report, please include:
- Clear description of the issue
- Steps to reproduce
- Expected vs actual behavior
- Screenshots/videos if applicable
- Environment details (OS, browser, Node.js version)
- Relevant error messages or logs

Use the bug report template when creating an issue.

### 💡 Feature Requests

We love new ideas! When suggesting a feature:
- Explain the problem you're trying to solve
- Describe your proposed solution
- Consider alternative solutions
- Think about how it fits with the project goals
- Provide mockups or examples if helpful

### 🔧 Code Contributions

#### Types of Contributions We Welcome:
- Bug fixes
- New features
- Performance improvements
- Documentation improvements
- Test coverage improvements
- UI/UX enhancements
- Mobile app improvements
- API enhancements

#### Before You Start:
1. **Check existing issues** and pull requests to avoid duplication
2. **Create an issue** to discuss larger changes before implementation
3. **Ask questions** if you're unsure about anything

## 📝 Development Process

### Branch Naming Convention

Use descriptive branch names:
- `feature/bucket-list-sharing` - New features
- `bugfix/auth-token-refresh` - Bug fixes
- `improvement/database-performance` - Performance improvements
- `docs/api-documentation` - Documentation updates

### Commit Message Format

We use conventional commits:
```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```
feat(api): add bucket list sharing endpoints

fix(mobile): resolve auth token refresh issue

docs(readme): update installation instructions

test(backend): add integration tests for items API
```

### Pull Request Process

1. **Update your fork**:
   ```bash
   git checkout main
   git pull upstream main
   git push origin main
   ```

2. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make your changes** following our coding standards

4. **Write or update tests** as needed

5. **Run the test suite**:
   ```bash
   # Backend
   cd backend && npm test
   
   # Web (if applicable)
   cd web && npm run build
   ```

6. **Lint your code**:
   ```bash
   # Backend
   cd backend && npm run lint
   
   # Web
   cd web && npm run lint
   ```

7. **Commit your changes** with conventional commit messages

8. **Push to your fork**:
   ```bash
   git push origin feature/your-feature-name
   ```

9. **Create a pull request** on GitHub

### Pull Request Guidelines

**Your PR should:**
- Have a clear title and description
- Reference any related issues
- Include tests for new functionality
- Update documentation if needed
- Pass all CI checks
- Be reasonably sized (prefer smaller, focused PRs)

**PR Description Template:**
```markdown
## What does this PR do?
Brief description of changes

## Related Issue
Closes #123

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Tests pass locally
- [ ] Added tests for new functionality
- [ ] Manual testing completed

## Screenshots (if applicable)
[Add screenshots for UI changes]

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No new warnings introduced
```

## 🧪 Testing Guidelines

### Backend Testing
- Write unit tests for new functions
- Add integration tests for API endpoints
- Test error conditions and edge cases
- Maintain test coverage above 80%

### Frontend Testing
- Test component rendering
- Test user interactions
- Test error states
- Add accessibility tests where applicable

### Running Tests
```bash
# Backend
cd backend
npm test                    # Run all tests
npm run test:watch         # Watch mode
npm run test:coverage      # Coverage report

# Web
cd web
npm run build              # Test build process
npm run lint               # Lint check
```

## 📋 Code Style Guidelines

### General
- Use TypeScript for type safety
- Follow existing code patterns
- Write self-documenting code
- Add comments for complex logic
- Use meaningful variable names

### Backend (Express.js)
- Use async/await over promises
- Implement proper error handling
- Validate all inputs
- Use Prisma for database operations
- Follow RESTful API conventions

### Frontend (React)
- Use functional components with hooks
- Implement proper error boundaries
- Follow component composition patterns
- Use TypeScript interfaces for props
- Maintain component reusability

### Mobile (React Native)
- Follow React Native best practices
- Use platform-specific code when needed
- Implement proper navigation patterns
- Handle different screen sizes
- Test on both iOS and Android

## 🎨 UI/UX Guidelines

### Design Principles
- Consistent with existing design system
- Accessible (WCAG guidelines)
- Mobile-first responsive design
- Intuitive user flows
- Performance-conscious

### Component Guidelines
- Use existing UI components when possible
- Follow accessibility best practices
- Implement proper loading states
- Handle error states gracefully
- Support keyboard navigation

## 📚 Documentation

### What to Document
- New API endpoints
- Configuration changes
- Breaking changes
- Setup instructions
- Architecture decisions

### Documentation Style
- Clear and concise writing
- Include code examples
- Use proper markdown formatting
- Keep it up to date

## 🔍 Code Review Process

### For Contributors
- Respond promptly to review feedback
- Be open to suggestions and changes
- Ask questions if feedback is unclear
- Update your PR based on feedback

### Review Criteria
- Code quality and style
- Test coverage
- Documentation completeness
- Performance implications
- Security considerations
- Breaking change assessment

## 🚀 Release Process

We follow semantic versioning:
- **Major** (1.0.0): Breaking changes
- **Minor** (0.1.0): New features, backwards compatible
- **Patch** (0.0.1): Bug fixes, backwards compatible

## 💬 Communication

### Discord/Slack
Join our community for real-time discussions

### GitHub Discussions
Use for:
- General questions
- Feature discussions
- Architecture proposals
- Community announcements

### Issues
Use for:
- Bug reports
- Feature requests
- Task tracking

## 🏆 Recognition

We appreciate all contributions! Contributors will be:
- Added to the contributors list
- Mentioned in release notes
- Recognized in the community

## ❓ Questions?

- Check the [README.md](README.md) for setup instructions
- Browse existing [issues](https://github.com/Stijnus/DreamVault/issues)
- Join our community discussions
- Reach out to maintainers

## 📄 License

By contributing to DreamVault, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to DreamVault! 🌟