{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(gh repo create:*)", "Bash(gh repo view:*)", "Bash(git remote add:*)", "Bash(npm run type-check:*)", "Bash(npm run:*)", "Bash(npx tsc:*)", "Bash(find:*)", "Bash(git rm:*)", "Bash(rm:*)", "Bash(cp:*)", "Bash(ls:*)", "Bash(git init:*)", "Bash(git branch:*)", "Bash(npm ls:*)", "Bash(npx:*)", "Bash(npm install)", "Bash(/dev/null)", "<PERSON><PERSON>(cat:*)", "Bash(npm install:*)", "Bash(grep:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(git pull:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "Bash(node:*)", "<PERSON><PERSON>(gh run list:*)", "Bash(gh run view:*)", "Bash(npm cache clean:*)", "Bash(gh run download:*)", "<PERSON><PERSON>(mv:*)", "Bash(cd:*)", "WebFetch(domain:github.com)", "Bash(git fetch:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(touch:*)", "Bash(GITHUB_SHA=test123 GITHUB_REF=refs/heads/main GITHUB_ACTOR=stijnus node .github/scripts/generate-quality-report.js)"], "deny": []}}