#!/bin/bash

# DreamVault Health Check Script
# Comprehensive health check for all DreamVault services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
BACKEND_PORT=3001
WEB_PORT=3000
MOBILE_PORT=19006
POSTGRES_PORT=5432
REDIS_PORT=6379

# Function to print colored output
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

print_header() {
    echo -e "${BLUE}🔍 $1${NC}"
}

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to test HTTP endpoint
test_endpoint() {
    local url=$1
    local name=$2
    local timeout=${3:-5}
    
    if curl -s --max-time $timeout "$url" >/dev/null 2>&1; then
        print_success "$name: Responding"
        return 0
    else
        print_error "$name: Not responding"
        return 1
    fi
}

# Function to test API endpoint with JSON response
test_api_endpoint() {
    local url=$1
    local name=$2
    local timeout=${3:-5}
    
    local response=$(curl -s --max-time $timeout "$url" 2>/dev/null)
    if [ $? -eq 0 ] && echo "$response" | jq . >/dev/null 2>&1; then
        print_success "$name: API responding with valid JSON"
        return 0
    else
        print_error "$name: API not responding or invalid JSON"
        return 1
    fi
}

echo ""
print_header "DreamVault Health Check"
echo "=========================="
echo ""

# Check basic services
print_header "Infrastructure Services"
echo ""

# PostgreSQL
if check_port $POSTGRES_PORT; then
    print_success "PostgreSQL: Running on port $POSTGRES_PORT"
else
    print_error "PostgreSQL: Not running on port $POSTGRES_PORT"
fi

# Redis
if check_port $REDIS_PORT; then
    print_success "Redis: Running on port $REDIS_PORT"
else
    print_warning "Redis: Not running on port $REDIS_PORT (optional)"
fi

echo ""

# Check application services
print_header "Application Services"
echo ""

# Backend API
if check_port $BACKEND_PORT; then
    print_success "Backend API: Running on port $BACKEND_PORT"
    
    # Test health endpoint
    test_api_endpoint "http://localhost:$BACKEND_PORT/health" "Backend Health Check"
    
    # Test API endpoints
    test_api_endpoint "http://localhost:$BACKEND_PORT/api/discovery/popular?limit=1" "Discovery API"
    test_api_endpoint "http://localhost:$BACKEND_PORT/api/shared-lists" "Shared Lists API"
    
else
    print_error "Backend API: Not running on port $BACKEND_PORT"
fi

echo ""

# Web Application
if check_port $WEB_PORT; then
    print_success "Web Application: Running on port $WEB_PORT"
    
    # Test web app endpoint
    test_endpoint "http://localhost:$WEB_PORT" "Web App Home Page"
    
else
    print_error "Web Application: Not running on port $WEB_PORT"
fi

echo ""

# Mobile Application
if check_port $MOBILE_PORT; then
    print_success "Mobile Application: Running on port $MOBILE_PORT"
    
    # Test mobile app endpoint
    test_endpoint "http://localhost:$MOBILE_PORT" "Mobile App Expo Server"
    
else
    print_info "Mobile Application: Not running on port $MOBILE_PORT"
fi

echo ""

# Check environment files
print_header "Configuration Files"
echo ""

# Backend .env
if [ -f "backend/.env" ]; then
    print_success "Backend .env: Found"
    
    # Check critical environment variables
    if grep -q "DATABASE_URL=" backend/.env; then
        print_success "Backend .env: DATABASE_URL configured"
    else
        print_error "Backend .env: DATABASE_URL missing"
    fi
    
    if grep -q "PORT=3001" backend/.env; then
        print_success "Backend .env: PORT correctly set to 3001"
    else
        print_warning "Backend .env: PORT not set to 3001"
    fi
    
else
    print_error "Backend .env: Not found"
fi

# Web .env.local
if [ -f "web/.env.local" ]; then
    print_success "Web .env.local: Found"
    
    # Check critical environment variables
    if grep -q "NEXT_PUBLIC_API_URL.*3001" web/.env.local; then
        print_success "Web .env.local: API_URL correctly points to port 3001"
    else
        print_warning "Web .env.local: API_URL not pointing to port 3001"
    fi
    
    if grep -q "AUTH0_BASE_URL.*3000" web/.env.local; then
        print_success "Web .env.local: AUTH0_BASE_URL correctly set to port 3000"
    else
        print_warning "Web .env.local: AUTH0_BASE_URL not set to port 3000"
    fi
    
else
    print_error "Web .env.local: Not found"
fi

echo ""

# Summary
print_header "Summary"
echo ""

# Count services
backend_ok=0
web_ok=0
mobile_ok=0
db_ok=0

if check_port $BACKEND_PORT; then backend_ok=1; fi
if check_port $WEB_PORT; then web_ok=1; fi
if check_port $MOBILE_PORT; then mobile_ok=1; fi
if check_port $POSTGRES_PORT; then db_ok=1; fi

total_critical=$((backend_ok + web_ok + db_ok))
total_services=3

if [ $total_critical -eq 3 ]; then
    print_success "All critical services are running! 🎉"
    echo ""
    echo "🌐 Access your applications:"
    echo "   • Backend API: http://localhost:$BACKEND_PORT"
    echo "   • Web App: http://localhost:$WEB_PORT"
    if [ $mobile_ok -eq 1 ]; then
        echo "   • Mobile App: http://localhost:$MOBILE_PORT"
    fi
    echo ""
    echo "📋 Quick commands:"
    echo "   • Check status: ./dev-servers.sh status"
    echo "   • View logs: ./dev-servers.sh logs [backend|web|mobile]"
    echo "   • Restart all: ./dev-servers.sh restart"
    
elif [ $total_critical -gt 0 ]; then
    print_warning "Some services are running, but not all critical services are available"
    echo ""
    echo "🔧 To start missing services:"
    echo "   ./dev-servers.sh start"
    
else
    print_error "No critical services are running"
    echo ""
    echo "🚀 To start all services:"
    echo "   ./dev-servers.sh start"
fi

echo ""

# Exit with appropriate code
if [ $total_critical -eq 3 ]; then
    exit 0
else
    exit 1
fi