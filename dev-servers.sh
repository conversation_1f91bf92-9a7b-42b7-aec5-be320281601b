#!/bin/bash

# DreamVault Dev Server Launcher (tmux-enabled)
set -e

trap 'print_warning "Interrupted. Cleaning up..."; kill_all_servers; exit 0' SIGINT SIGTERM

# Colors
RED='\033[0;31m'; GREEN='\033[0;32m'; YELLOW='\033[1;33m'
BLUE='\033[0;34m'; CYAN='\033[0;36m'; NC='\033[0m'

# Ports
BACKEND_PORT=3001; WEB_PORT=3000; MOBILE_PORT=8081
POSTGRES_PORT=5432; REDIS_PORT=6379

# Dirs
BACKEND_DIR="./backend"; WEB_DIR="./web"; MOBILE_DIR="./mobile"

print_status()   { echo -e "${BLUE}[$(date '+%F %T')]${NC} $1"; }
print_success()  { echo -e "${GREEN}✅ $1${NC}"; }
print_error()    { echo -e "${RED}❌ $1${NC}"; }
print_warning()  { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_info()     { echo -e "${CYAN}ℹ️  $1${NC}"; }

detect_pm() {
    command -v bun &>/dev/null && echo "bun" && return
    command -v pnpm &>/dev/null && echo "pnpm" && return
    echo "npm"
}

PM=$(detect_pm)

check_port() { lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1; }

kill_port() {
    local port=$1 name=$2
    if check_port $port; then
        print_warning "Killing $name on port $port"
        lsof -ti :$port | xargs kill -9 2>/dev/null || true
        sleep 1 && check_port $port && print_error "Failed to kill port $port" || print_success "Killed $name"
    else
        print_info "$name (port $port) is already free"
    fi
}

check_dependencies() {
    print_status "Checking dependencies..."
    command -v node &>/dev/null || { print_error "Node.js is not installed"; exit 1; }
    command -v $PM &>/dev/null || { print_error "$PM is not installed"; exit 1; }
    command -v tmux &>/dev/null || { print_error "tmux is not installed"; exit 1; }

    [ -d "$BACKEND_DIR" ] || { print_error "Missing $BACKEND_DIR"; exit 1; }
    [ -d "$WEB_DIR" ] || { print_error "Missing $WEB_DIR"; exit 1; }
    [ -d "$MOBILE_DIR" ] || print_warning "Missing $MOBILE_DIR (mobile skipped)"

    print_success "Dependency check passed"
}

check_database() {
    print_status "Checking database..."
    check_port $POSTGRES_PORT || {
        print_error "PostgreSQL not running on port $POSTGRES_PORT"
        print_info "Start it via: brew services start postgresql (macOS) / sudo systemctl start postgresql (Linux)"
        exit 1
    }
    print_success "PostgreSQL is running"
}

check_redis() {
    print_status "Checking Redis..."
    check_port $REDIS_PORT && print_success "Redis is running" || {
        print_warning "Redis not running on port $REDIS_PORT"
        print_info "Optional. Start with: brew services start redis"
    }
}

install_dependencies() {
    print_status "Installing dependencies with $PM..."
    for dir in "$BACKEND_DIR" "$WEB_DIR" "$MOBILE_DIR"; do
        [ -f "$dir/package.json" ] || continue
        print_info "Installing in $dir"
        cd "$dir" && $PM install && cd - >/dev/null
        print_success "Installed $dir dependencies"
    done
}

setup_database() {
    print_status "Setting up Supabase connection..."
    print_info "Supabase setup will be handled via environment variables"
    print_info "Make sure to configure SUPABASE_URL and SUPABASE_ANON_KEY"
    print_success "Database setup complete"
}

kill_all_servers() {
    print_status "Killing dev servers..."
    kill_port $BACKEND_PORT "Backend"
    kill_port $WEB_PORT "Web"
    kill_port $MOBILE_PORT "Mobile"

    pkill -f "npm run dev|$PM run dev|next dev|expo start|vite|webpack-dev-server|nodemon|ts-node-dev" 2>/dev/null || true

    for port in 3000 3001 3002 4000 5173 8000 8080 8081 9000; do
        [ $port -ne $POSTGRES_PORT ] && [ $port -ne $REDIS_PORT ] && kill_port $port "Process"
    done

    tmux has-session -t dreamvault 2>/dev/null && tmux kill-session -t dreamvault
    print_success "All servers killed"
}

start_all_tmux() {
    print_status "Starting servers in tmux windows..."

    check_dependencies; check_database; check_redis
    kill_all_servers
    sleep 2

    tmux new-session -d -s dreamvault -n backend "cd $BACKEND_DIR && $PM run dev"
    tmux new-window -t dreamvault -n web "cd $WEB_DIR && $PM run dev"

    if [ -d "$MOBILE_DIR" ]; then
        tmux new-window -t dreamvault -n mobile "cd $MOBILE_DIR && $PM run start"
    fi

    print_success "🚀 Servers running in tmux session: dreamvault"
    echo ""
    echo "📺 Use: tmux attach -t dreamvault"
    echo "  → Ctrl+b → [n/p] to switch windows"
    echo ""
}

check_status() {
    print_status "Server Status"
    for svc in "Backend:$BACKEND_PORT" "Web:$WEB_PORT" "Mobile:$MOBILE_PORT"; do
        IFS=":" read -r name port <<< "$svc"
        check_port $port && print_success "$name: http://localhost:$port" || print_error "$name not running"
    done
    check_port $POSTGRES_PORT && print_success "PostgreSQL running" || print_error "PostgreSQL not running"
    check_port $REDIS_PORT && print_success "Redis running" || print_info "Redis optional"
}

show_logs() {
    print_warning "Logging is now done inside tmux."
    echo "ℹ️ Use: tmux attach -t dreamvault"
}

# Legacy start (still useful if no tmux preferred)
start_all_basic() {
    print_status "Starting all servers without tmux"
    install_dependencies
    setup_database
    echo "Use 'start-multi' for tmux-based launch."
}

# CLI
case "${1:-start-multi}" in
    start)       start_all_basic ;;
    start-multi) start_all_tmux ;;
    stop)        kill_all_servers ;;
    restart)     kill_all_servers; sleep 2; start_all_tmux ;;
    install)     install_dependencies ;;
    setup)       check_dependencies; install_dependencies; setup_database ;;
    status)      check_status ;;
    logs)        show_logs ;;
    help|-h|--help)
        echo "DreamVault Dev Server CLI"
        echo ""
        echo "Usage: ./dev-servers.sh [command]"
        echo ""
        echo "Commands:"
        echo "  start         Start all servers (legacy method)"
        echo "  start-multi   Start all servers in tmux (recommended)"
        echo "  stop          Kill all servers"
        echo "  restart       Restart everything"
        echo "  status        Show current status"
        echo "  setup         Setup DB + install deps"
        echo "  install       Install dependencies only"
        echo "  logs          Show log info (use tmux instead)"
        echo ""
        echo "Pro Tip: tmux attach -t dreamvault"
        ;;
    *) print_error "Unknown command: $1"; exit 1 ;;
esac