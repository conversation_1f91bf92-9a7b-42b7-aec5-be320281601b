#!/bin/bash

# DreamVault Dev Server Killer
# Stops all known dev server processes & frees up ports

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print()      { echo -e "${YELLOW}➡ $1${NC}"; }
success()    { echo -e "${GREEN}✅ $1${NC}"; }
error()      { echo -e "${RED}❌ $1${NC}"; }

check_port() {
    lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1
}

kill_port() {
    local port=$1
    if check_port $port; then
        local pids
        pids=$(lsof -ti :$port)
        echo "$pids" | xargs kill -9 2>/dev/null || true
        success "Killed processes on port $port"
    fi
}

print "Stopping dev server processes..."

# Kill known dev-related process patterns
pkill -f "vite"              2>/dev/null || true
pkill -f "next dev"          2>/dev/null || true
pkill -f "webpack-dev-server" 2>/dev/null || true
pkill -f "expo start"        2>/dev/null || true
pkill -f "nodemon"           2>/dev/null || true
pkill -f "ts-node-dev"       2>/dev/null || true
pkill -f "npm run dev"       2>/dev/null || true
pkill -f "pnpm run dev"      2>/dev/null || true
pkill -f "bun run dev"       2>/dev/null || true
pkill -f "npm start"         2>/dev/null || true
pkill -f "pnpm start"        2>/dev/null || true
pkill -f "bun start"         2>/dev/null || true

success "Killed dev-related processes"

# Kill common dev ports
DEV_PORTS=(3000 3001 3002 3003 4000 4001 5000 5001 5173 8000 8001 8080 8081 9000)
for port in "${DEV_PORTS[@]}"; do
    kill_port $port
done

# Kill DreamVault tmux session if it exists
if command -v tmux &>/dev/null && tmux has-session -t dreamvault 2>/dev/null; then
    tmux kill-session -t dreamvault
    success "Killed tmux session: dreamvault"
fi

success "All development servers stopped."