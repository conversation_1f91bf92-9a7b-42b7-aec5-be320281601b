# 🌟 DreamVault

A comprehensive full-stack bucket list application that helps users track, manage, and achieve their dreams and goals. Built with modern technologies and designed for scalability, security, and user experience.

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5+-blue.svg)](https://www.typescriptlang.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-14+-blue.svg)](https://www.postgresql.org/)

## 🚀 Features

### 🎯 Core Functionality
- **Bucket List Management** - Create, organize, and track life goals and dreams
- **Progress Tracking** - Document your journey with photos, notes, and milestones
- **Smart Categorization** - Organize items by travel, adventure, career, personal, creative, fitness, education, and social
- **Priority System** - Set priorities from low to urgent for better goal management
- **Status Tracking** - Monitor progress from not started to completed

### 🔐 Authentication & Security
- **Auth0 Integration** - Secure authentication across all platforms
- **JWT Token Management** - Stateless authentication with refresh tokens
- **Role-based Access** - User profile management and permissions
- **Input Sanitization** - XSS protection and data validation
- **Rate Limiting** - API protection against abuse

### 📱 Multi-Platform Support
- **Web Application** - Modern React-based web interface with dark/light theme support
- **Mobile Apps** - Native iOS and Android apps with React Native + Expo
- **Responsive Design** - Optimized for desktop, tablet, and mobile devices
- **Theme System** - Complete dark/light/system theme support with persistence
- **Offline Support** - Work on your goals even without internet connection

### 🤖 AI-Powered Features
- **Smart Suggestions** - AI-powered goal recommendations
- **Progress Insights** - Intelligent analysis of your achievements
- **Goal Planning** - AI assistance for breaking down complex goals

### 🌐 Social & Sharing
- **List Sharing** - Share bucket lists with friends and family
- **Achievement Notifications** - Celebrate milestones together
- **Social Feed** - See friends' achievements and progress

### 📊 Analytics & Insights
- **Progress Analytics** - Visual insights into your goal completion
- **Achievement System** - Gamified experience with badges and rewards
- **Timeline View** - Visual progress tracking over time
- **Export Options** - Export your data in various formats
- **Settings Management** - Comprehensive user preferences and privacy controls

### 🎨 User Experience
- **Modern UI Design** - Beautiful, intuitive interface with glassmorphism effects
- **Custom Animations** - Smooth transitions and micro-interactions
- **Accessibility** - WCAG compliant components with keyboard navigation
- **Performance Optimized** - Fast loading times and smooth interactions

## 🏗️ Architecture

### Technology Stack

**Backend API**
- **Express.js** - Fast, unopinionated web framework
- **TypeScript** - Type-safe JavaScript development
- **PostgreSQL** - Robust relational database
- **Prisma ORM** - Modern database toolkit and ORM
- **Auth0** - Authentication and authorization platform
- **Socket.io** - Real-time bidirectional communication
- **AWS S3** - Cloud storage for media files
- **Redis** - In-memory caching and session storage

**Web Application**
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework with dark mode support
- **Radix UI** - Accessible UI components
- **Zustand** - Lightweight state management
- **React Hook Form** - Performant forms with validation
- **Theme Provider** - Custom theme switching with localStorage persistence

**Mobile Applications**
- **React Native** - Cross-platform mobile development
- **Expo** - Development platform and tools
- **React Navigation** - Navigation library
- **Auth0 React Native** - Mobile authentication
- **Zustand** - State management
- **Expo Camera** - Camera and media capture

**Shared Infrastructure**
- **TypeScript Types** - Shared type definitions across platforms
- **ESLint & Prettier** - Code linting and formatting
- **Jest** - Testing framework
- **GitHub Actions** - CI/CD pipeline

### Project Structure

```
DreamVault/
├── backend/                 # Express.js API server
│   ├── src/
│   │   ├── routes/         # API route handlers
│   │   ├── lib/            # Utilities and services
│   │   ├── middleware/     # Express middleware
│   │   ├── tests/          # Test files
│   │   └── prisma/         # Database schema and migrations
│   └── package.json
├── web/                    # Next.js web application
│   ├── src/
│   │   ├── app/            # App Router pages
│   │   ├── components/     # React components (including theme provider)
│   │   ├── lib/            # Utilities and hooks
│   │   └── types/          # Web-specific type definitions
│   └── package.json
├── mobile/                 # React Native + Expo app
│   ├── src/
│   │   ├── screens/        # Screen components
│   │   ├── navigation/     # Navigation setup
│   │   ├── components/     # Reusable components
│   │   └── utils/          # Utilities and services
│   └── package.json
├── packages/
│   └── types/              # Shared TypeScript types
└── CLAUDE.md              # Development guidance for AI assistants
```

## 🛠️ Installation & Setup

### Prerequisites

- **Node.js** 18+ and npm
- **PostgreSQL** 14+
- **Redis** (optional, for caching)
- **Auth0 Account** for authentication
- **AWS Account** (for S3 storage)
- **Expo CLI** (for mobile development)

### Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/Stijnus/DreamVault.git
   cd DreamVault
   ```

2. **Install dependencies**
   ```bash
   # Backend
   cd backend && npm install
   
   # Web
   cd ../web && npm install
   
   # Mobile
   cd ../mobile && npm install
   
   # Shared types
   cd ../packages/types && npm install
   ```

3. **Set up environment variables**
   ```bash
   # Backend
   cd backend && cp .env.example .env
   # Edit .env with your actual values
   
   # Mobile
   cd ../mobile && cp .env.example .env
   # Edit .env with your actual values
   ```

4. **Set up the database**
   ```bash
   cd backend
   npm run db:push
   npm run db:seed
   ```

5. **Start development servers**
   ```bash
   # Terminal 1 - Backend API (port 3001)
   cd backend && npm run dev
   
   # Terminal 2 - Web App (port 3000)
   cd web && npm run dev
   
   # Terminal 3 - Mobile App
   cd mobile && npm run start
   ```

### Environment Configuration

#### Backend (.env)
```bash
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/dreamvault_dev"

# Server
PORT=3001
NODE_ENV=development

# Auth0
AUTH0_DOMAIN=your-auth0-domain.auth0.com
AUTH0_AUDIENCE=your-api-identifier
AUTH0_CLIENT_ID=your-client-id
AUTH0_CLIENT_SECRET=your-client-secret

# JWT
JWT_SECRET=your-jwt-secret-key

# AWS S3
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=dreamvault-media

# External APIs
OPENAI_API_KEY=your-openai-api-key
WEATHER_API_KEY=your-weather-api-key
```

#### Mobile (.env)
```bash
# API Configuration
API_BASE_URL=http://localhost:3001/api

# Auth0 Configuration
AUTH0_DOMAIN=your-auth0-domain.auth0.com
AUTH0_CLIENT_ID=your-client-id
AUTH0_AUDIENCE=your-api-identifier

# Feature Flags
ENABLE_BIOMETRIC_AUTH=true
ENABLE_OFFLINE_SYNC=true
ENABLE_PUSH_NOTIFICATIONS=true
```

## 📱 Development

### Backend Development

```bash
cd backend

# Development server with hot reload
npm run dev

# Build for production
npm run build

# Run tests
npm test
npm run test:watch
npm run test:coverage

# Database operations
npm run db:generate    # Generate Prisma client
npm run db:push        # Push schema changes
npm run db:migrate     # Run migrations
npm run db:seed        # Seed with sample data
npm run db:studio      # Open Prisma Studio

# Code quality
npm run lint           # ESLint
npm run type-check     # TypeScript checking
```

### Web Development

```bash
cd web

# Development server (port 3000)
npm run dev

# Build for production
npm run build

# Start production server
npm run start

# Code quality
npm run lint           # Next.js ESLint
npm run type-check     # TypeScript checking
```

### Mobile Development

```bash
cd mobile

# Start Expo development server
npm run start

# Run on specific platforms
npm run android        # React Native Android
npm run ios            # React Native iOS
npm run test           # Jest tests
npm run lint           # ESLint
npm run type-check     # TypeScript checking
```

## 🧪 Testing

### Backend Testing
```bash
cd backend

# Run all tests
npm test

# Run specific test file
npm test -- auth.test.ts

# Watch mode
npm run test:watch

# Coverage report
npm run test:coverage

# Test specific services
npm run test:auth      # Test Auth0 connection
npm run test:db        # Test database connection
```

### Integration Testing
- API endpoint testing with Supertest
- Database integration tests
- Authentication flow testing
- File upload and media processing tests

## 🚀 Deployment

### Backend Deployment

**Prerequisites for Production:**
- PostgreSQL database (AWS RDS, Google Cloud SQL, etc.)
- Redis instance (AWS ElastiCache, Redis Cloud, etc.)
- AWS S3 bucket for media storage
- Auth0 production tenant
- Environment variables configured

**Docker Support:**
```bash
cd backend
docker build -t dreamvault-api .
docker run -p 3001:3001 dreamvault-api
```

### Web Application Deployment

**Vercel (Recommended):**
```bash
cd web
npm run build
# Deploy to Vercel
```

**Docker:**
```bash
cd web
docker build -t dreamvault-web .
docker run -p 3000:3000 dreamvault-web
```

### Mobile App Deployment

**iOS App Store:**
```bash
cd mobile
expo build:ios
# Follow Expo's guide for App Store submission
```

**Google Play Store:**
```bash
cd mobile
expo build:android
# Follow Expo's guide for Play Store submission
```

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user
- `PUT /api/auth/profile` - Update user profile

### Bucket List Endpoints
- `GET /api/items` - Get user's bucket list items
- `POST /api/items` - Create new bucket list item
- `PUT /api/items/:id` - Update bucket list item
- `DELETE /api/items/:id` - Delete bucket list item
- `POST /api/items/:id/progress` - Add progress entry

### Media Endpoints
- `POST /api/media/upload` - Upload media files
- `GET /api/media/:id` - Get media file
- `DELETE /api/media/:id` - Delete media file

### Health & Monitoring
- `GET /health` - Comprehensive health check
- `GET /ready` - Readiness probe
- `GET /live` - Liveness probe
- `GET /metrics` - Application metrics

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines:

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** and add tests if applicable
4. **Run tests**: `npm test` in relevant directories
5. **Commit your changes**: `git commit -m 'Add amazing feature'`
6. **Push to the branch**: `git push origin feature/amazing-feature`
7. **Open a Pull Request**

### Development Guidelines

- Follow TypeScript best practices
- Write tests for new features
- Use conventional commit messages
- Ensure all linters pass
- Update documentation as needed

### Code Style

- **ESLint** and **Prettier** are configured for consistent code style
- **TypeScript** strict mode is enabled
- Follow component naming conventions
- Use meaningful variable and function names

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the individual README files in each directory
- **Issues**: [GitHub Issues](https://github.com/Stijnus/DreamVault/issues)
- **Discussions**: [GitHub Discussions](https://github.com/Stijnus/DreamVault/discussions)

## 🎯 Roadmap

### Phase 1 (Completed)
- ✅ Core bucket list functionality
- ✅ User authentication and profiles
- ✅ Mobile app support with React Native + Expo
- ✅ File upload and media management
- ✅ Complete dark/light theme system
- ✅ Settings and user preferences
- ✅ Responsive web interface with modern UI

### Phase 2 (In Progress)
- ✅ AI-powered goal suggestions (implemented)
- 🔄 Advanced analytics and insights (partial)
- 🔄 Social features and list sharing
- 🔄 Push notifications

### Phase 3
- 📋 Collaborative bucket lists
- 📋 Integration with calendar apps
- 📋 Location-based reminders
- 📋 Goal achievement streaks

### Phase 4
- 📋 Community features
- 📋 Goal marketplace
- 📋 Advanced AI insights
- 📋 Wearable device integration

## 🙏 Acknowledgments

- **Auth0** for authentication services
- **Prisma** for the excellent ORM
- **Next.js** team for the amazing framework
- **Expo** for making mobile development accessible
- **Open source community** for the amazing tools and libraries

---

**Made with ❤️ by the DreamVault team**

*Turn your dreams into achievable goals, one step at a time.*