{"name": "@dreamvault/backend", "version": "1.0.0", "description": "DreamVault API Server", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit"}, "dependencies": {"@aws-sdk/client-s3": "^3.857.0", "@aws-sdk/s3-request-presigner": "^3.857.0", "@google/generative-ai": "^0.24.1", "@sentry/node": "^9.43.0", "@sentry/profiling-node": "^9.43.0", "@supabase/supabase-js": "^2.39.0", "@types/socket.io": "^3.0.1", "aws-sdk": "^2.1519.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "multer": "^1.4.5-lts.1", "openai": "^5.11.0", "redis": "^4.6.12", "sharp": "^0.34.3", "socket.io": "^4.8.1", "tslib": "^2.8.1", "uuid": "^11.1.0", "zod": "^3.23.8"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "axios": "^1.11.0", "eslint": "^8.56.0", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "keywords": ["bucket-list", "api", "express", "supabase"], "author": "DreamVault Team", "license": "MIT"}