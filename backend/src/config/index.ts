import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

// Environment validation
const requiredEnvVars = [
  'SUPABASE_URL',
  'SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY'
] as const

const optionalEnvVars = [
  'PORT',
  'NODE_ENV',
  'REDIS_URL',
  'AWS_ACCESS_KEY_ID',
  'AWS_SECRET_ACCESS_KEY',
  'AWS_REGION',
  'AWS_S3_BUCKET',
  'OPENAI_API_KEY',
  'GOOGLE_API_KEY',
  'WEATHER_API_KEY',
  'ALLOWED_ORIGINS',
  'SENTRY_DSN'
] as const

// Validate required environment variables
export const validateEnvironment = (): void => {
  const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar])
  
  if (missingEnvVars.length > 0) {
    console.error('❌ Missing required environment variables:', missingEnvVars.join(', '))
    console.error('Please check your .env file and ensure all required variables are set.')
    throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`)
  }
}

// Configuration object with type safety
export const config = {
  // Server configuration
  server: {
    port: parseInt(process.env.PORT || '3001', 10),
    nodeEnv: process.env.NODE_ENV || 'development',
    isDevelopment: process.env.NODE_ENV !== 'production',
    isProduction: process.env.NODE_ENV === 'production',
    allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || [
      'http://localhost:3000',
      'http://localhost:19006',
      'http://localhost:8081'
    ]
  },

  // Database configuration
  database: {
    url: process.env.DATABASE_URL!,
    maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '10', 10),
    connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000', 10)
  },

  // Supabase configuration
  supabase: {
    url: process.env.SUPABASE_URL!,
    anonKey: process.env.SUPABASE_ANON_KEY!,
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY!
  },

  // Redis configuration
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    maxRetries: parseInt(process.env.REDIS_MAX_RETRIES || '3', 10),
    retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY || '100', 10)
  },

  // AWS configuration
  aws: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION || 'us-east-1',
    s3Bucket: process.env.AWS_S3_BUCKET || 'dreamvault-media'
  },

  // External APIs
  externalApis: {
    openaiApiKey: "********************************************************************************************************************************************************************",
    googleApiKey: process.env.GOOGLE_API_KEY,
    weatherApiKey: process.env.WEATHER_API_KEY
  },

  // Security configuration
  security: {
    rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || '1000', 10),
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760', 10), // 10MB
    allowedFileTypes: (process.env.ALLOWED_FILE_TYPES || 'jpg,jpeg,png,gif,mp4,mov,mp3,wav').split(',')
  },

  // Monitoring configuration
  monitoring: {
    enableMetrics: process.env.ENABLE_METRICS !== 'false',
    enableHealthChecks: process.env.ENABLE_HEALTH_CHECKS !== 'false',
    logLevel: process.env.LOG_LEVEL || 'info',
    sentryDsn: process.env.SENTRY_DSN
  }
} as const

// Export individual config sections for convenience
export const {
  server: serverConfig,
  database: databaseConfig,
  auth: authConfig,
  redis: redisConfig,
  aws: awsConfig,
  externalApis: externalApisConfig,
  security: securityConfig,
  monitoring: monitoringConfig
} = config

// Configuration validation
export const validateConfig = (): void => {
  validateEnvironment()

  // Validate port range
  if (config.server.port < 1 || config.server.port > 65535) {
    throw new Error(`Invalid port number: ${config.server.port}. Must be between 1 and 65535.`)
  }

  // Validate JWT secret strength in production
  if (config.server.isProduction && config.auth.jwtSecret.length < 32) {
    throw new Error('JWT_SECRET must be at least 32 characters long in production')
  }

  // Validate database URL format
  if (!config.database.url.startsWith('postgresql://')) {
    throw new Error('DATABASE_URL must be a valid PostgreSQL connection string')
  }

  // Validate Auth0 domain format
  if (!config.auth.auth0Domain.includes('.auth0.com') && config.server.isProduction) {
    console.warn('⚠️  AUTH0_DOMAIN does not appear to be a valid Auth0 domain')
  }

  console.log('✅ Configuration validation passed')
}

// Helper function to get configuration summary (without secrets)
export const getConfigSummary = () => ({
  server: {
    port: config.server.port,
    nodeEnv: config.server.nodeEnv,
    allowedOrigins: config.server.allowedOrigins
  },
  database: {
    hasUrl: !!config.database.url,
    maxConnections: config.database.maxConnections
  },
  auth: {
    auth0Domain: config.auth.auth0Domain,
    hasJwtSecret: !!config.auth.jwtSecret
  },
  redis: {
    hasUrl: !!config.redis.url
  },
  aws: {
    hasCredentials: !!(config.aws.accessKeyId && config.aws.secretAccessKey),
    region: config.aws.region,
    s3Bucket: config.aws.s3Bucket
  },
  externalApis: {
    hasOpenaiKey: !!config.externalApis.openaiApiKey,
    hasGoogleKey: !!config.externalApis.googleApiKey,
    hasWeatherKey: !!config.externalApis.weatherApiKey
  },
  monitoring: config.monitoring
})