import { prisma } from '../lib/prisma'

const defaultAchievements = [
  // Completion-based achievements
  {
    name: 'First Steps',
    description: 'Complete your first bucket list item',
    icon: '🎯',
    category: 'completion',
    criteria: {
      type: 'completion_count',
      value: 1
    },
    points: 10
  },
  {
    name: 'Getting Started',
    description: 'Complete 5 bucket list items',
    icon: '🚀',
    category: 'completion',
    criteria: {
      type: 'completion_count',
      value: 5
    },
    points: 25
  },
  {
    name: 'Dream Chaser',
    description: 'Complete 10 bucket list items',
    icon: '⭐',
    category: 'completion',
    criteria: {
      type: 'completion_count',
      value: 10
    },
    points: 50
  },
  {
    name: 'Goal Crusher',
    description: 'Complete 25 bucket list items',
    icon: '💪',
    category: 'completion',
    criteria: {
      type: 'completion_count',
      value: 25
    },
    points: 100
  },
  {
    name: 'Legend',
    description: 'Complete 50 bucket list items',
    icon: '👑',
    category: 'completion',
    criteria: {
      type: 'completion_count',
      value: 50
    },
    points: 250
  },

  // Category-specific achievements
  {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Complete 5 travel-related bucket list items',
    icon: '✈️',
    category: 'travel',
    criteria: {
      type: 'category_completion',
      value: 5,
      category: 'TRAVEL'
    },
    points: 30
  },
  {
    name: 'Career Climber',
    description: 'Complete 3 career-related bucket list items',
    icon: '📈',
    category: 'career',
    criteria: {
      type: 'category_completion',
      value: 3,
      category: 'CAREER'
    },
    points: 40
  },
  {
    name: 'Personal Growth Guru',
    description: 'Complete 5 personal growth bucket list items',
    icon: '🌱',
    category: 'growth',
    criteria: {
      type: 'category_completion',
      value: 5,
      category: 'PERSONAL_GROWTH'
    },
    points: 35
  },
  {
    name: 'Adventure Seeker',
    description: 'Complete 5 adventure bucket list items',
    icon: '🏔️',
    category: 'adventure',
    criteria: {
      type: 'category_completion',
      value: 5,
      category: 'ADVENTURES'
    },
    points: 40
  },
  {
    name: 'Lifelong Learner',
    description: 'Complete 5 learning-related bucket list items',
    icon: '📚',
    category: 'learning',
    criteria: {
      type: 'category_completion',
      value: 5,
      category: 'LEARNING'
    },
    points: 30
  },

  // Milestone-based achievements
  {
    name: 'Milestone Master',
    description: 'Complete 10 milestones across all your bucket list items',
    icon: '🏆',
    category: 'milestone',
    criteria: {
      type: 'milestone_count',
      value: 10
    },
    points: 50
  },
  {
    name: 'Progress Tracker',
    description: 'Complete 25 milestones across all your bucket list items',
    icon: '📊',
    category: 'milestone',
    criteria: {
      type: 'milestone_count',
      value: 25
    },
    points: 100
  },

  // Streak-based achievements
  {
    name: 'Consistent Achiever',
    description: 'Complete bucket list items for 7 consecutive days',
    icon: '🔥',
    category: 'streak',
    criteria: {
      type: 'streak_days',
      value: 7
    },
    points: 75
  },
  {
    name: 'Unstoppable',
    description: 'Complete bucket list items for 30 consecutive days',
    icon: '⚡',
    category: 'streak',
    criteria: {
      type: 'streak_days',
      value: 30
    },
    points: 200
  },

  // Time-based achievements
  {
    name: 'Monthly Champion',
    description: 'Complete 5 bucket list items in a single month',
    icon: '📅',
    category: 'time',
    criteria: {
      type: 'time_based',
      value: 5,
      timeframe: 30
    },
    points: 60
  },
  {
    name: 'Speed Demon',
    description: 'Complete 10 bucket list items in a single month',
    icon: '💨',
    category: 'time',
    criteria: {
      type: 'time_based',
      value: 10,
      timeframe: 30
    },
    points: 120
  },

  // Cost-based achievements
  {
    name: 'Budget Conscious',
    description: 'Complete bucket list items worth $1,000 in total estimated cost',
    icon: '💰',
    category: 'financial',
    criteria: {
      type: 'cost_saved',
      value: 1000
    },
    points: 50
  },
  {
    name: 'Big Spender',
    description: 'Complete bucket list items worth $5,000 in total estimated cost',
    icon: '💎',
    category: 'financial',
    criteria: {
      type: 'cost_saved',
      value: 5000
    },
    points: 150
  }
]

export async function seedAchievements() {
  console.log('🌱 Seeding achievements...')

  try {
    // Clear existing achievements
    await prisma.userAchievement.deleteMany()
    await prisma.achievement.deleteMany()

    // Create achievements
    for (const achievementData of defaultAchievements) {
      await prisma.achievement.create({
        data: achievementData
      })
    }

    console.log(`✅ Created ${defaultAchievements.length} achievements`)
  } catch (error) {
    console.error('❌ Error seeding achievements:', error)
    throw error
  }
}

// Run if called directly
if (require.main === module) {
  seedAchievements()
    .then(() => {
      console.log('🎉 Achievement seeding completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Achievement seeding failed:', error)
      process.exit(1)
    })
}