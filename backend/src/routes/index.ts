import { Router } from 'express'
import aiRoutes from './ai'

const router = Router()

// Mount route modules - only keep AI functionality for now
router.use('/ai', aiRoutes)

// Health check for API routes
router.get('/', (_req, res) => {
  res.json({
    message: 'DreamVault API v1.0.0 - AI Services',
    timestamp: new Date().toISOString(),
    note: 'Basic CRUD operations handled directly by Supabase',
    endpoints: {
      ai: '/api/ai',
      health: '/health'
    }
  })
})

export default router