import { Router, Request, Response } from 'express'
import { z } from 'zod'
import { authenticate } from '../middleware/auth'
import { aiService } from '../lib/ai-service'
import { Logger } from '../lib/monitoring'

const router = Router()

// Validation schemas
const categorySuggestionSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().max(1000).optional()
})

const locationExtractionSchema = z.object({
  text: z.string().min(1).max(2000)
})

const tagSuggestionSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().max(1000).optional(),
  category: z.string().max(50).optional()
})

const contentModerationSchema = z.object({
  text: z.string().min(1).max(2000)
})

const smartSuggestionsSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().max(1000).optional()
})

// POST /api/ai/suggest-categories - Get category suggestions for bucket list item
router.post('/suggest-categories', authenticate, async (req: Request, res: Response) => {
  try {
    if (!aiService.isAvailable()) {
      return res.status(503).json({
        error: {
          code: 'AI_SERVICE_UNAVAILABLE',
          message: 'AI service is not available. Please check OpenAI API configuration.',
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      })
    }

    const validation = categorySuggestionSchema.safeParse(req.body)
    if (!validation.success) {
      return res.status(400).json({
        error: {
          code: 'VALIDATION_FAILED',
          message: 'Invalid request data',
          details: validation.error.issues,
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      })
    }

    const { title, description } = validation.data

    Logger.info('Generating category suggestions', {
      userId: req.dbUser?.id,
      title: title.substring(0, 50)
    })

    const suggestions = await aiService.suggestCategories(title, description)

    return res.json({
      message: 'Category suggestions generated successfully',
      suggestions,
      count: suggestions.length
    })
  } catch (error: any) {
    Logger.error('Failed to generate category suggestions', error)
    return res.status(500).json({
      error: {
        code: 'CATEGORY_SUGGESTION_FAILED',
        message: error.message || 'Failed to generate category suggestions',
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || 'unknown'
      }
    })
  }
})

// POST /api/ai/extract-locations - Extract location information from text
router.post('/extract-locations', authenticate, async (req: Request, res: Response) => {
  try {
    if (!aiService.isAvailable()) {
      return res.status(503).json({
        error: {
          code: 'AI_SERVICE_UNAVAILABLE',
          message: 'AI service is not available. Please check OpenAI API configuration.',
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      })
    }

    const validation = locationExtractionSchema.safeParse(req.body)
    if (!validation.success) {
      return res.status(400).json({
        error: {
          code: 'VALIDATION_FAILED',
          message: 'Invalid request data',
          details: validation.error.issues,
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      })
    }

    const { text } = validation.data

    Logger.info('Extracting locations', {
      userId: req.dbUser?.id,
      textLength: text.length
    })

    const extraction = await aiService.extractLocations(text)

    return res.json({
      message: 'Location extraction completed successfully',
      extraction,
      locationsFound: extraction.locations.length
    })
  } catch (error: any) {
    Logger.error('Failed to extract locations', error)
    return res.status(500).json({
      error: {
        code: 'LOCATION_EXTRACTION_FAILED',
        message: error.message || 'Failed to extract locations',
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || 'unknown'
      }
    })
  }
})

// POST /api/ai/suggest-tags - Generate tag suggestions
router.post('/suggest-tags', authenticate, async (req: Request, res: Response) => {
  try {
    if (!aiService.isAvailable()) {
      return res.status(503).json({
        error: {
          code: 'AI_SERVICE_UNAVAILABLE',
          message: 'AI service is not available. Please check OpenAI API configuration.',
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      })
    }

    const validation = tagSuggestionSchema.safeParse(req.body)
    if (!validation.success) {
      return res.status(400).json({
        error: {
          code: 'VALIDATION_FAILED',
          message: 'Invalid request data',
          details: validation.error.issues,
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      })
    }

    const { title, description, category } = validation.data

    Logger.info('Generating tag suggestions', {
      userId: req.dbUser?.id,
      title: title.substring(0, 50),
      category
    })

    const suggestions = await aiService.suggestTags(title, description, category)

    return res.json({
      message: 'Tag suggestions generated successfully',
      suggestions,
      count: suggestions.length
    })
  } catch (error: any) {
    Logger.error('Failed to generate tag suggestions', error)
    return res.status(500).json({
      error: {
        code: 'TAG_SUGGESTION_FAILED',
        message: error.message || 'Failed to generate tag suggestions',
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || 'unknown'
      }
    })
  }
})

// POST /api/ai/moderate-content - Moderate content for appropriateness
router.post('/moderate-content', authenticate, async (req: Request, res: Response) => {
  try {
    if (!aiService.isAvailable()) {
      return res.status(503).json({
        error: {
          code: 'AI_SERVICE_UNAVAILABLE',
          message: 'AI service is not available. Please check OpenAI API configuration.',
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      })
    }

    const validation = contentModerationSchema.safeParse(req.body)
    if (!validation.success) {
      return res.status(400).json({
        error: {
          code: 'VALIDATION_FAILED',
          message: 'Invalid request data',
          details: validation.error.issues,
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      })
    }

    const { text } = validation.data

    Logger.info('Moderating content', {
      userId: req.dbUser?.id,
      textLength: text.length
    })

    const moderation = await aiService.moderateContent(text)

    return res.json({
      message: 'Content moderation completed successfully',
      moderation,
      isAppropriate: moderation.isAppropriate
    })
  } catch (error: any) {
    Logger.error('Failed to moderate content', error)
    return res.status(500).json({
      error: {
        code: 'CONTENT_MODERATION_FAILED',
        message: error.message || 'Failed to moderate content',
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || 'unknown'
      }
    })
  }
})

// POST /api/ai/smart-suggestions - Generate comprehensive suggestions
router.post('/smart-suggestions', authenticate, async (req: Request, res: Response) => {
  try {
    if (!aiService.isAvailable()) {
      return res.status(503).json({
        error: {
          code: 'AI_SERVICE_UNAVAILABLE',
          message: 'AI service is not available. Please check OpenAI API configuration.',
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      })
    }

    const validation = smartSuggestionsSchema.safeParse(req.body)
    if (!validation.success) {
      return res.status(400).json({
        error: {
          code: 'VALIDATION_FAILED',
          message: 'Invalid request data',
          details: validation.error.issues,
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      })
    }

    const { title, description } = validation.data

    Logger.info('Generating smart suggestions', {
      userId: req.dbUser?.id,
      title: title.substring(0, 50)
    })

    const suggestions = await aiService.generateSmartSuggestions(title, description)

    return res.json({
      message: 'Smart suggestions generated successfully',
      suggestions,
      summary: {
        categoriesCount: suggestions.categories.length,
        tagsCount: suggestions.tags.length,
        locationsCount: suggestions.locations.locations.length,
        hasInsights: !!(suggestions.estimatedDuration || suggestions.estimatedCost || suggestions.bestTimeToVisit)
      }
    })
  } catch (error: any) {
    Logger.error('Failed to generate smart suggestions', error)
    return res.status(500).json({
      error: {
        code: 'SMART_SUGGESTIONS_FAILED',
        message: error.message || 'Failed to generate smart suggestions',
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || 'unknown'
      }
    })
  }
})

// GET /api/ai/status - Check AI service status
router.get('/status', authenticate, async (req: Request, res: Response) => {
  try {
    const isAvailable = aiService.isAvailable()
    
    return res.json({
      message: 'AI service status retrieved successfully',
      status: {
        isAvailable,
        service: 'OpenAI',
        features: [
          'Category Suggestions',
          'Location Extraction', 
          'Tag Suggestions',
          'Content Moderation',
          'Smart Suggestions'
        ]
      }
    })
  } catch (error: any) {
    Logger.error('Failed to get AI service status', error)
    return res.status(500).json({
      error: {
        code: 'STATUS_CHECK_FAILED',
        message: error.message || 'Failed to check AI service status',
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || 'unknown'
      }
    })
  }
})

export default router
