import { Request, Response } from 'express'
import { config } from '../config'
import { captureException } from './sentry'

// Performance metrics storage
interface PerformanceMetrics {
  requests: {
    total: number
    successful: number
    failed: number
    averageResponseTime: number
  }
  endpoints: Record<string, {
    count: number
    averageResponseTime: number
    errors: number
  }>
  errors: {
    total: number
    byType: Record<string, number>
  }
  uptime: number
  memory: NodeJS.MemoryUsage
  startTime: Date
}

class MonitoringService {
  private metrics: PerformanceMetrics
  private requestTimes: number[] = []
  private readonly maxRequestTimes = 1000 // Keep last 1000 request times for average calculation

  constructor() {
    this.metrics = {
      requests: {
        total: 0,
        successful: 0,
        failed: 0,
        averageResponseTime: 0
      },
      endpoints: {},
      errors: {
        total: 0,
        byType: {}
      },
      uptime: 0,
      memory: process.memoryUsage(),
      startTime: new Date()
    }

    // Update memory usage every 30 seconds
    setInterval(() => {
      this.metrics.memory = process.memoryUsage()
      this.metrics.uptime = process.uptime()
    }, 30000)
  }

  // Record a request
  recordRequest(req: Request, res: Response, responseTime: number): void {
    if (!config.monitoring.enableMetrics) return

    this.metrics.requests.total++
    
    const endpoint = `${req.method} ${req.route?.path || req.path}`
    
    // Initialize endpoint metrics if not exists
    if (!this.metrics.endpoints[endpoint]) {
      this.metrics.endpoints[endpoint] = {
        count: 0,
        averageResponseTime: 0,
        errors: 0
      }
    }

    const endpointMetrics = this.metrics.endpoints[endpoint]
    endpointMetrics.count++

    // Update response time tracking
    this.requestTimes.push(responseTime)
    if (this.requestTimes.length > this.maxRequestTimes) {
      this.requestTimes.shift()
    }

    // Calculate average response times
    this.metrics.requests.averageResponseTime = 
      this.requestTimes.reduce((sum, time) => sum + time, 0) / this.requestTimes.length

    endpointMetrics.averageResponseTime = 
      (endpointMetrics.averageResponseTime * (endpointMetrics.count - 1) + responseTime) / endpointMetrics.count

    // Track success/failure
    if (res.statusCode >= 400) {
      this.metrics.requests.failed++
      endpointMetrics.errors++
    } else {
      this.metrics.requests.successful++
    }
  }

  // Record an error
  recordError(error: Error, context?: string): void {
    if (!config.monitoring.enableMetrics) return

    this.metrics.errors.total++
    
    const errorType = error.name || 'UnknownError'
    this.metrics.errors.byType[errorType] = (this.metrics.errors.byType[errorType] || 0) + 1

    // Log error details
    console.error(`[ERROR] ${context || 'Unknown context'}:`, {
      name: error.name,
      message: error.message,
      stack: config.server.isDevelopment ? error.stack : undefined,
      timestamp: new Date().toISOString()
    })
  }

  // Get current metrics
  getMetrics(): PerformanceMetrics & { 
    healthScore: number
    status: 'healthy' | 'degraded' | 'unhealthy'
  } {
    const successRate = this.metrics.requests.total > 0 
      ? (this.metrics.requests.successful / this.metrics.requests.total) * 100 
      : 100

    const errorRate = this.metrics.requests.total > 0
      ? (this.metrics.requests.failed / this.metrics.requests.total) * 100
      : 0

    // Calculate health score (0-100)
    let healthScore = 100
    
    // Deduct points for high error rate
    if (errorRate > 10) healthScore -= 30
    else if (errorRate > 5) healthScore -= 15
    else if (errorRate > 1) healthScore -= 5

    // Deduct points for slow response times
    if (this.metrics.requests.averageResponseTime > 5000) healthScore -= 25
    else if (this.metrics.requests.averageResponseTime > 2000) healthScore -= 15
    else if (this.metrics.requests.averageResponseTime > 1000) healthScore -= 5

    // Deduct points for high memory usage (>500MB)
    const memoryUsageMB = this.metrics.memory.heapUsed / 1024 / 1024
    if (memoryUsageMB > 500) healthScore -= 20
    else if (memoryUsageMB > 300) healthScore -= 10

    let status: 'healthy' | 'degraded' | 'unhealthy'
    if (healthScore >= 80) status = 'healthy'
    else if (healthScore >= 60) status = 'degraded'
    else status = 'unhealthy'

    return {
      ...this.metrics,
      healthScore,
      status
    }
  }

  // Reset metrics (useful for testing)
  reset(): void {
    this.metrics = {
      requests: {
        total: 0,
        successful: 0,
        failed: 0,
        averageResponseTime: 0
      },
      endpoints: {},
      errors: {
        total: 0,
        byType: {}
      },
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      startTime: new Date()
    }
    this.requestTimes = []
  }
}

// Singleton instance
export const monitoring = new MonitoringService()

// Express middleware for request monitoring
export const monitoringMiddleware = (req: Request, res: Response, next: Function) => {
  if (!config.monitoring.enableMetrics) {
    return next()
  }

  const startTime = Date.now()

  res.on('finish', () => {
    const responseTime = Date.now() - startTime
    monitoring.recordRequest(req, res, responseTime)
  })

  next()
}

// Logger utility
export class Logger {
  private static formatMessage(level: string, message: string, meta?: any): string {
    const timestamp = new Date().toISOString()
    const metaStr = meta ? ` | ${JSON.stringify(meta)}` : ''
    return `[${timestamp}] [${level.toUpperCase()}] ${message}${metaStr}`
  }

  static info(message: string, meta?: any): void {
    if (this.shouldLog('info')) {
      console.log(this.formatMessage('info', message, meta))
    }
  }

  static warn(message: string, meta?: any): void {
    if (this.shouldLog('warn')) {
      console.warn(this.formatMessage('warn', message, meta))
    }
  }

  static error(message: string, error?: Error | any, meta?: any): void {
    if (this.shouldLog('error')) {
      const errorMeta = error instanceof Error
        ? { name: error.name, message: error.message, stack: error.stack, ...meta }
        : { error, ...meta }

      console.error(this.formatMessage('error', message, errorMeta))

      // Record error in monitoring
      if (error instanceof Error) {
        monitoring.recordError(error, message)

        // Send to Sentry if configured
        if (config.monitoring.sentryDsn) {
          captureException(error, {
            extra: { context: message, ...meta }
          })
        }
      }
    }
  }

  static debug(message: string, meta?: any): void {
    if (this.shouldLog('debug')) {
      console.debug(this.formatMessage('debug', message, meta))
    }
  }

  private static shouldLog(level: string): boolean {
    const logLevels = ['error', 'warn', 'info', 'debug']
    const currentLevelIndex = logLevels.indexOf(config.monitoring.logLevel)
    const messageLevelIndex = logLevels.indexOf(level)
    
    return messageLevelIndex <= currentLevelIndex
  }
}

// Health check utilities
export const healthChecks = {
  // Database health check
  async database(): Promise<{ status: 'healthy' | 'unhealthy', details?: string }> {
    try {
      const { prisma } = await import('../lib/prisma')
      await prisma.$queryRaw`SELECT 1`
      return { status: 'healthy' }
    } catch (error) {
      return { 
        status: 'unhealthy', 
        details: error instanceof Error ? error.message : 'Database connection failed' 
      }
    }
  },

  // Auth0 health check
  async auth0(): Promise<{ status: 'healthy' | 'unhealthy' | 'development', details?: string }> {
    try {
      if (config.auth.auth0Domain === 'local-dev.auth0.com') {
        return { status: 'development', details: 'Using development Auth0 configuration' }
      }

      const response = await fetch(`https://${config.auth.auth0Domain}/.well-known/jwks.json`, {
        timeout: 5000
      } as any)
      
      if (response.ok) {
        return { status: 'healthy' }
      } else {
        return { status: 'unhealthy', details: `HTTP ${response.status}` }
      }
    } catch (error) {
      return { 
        status: 'unhealthy', 
        details: error instanceof Error ? error.message : 'Auth0 connection failed' 
      }
    }
  },

  // Memory health check
  memory(): { status: 'healthy' | 'warning' | 'critical', details: any } {
    const usage = process.memoryUsage()
    const heapUsedMB = usage.heapUsed / 1024 / 1024
    const heapTotalMB = usage.heapTotal / 1024 / 1024
    const usagePercentage = (heapUsedMB / heapTotalMB) * 100

    let status: 'healthy' | 'warning' | 'critical'
    if (usagePercentage > 90 || heapUsedMB > 500) {
      status = 'critical'
    } else if (usagePercentage > 75 || heapUsedMB > 300) {
      status = 'warning'
    } else {
      status = 'healthy'
    }

    return {
      status,
      details: {
        heapUsedMB: Math.round(heapUsedMB),
        heapTotalMB: Math.round(heapTotalMB),
        usagePercentage: Math.round(usagePercentage),
        rss: Math.round(usage.rss / 1024 / 1024),
        external: Math.round(usage.external / 1024 / 1024)
      }
    }
  }
}