import { prisma } from './prisma'
import { Logger } from './monitoring'
import { notificationService } from './notification-service'
import { weatherService } from './weather-service'
import { BucketListItem, ItemCategory, ItemPriority, NotificationType } from '@prisma/client'

export interface ReminderContext {
  location?: {
    latitude: number
    longitude: number
    city?: string
    country?: string
  }
  weather?: {
    temperature: number
    condition: string
    season: string
  }
  timeOfYear?: {
    month: number
    season: 'spring' | 'summer' | 'fall' | 'winter'
  }
  userPreferences?: {
    reminderFrequency: 'daily' | 'weekly' | 'monthly'
    timeOfDay: 'morning' | 'afternoon' | 'evening'
  }
}

export interface ReminderSuggestion {
  itemId: string
  title: string
  reason: string
  priority: 'high' | 'medium' | 'low'
  suggestedDate?: Date
  context: {
    type: 'location' | 'weather' | 'seasonal' | 'deadline' | 'milestone'
    details: string
  }
}

class ReminderService {
  private isInitialized = false

  constructor() {
    this.initialize()
  }

  private async initialize() {
    try {
      Logger.info('Initializing reminder service...')
      this.isInitialized = true
      Logger.info('Reminder service initialized successfully')
    } catch (error) {
      Logger.error('Failed to initialize reminder service', error)
      this.isInitialized = false
    }
  }

  /**
   * Generate contextual reminders for a user based on location, weather, and season
   */
  async generateContextualReminders(userId: string, context: ReminderContext): Promise<ReminderSuggestion[]> {
    try {
      // Get user's bucket list items that are not completed
      const items = await prisma.bucketListItem.findMany({
        where: {
          userId,
          status: {
            in: ['NOT_STARTED', 'IN_PROGRESS']
          }
        },
        orderBy: [
          { priority: 'asc' }, // MUST_DO first
          { createdAt: 'desc' }
        ]
      })

      const suggestions: ReminderSuggestion[] = []

      for (const item of items) {
        const itemSuggestions = await this.analyzeItemForReminders(item, context)
        suggestions.push(...itemSuggestions)
      }

      // Sort by priority and limit to top 10
      return suggestions
        .sort((a, b) => {
          const priorityOrder = { high: 0, medium: 1, low: 2 }
          return priorityOrder[a.priority] - priorityOrder[b.priority]
        })
        .slice(0, 10)
    } catch (error) {
      Logger.error('Failed to generate contextual reminders', error)
      throw new Error('Failed to generate contextual reminders')
    }
  }

  /**
   * Analyze a single item for reminder opportunities
   */
  private async analyzeItemForReminders(item: BucketListItem, context: ReminderContext): Promise<ReminderSuggestion[]> {
    const suggestions: ReminderSuggestion[] = []

    // Location-based reminders
    if (context.location && this.isLocationRelevant(item, context.location)) {
      suggestions.push({
        itemId: item.id,
        title: item.title,
        reason: `You're near a location relevant to "${item.title}". Perfect time to make progress!`,
        priority: item.priority === 'MUST_DO' ? 'high' : 'medium',
        context: {
          type: 'location',
          details: `Near ${context.location.city || 'relevant location'}`
        }
      })
    }

    // Weather-based reminders
    if (context.weather && this.isWeatherSuitable(item, context.weather)) {
      suggestions.push({
        itemId: item.id,
        title: item.title,
        reason: `Perfect weather for "${item.title}"! ${context.weather.condition} conditions are ideal.`,
        priority: 'medium',
        context: {
          type: 'weather',
          details: `${context.weather.condition}, ${context.weather.temperature}°`
        }
      })
    }

    // Seasonal reminders
    if (context.timeOfYear && this.isSeasonallyRelevant(item, context.timeOfYear)) {
      suggestions.push({
        itemId: item.id,
        title: item.title,
        reason: `${context.timeOfYear.season} is the perfect season for "${item.title}"!`,
        priority: 'medium',
        suggestedDate: this.calculateOptimalDate(context.timeOfYear),
        context: {
          type: 'seasonal',
          details: `${context.timeOfYear.season} activity`
        }
      })
    }

    // Deadline-based reminders
    if (item.targetDate && this.isDeadlineApproaching(item.targetDate)) {
      const daysUntil = Math.ceil((item.targetDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24))
      suggestions.push({
        itemId: item.id,
        title: item.title,
        reason: `Only ${daysUntil} days left to complete "${item.title}"!`,
        priority: daysUntil <= 7 ? 'high' : 'medium',
        context: {
          type: 'deadline',
          details: `${daysUntil} days remaining`
        }
      })
    }

    // Milestone reminders (items that haven't been updated in a while)
    if (this.needsProgressUpdate(item)) {
      const daysSinceUpdate = Math.floor((Date.now() - item.updatedAt.getTime()) / (1000 * 60 * 60 * 24))
      suggestions.push({
        itemId: item.id,
        title: item.title,
        reason: `It's been ${daysSinceUpdate} days since you updated "${item.title}". Time for a progress check!`,
        priority: 'low',
        context: {
          type: 'milestone',
          details: `${daysSinceUpdate} days since last update`
        }
      })
    }

    return suggestions
  }

  /**
   * Check if current location is relevant to the item
   */
  private isLocationRelevant(item: BucketListItem, location: { city?: string; country?: string }): boolean {
    const itemText = `${item.title} ${item.description || ''}`.toLowerCase()
    
    if (location.city && itemText.includes(location.city.toLowerCase())) {
      return true
    }
    
    if (location.country && itemText.includes(location.country.toLowerCase())) {
      return true
    }

    // Check for travel-related items when in interesting locations
    if (item.category === 'TRAVEL' && location.city) {
      const travelKeywords = ['visit', 'explore', 'see', 'tour', 'travel', 'trip']
      return travelKeywords.some(keyword => itemText.includes(keyword))
    }

    return false
  }

  /**
   * Check if current weather is suitable for the item
   */
  private isWeatherSuitable(item: BucketListItem, weather: { condition: string; temperature: number }): boolean {
    const itemText = `${item.title} ${item.description || ''}`.toLowerCase()
    const condition = weather.condition.toLowerCase()

    // Outdoor activities
    if (condition.includes('sunny') || condition.includes('clear')) {
      const outdoorKeywords = ['hike', 'bike', 'run', 'walk', 'outdoor', 'park', 'beach', 'garden']
      if (outdoorKeywords.some(keyword => itemText.includes(keyword))) {
        return true
      }
    }

    // Winter activities
    if (condition.includes('snow') || weather.temperature < 32) {
      const winterKeywords = ['ski', 'snowboard', 'ice', 'winter', 'snow']
      if (winterKeywords.some(keyword => itemText.includes(keyword))) {
        return true
      }
    }

    // Rainy day activities
    if (condition.includes('rain') || condition.includes('storm')) {
      const indoorKeywords = ['read', 'write', 'learn', 'study', 'museum', 'movie', 'cook']
      if (indoorKeywords.some(keyword => itemText.includes(keyword))) {
        return true
      }
    }

    return false
  }

  /**
   * Check if item is seasonally relevant
   */
  private isSeasonallyRelevant(item: BucketListItem, timeOfYear: { season: string }): boolean {
    const itemText = `${item.title} ${item.description || ''}`.toLowerCase()
    const season = timeOfYear.season.toLowerCase()

    const seasonalKeywords = {
      spring: ['spring', 'bloom', 'flower', 'garden', 'easter'],
      summer: ['summer', 'beach', 'swim', 'vacation', 'festival', 'camping'],
      fall: ['fall', 'autumn', 'harvest', 'halloween', 'thanksgiving', 'leaves'],
      winter: ['winter', 'christmas', 'holiday', 'ski', 'snow', 'ice']
    }

    const keywords = seasonalKeywords[season as keyof typeof seasonalKeywords] || []
    return keywords.some(keyword => itemText.includes(keyword))
  }

  /**
   * Check if deadline is approaching
   */
  private isDeadlineApproaching(targetDate: Date): boolean {
    const now = new Date()
    const daysUntil = Math.ceil((targetDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    return daysUntil <= 30 && daysUntil > 0 // Within 30 days
  }

  /**
   * Check if item needs a progress update
   */
  private needsProgressUpdate(item: BucketListItem): boolean {
    const daysSinceUpdate = Math.floor((Date.now() - item.updatedAt.getTime()) / (1000 * 60 * 60 * 24))
    
    // High priority items: remind after 3 days
    if (item.priority === 'MUST_DO' && daysSinceUpdate >= 3) {
      return true
    }
    
    // Medium priority items: remind after 7 days
    if (item.priority === 'WANT_TO_DO' && daysSinceUpdate >= 7) {
      return true
    }
    
    // Low priority items: remind after 14 days
    if (item.priority === 'SOMEDAY' && daysSinceUpdate >= 14) {
      return true
    }

    return false
  }

  /**
   * Calculate optimal date for seasonal activities
   */
  private calculateOptimalDate(timeOfYear: { month: number; season: string }): Date {
    const now = new Date()
    const optimalDate = new Date(now)
    
    // Add 1-7 days for immediate seasonal opportunities
    optimalDate.setDate(now.getDate() + Math.floor(Math.random() * 7) + 1)
    
    return optimalDate
  }

  /**
   * Send reminder notifications based on suggestions
   */
  async sendReminderNotifications(userId: string, suggestions: ReminderSuggestion[]) {
    try {
      for (const suggestion of suggestions.slice(0, 3)) { // Limit to top 3 to avoid spam
        await notificationService.createNotification({
          userId,
          type: 'REMINDER',
          title: 'Perfect Time for Your Goal!',
          message: suggestion.reason,
          data: {
            itemId: suggestion.itemId,
            context: suggestion.context,
            priority: suggestion.priority
          }
        })
      }

      Logger.info('Reminder notifications sent', {
        userId,
        count: Math.min(suggestions.length, 3)
      })
    } catch (error) {
      Logger.error('Failed to send reminder notifications', error)
      throw new Error('Failed to send reminder notifications')
    }
  }

  /**
   * Check if service is available
   */
  isAvailable(): boolean {
    return this.isInitialized
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      supportedContexts: ['location', 'weather', 'seasonal', 'deadline', 'milestone'],
      weatherServiceAvailable: weatherService.isAvailable(),
      notificationServiceAvailable: notificationService.isAvailable()
    }
  }
}

// Export singleton instance
export const reminderService = new ReminderService()
