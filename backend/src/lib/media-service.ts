import { MediaType } from '@prisma/client'
import { s3Service, UploadResult } from './s3'
import { imageProcessingService, ImageVariant } from './image-processing'
import { virusScannerService } from './virus-scanner'
import { Logger } from './monitoring'
import { captureException } from './sentry'

export interface MediaUploadOptions {
  itemId: string
  userId: string
  caption?: string
  generateVariants?: boolean
}

export interface MediaUploadResult {
  id: string
  fileUrl: string
  fileType: MediaType
  fileSize: number
  caption?: string
  metadata: any
  variants?: MediaVariant[]
}

export interface MediaVariant {
  name: string
  url: string
  width: number
  height: number
  size: number
}

export interface ProcessedMediaFile {
  original: UploadResult
  variants?: UploadResult[]
  metadata: {
    originalName: string
    mimetype: string
    dimensions?: { width: number; height: number }
    variants?: Array<{
      name: string
      dimensions: { width: number; height: number }
      size: number
    }>
  }
}

class MediaService {
  /**
   * Process and upload a media file
   */
  async uploadMediaFile(
    file: Express.Multer.File,
    options: MediaUploadOptions
  ): Promise<ProcessedMediaFile> {
    try {
      Logger.info('Starting media file upload', {
        filename: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        itemId: options.itemId
      })

      // 1. Virus scan
      const scanResult = await virusScannerService.scanFile(
        file.buffer,
        file.originalname,
        file.mimetype
      )

      if (!scanResult.isClean) {
        throw new Error(`File rejected by virus scanner: ${scanResult.threat}`)
      }

      // 2. Determine media type
      const mediaType = this.getMediaType(file.mimetype)
      
      // 3. Process based on file type
      if (this.isImage(file.mimetype)) {
        return await this.processImageFile(file, options)
      } else if (this.isVideo(file.mimetype)) {
        return await this.processVideoFile(file, options)
      } else if (this.isAudio(file.mimetype)) {
        return await this.processAudioFile(file, options)
      } else {
        throw new Error(`Unsupported media type: ${file.mimetype}`)
      }
    } catch (error) {
      Logger.error('Media file upload failed', error)
      captureException(error as Error, {
        extra: {
          filename: file.originalname,
          mimetype: file.mimetype,
          size: file.size,
          options
        }
      })
      throw error
    }
  }

  /**
   * Process image file with variants
   */
  private async processImageFile(
    file: Express.Multer.File,
    options: MediaUploadOptions
  ): Promise<ProcessedMediaFile> {
    try {
      // Validate image
      const validation = await imageProcessingService.validateImage(file.buffer)
      if (!validation.isValid) {
        throw new Error(`Invalid image: ${validation.error}`)
      }

      // Get image metadata
      const imageMetadata = await imageProcessingService.getImageMetadata(file.buffer)

      // Upload original image
      const folder = `items/${options.itemId}/images`
      const originalUpload = await s3Service.uploadFile(
        file.buffer,
        file.mimetype,
        folder,
        file.originalname
      )

      let variantUploads: UploadResult[] = []
      let variantMetadata: Array<{
        name: string
        dimensions: { width: number; height: number }
        size: number
      }> = []

      // Generate and upload variants if requested
      if (options.generateVariants !== false) {
        const variants = await imageProcessingService.createImageVariants(file.buffer)
        
        variantUploads = await Promise.all(
          variants.map(async (variant) => {
            const variantFolder = `items/${options.itemId}/images/variants`
            return await s3Service.uploadFile(
              variant.buffer,
              variant.contentType,
              variantFolder,
              `${variant.name}-${file.originalname}`
            )
          })
        )

        variantMetadata = variants.map((variant, index) => ({
          name: variant.name,
          dimensions: { width: variant.width, height: variant.height },
          size: variant.size
        }))
      }

      return {
        original: originalUpload,
        variants: variantUploads,
        metadata: {
          originalName: file.originalname,
          mimetype: file.mimetype,
          dimensions: { width: imageMetadata.width, height: imageMetadata.height },
          variants: variantMetadata
        }
      }
    } catch (error) {
      Logger.error('Image processing failed', error)
      throw error
    }
  }

  /**
   * Process video file
   */
  private async processVideoFile(
    file: Express.Multer.File,
    options: MediaUploadOptions
  ): Promise<ProcessedMediaFile> {
    try {
      // Upload video file
      const folder = `items/${options.itemId}/videos`
      const upload = await s3Service.uploadFile(
        file.buffer,
        file.mimetype,
        folder,
        file.originalname
      )

      // TODO: Add video processing (thumbnail generation, compression)
      // This would require ffmpeg integration

      return {
        original: upload,
        metadata: {
          originalName: file.originalname,
          mimetype: file.mimetype
        }
      }
    } catch (error) {
      Logger.error('Video processing failed', error)
      throw error
    }
  }

  /**
   * Process audio file
   */
  private async processAudioFile(
    file: Express.Multer.File,
    options: MediaUploadOptions
  ): Promise<ProcessedMediaFile> {
    try {
      // Upload audio file
      const folder = `items/${options.itemId}/audio`
      const upload = await s3Service.uploadFile(
        file.buffer,
        file.mimetype,
        folder,
        file.originalname
      )

      // TODO: Add audio processing (compression, format conversion)
      // This would require ffmpeg integration

      return {
        original: upload,
        metadata: {
          originalName: file.originalname,
          mimetype: file.mimetype
        }
      }
    } catch (error) {
      Logger.error('Audio processing failed', error)
      throw error
    }
  }

  /**
   * Delete media file and all its variants
   */
  async deleteMediaFile(fileUrl: string): Promise<void> {
    try {
      const key = s3Service.extractKeyFromUrl(fileUrl)
      if (!key) {
        throw new Error('Invalid file URL')
      }

      await s3Service.deleteFile(key)

      // TODO: Delete variants if they exist
      // This would require tracking variant keys in the database

      Logger.info('Media file deleted', { fileUrl, key })
    } catch (error) {
      Logger.error('Failed to delete media file', error)
      captureException(error as Error, {
        extra: { fileUrl }
      })
      throw error
    }
  }

  /**
   * Generate presigned upload URL for direct client uploads
   */
  async generateUploadUrl(
    itemId: string,
    contentType: string,
    filename: string
  ): Promise<{ url: string; key: string; fields: Record<string, string> }> {
    try {
      // Validate content type
      if (!virusScannerService.isSupportedMimeType(contentType)) {
        throw new Error(`Unsupported content type: ${contentType}`)
      }

      const mediaType = this.getMediaType(contentType)
      const folder = this.getFolderForMediaType(itemId, mediaType)

      return await s3Service.getPresignedUploadUrl(folder, contentType, {
        expiresIn: 3600, // 1 hour
        contentLength: 100 * 1024 * 1024 // 100MB max
      })
    } catch (error) {
      Logger.error('Failed to generate upload URL', error)
      throw error
    }
  }

  /**
   * Get media type from MIME type
   */
  private getMediaType(mimetype: string): MediaType {
    if (mimetype.startsWith('image/')) {
      return MediaType.IMAGE
    } else if (mimetype.startsWith('video/')) {
      return MediaType.VIDEO
    } else if (mimetype.startsWith('audio/')) {
      return MediaType.VOICE_NOTE
    } else {
      return MediaType.DOCUMENT
    }
  }

  /**
   * Check if file is an image
   */
  private isImage(mimetype: string): boolean {
    return mimetype.startsWith('image/')
  }

  /**
   * Check if file is a video
   */
  private isVideo(mimetype: string): boolean {
    return mimetype.startsWith('video/')
  }

  /**
   * Check if file is audio
   */
  private isAudio(mimetype: string): boolean {
    return mimetype.startsWith('audio/')
  }

  /**
   * Get folder path for media type
   */
  private getFolderForMediaType(itemId: string, mediaType: MediaType): string {
    const basePath = `items/${itemId}`
    
    switch (mediaType) {
      case MediaType.IMAGE:
        return `${basePath}/images`
      case MediaType.VIDEO:
        return `${basePath}/videos`
      case MediaType.VOICE_NOTE:
        return `${basePath}/audio`
      case MediaType.DOCUMENT:
        return `${basePath}/documents`
      default:
        return `${basePath}/misc`
    }
  }

  /**
   * Get supported MIME types
   */
  getSupportedMimeTypes(): string[] {
    return virusScannerService.getSupportedMimeTypes()
  }
}

// Singleton instance
export const mediaService = new MediaService()
export default mediaService
