import { prisma } from './prisma'
import { Logger } from './monitoring'
import { config } from '../config'
import { NotificationType } from '@prisma/client'
import { pushNotificationService } from './push-notification-service'
import { emailService } from './email-service'

export interface CreateNotificationData {
  userId: string
  type: NotificationType
  title: string
  message: string
  data?: any
}

export interface NotificationPreferences {
  pushNotifications: boolean
  emailNotifications: boolean
  reminderNotifications: boolean
  achievementNotifications: boolean
  socialNotifications: boolean
}

export interface PushNotificationPayload {
  title: string
  body: string
  icon?: string
  badge?: string
  data?: any
  actions?: Array<{
    action: string
    title: string
    icon?: string
  }>
}

class NotificationService {
  private isInitialized = false

  constructor() {
    this.initialize()
  }

  private async initialize() {
    try {
      Logger.info('Initializing notification service...')
      this.isInitialized = true
      Logger.info('Notification service initialized successfully')
    } catch (error) {
      Logger.error('Failed to initialize notification service', error)
      this.isInitialized = false
    }
  }

  /**
   * Create a new notification
   */
  async createNotification(data: CreateNotificationData, emitRealtime: boolean = true) {
    try {
      const notification = await prisma.notification.create({
        data: {
          userId: data.userId,
          type: data.type,
          title: data.title,
          message: data.message,
          data: data.data || null,
          isRead: false,
          createdAt: new Date()
        }
      })

      Logger.info('Notification created', {
        notificationId: notification.id,
        userId: data.userId,
        type: data.type
      })

      // Send push notification if user has enabled it
      await this.sendPushNotificationIfEnabled(data.userId, {
        title: data.title,
        body: data.message,
        data: { notificationId: notification.id, ...data.data }
      })

      // Send email notification if user has enabled it
      await this.sendEmailNotificationIfEnabled(data.userId, data)

      // Emit real-time event if requested (avoid circular imports)
      if (emitRealtime) {
        // Import here to avoid circular dependency
        const { realtimeService } = await import('./realtime-service')
        await realtimeService.handleNewNotification(notification, data.userId)
      }

      return notification
    } catch (error) {
      Logger.error('Failed to create notification', error)
      throw new Error('Failed to create notification')
    }
  }

  /**
   * Get notifications for a user
   */
  async getUserNotifications(userId: string, options: {
    limit?: number
    offset?: number
    unreadOnly?: boolean
  } = {}) {
    try {
      const { limit = 20, offset = 0, unreadOnly = false } = options

      const where = {
        userId,
        ...(unreadOnly ? { isRead: false } : {})
      }

      const [notifications, total] = await Promise.all([
        prisma.notification.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          take: limit,
          skip: offset
        }),
        prisma.notification.count({ where })
      ])

      return {
        notifications,
        total,
        hasMore: offset + limit < total
      }
    } catch (error) {
      Logger.error('Failed to get user notifications', error)
      throw new Error('Failed to get user notifications')
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string, userId: string) {
    try {
      const notification = await prisma.notification.update({
        where: {
          id: notificationId,
          userId // Ensure user owns the notification
        },
        data: {
          isRead: true,
          readAt: new Date()
        }
      })

      Logger.info('Notification marked as read', {
        notificationId,
        userId
      })

      return notification
    } catch (error) {
      Logger.error('Failed to mark notification as read', error)
      throw new Error('Failed to mark notification as read')
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllAsRead(userId: string) {
    try {
      const result = await prisma.notification.updateMany({
        where: {
          userId,
          isRead: false
        },
        data: {
          isRead: true,
          readAt: new Date()
        }
      })

      Logger.info('All notifications marked as read', {
        userId,
        count: result.count
      })

      return result
    } catch (error) {
      Logger.error('Failed to mark all notifications as read', error)
      throw new Error('Failed to mark all notifications as read')
    }
  }

  /**
   * Delete a notification
   */
  async deleteNotification(notificationId: string, userId: string) {
    try {
      await prisma.notification.delete({
        where: {
          id: notificationId,
          userId // Ensure user owns the notification
        }
      })

      Logger.info('Notification deleted', {
        notificationId,
        userId
      })
    } catch (error) {
      Logger.error('Failed to delete notification', error)
      throw new Error('Failed to delete notification')
    }
  }

  /**
   * Get user notification preferences
   */
  async getUserPreferences(userId: string): Promise<NotificationPreferences> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { preferences: true }
      })

      if (!user) {
        throw new Error('User not found')
      }

      const preferences = user.preferences as any || {}
      const notifications = preferences.notifications || {}

      return {
        pushNotifications: notifications.push ?? true,
        emailNotifications: notifications.email ?? true,
        reminderNotifications: notifications.reminders ?? true,
        achievementNotifications: notifications.achievements ?? true,
        socialNotifications: notifications.social ?? true
      }
    } catch (error) {
      Logger.error('Failed to get user notification preferences', error)
      throw new Error('Failed to get user notification preferences')
    }
  }

  /**
   * Send push notification if user has enabled it
   */
  private async sendPushNotificationIfEnabled(userId: string, payload: PushNotificationPayload) {
    try {
      const preferences = await this.getUserPreferences(userId)

      if (!preferences.pushNotifications) {
        Logger.debug('Push notifications disabled for user', { userId })
        return
      }

      if (!pushNotificationService.isAvailable()) {
        Logger.warn('Push notification service not available')
        return
      }

      await pushNotificationService.sendToUser(userId, payload)
    } catch (error) {
      Logger.error('Failed to send push notification', error)
    }
  }

  /**
   * Send email notification if user has enabled it
   */
  private async sendEmailNotificationIfEnabled(userId: string, data: CreateNotificationData) {
    try {
      const preferences = await this.getUserPreferences(userId)

      if (!preferences.emailNotifications) {
        Logger.debug('Email notifications disabled for user', { userId })
        return
      }

      if (!emailService.isAvailable()) {
        Logger.warn('Email service not available')
        return
      }

      // Get user email
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { email: true }
      })

      if (!user) {
        Logger.error('User not found for email notification', { userId })
        return
      }

      await emailService.sendNotification({
        to: user.email,
        subject: data.title,
        template: data.type.toLowerCase() as any,
        data: {
          title: data.title,
          message: data.message,
          ...data.data
        }
      })
    } catch (error) {
      Logger.error('Failed to send email notification', error)
    }
  }

  /**
   * Get unread notification count for a user
   */
  async getUnreadCount(userId: string): Promise<number> {
    try {
      const count = await prisma.notification.count({
        where: {
          userId,
          isRead: false
        }
      })

      return count
    } catch (error) {
      Logger.error('Failed to get unread notification count', error)
      throw new Error('Failed to get unread notification count')
    }
  }

  /**
   * Check if service is available
   */
  isAvailable(): boolean {
    return this.isInitialized
  }

  /**
   * Send shared list invitation notification
   */
  async sendSharedListInvitation(userId: string, listName: string, inviterName: string) {
    try {
      await this.createNotification({
        userId,
        type: NotificationType.SHARED_LIST_INVITE,
        title: 'Shared List Invitation',
        message: `${inviterName} invited you to collaborate on "${listName}"`,
        data: { listName, inviterName }
      })

      Logger.debug('Shared list invitation sent', { userId, listName, inviterName })

    } catch (error) {
      Logger.error('Failed to send shared list invitation', error)
    }
  }
}

// Export singleton instance
export const notificationService = new NotificationService()
