import { prisma } from './prisma'
import { Logger } from './monitoring'
import { socketService } from './socket-service'
import { notificationService } from './notification-service'
import { BucketListItem, ItemStatus, ItemPriority, ItemCategory, NotificationType } from '@prisma/client'

export interface ConflictResolution {
  itemId: string
  conflictType: 'concurrent_edit' | 'version_mismatch'
  originalData: any
  conflictingData: any
  resolvedData: any
  resolvedBy: string
  resolvedAt: Date
}

export interface ItemUpdateData {
  id?: string
  title?: string
  description?: string
  status?: ItemStatus
  priority?: ItemPriority
  category?: ItemCategory
  targetDate?: Date
  location?: any // JSON field
  tags?: any // JSON field
  estimatedCost?: number
  completionPercentage?: number
  completedAt?: Date
  version?: number
  lastModifiedBy?: string
}

class RealtimeService {
  private isInitialized = false

  constructor() {
    this.initialize()
  }

  private async initialize() {
    try {
      Logger.info('Initializing realtime service...')
      this.isInitialized = true
      Logger.info('Realtime service initialized successfully')
    } catch (error) {
      Logger.error('Failed to initialize realtime service', error)
      this.isInitialized = false
    }
  }

  /**
   * Handle bucket list item creation
   */
  async handleItemCreated(item: BucketListItem, userId: string): Promise<void> {
    try {
      // Emit to user's personal room
      socketService.emitToUser(userId, 'bucketListItemCreated', {
        item,
        userId
      })

      // Check if item belongs to any shared lists
      const sharedListItems = await prisma.sharedListItem.findMany({
        where: { bucketListItemId: item.id },
        include: { sharedList: true }
      })

      // Emit to shared list rooms and notify collaborators
      for (const sharedListItem of sharedListItems) {
        const sharedListId = sharedListItem.sharedListId

        socketService.emitToSharedList(sharedListId, 'bucketListItemCreated', {
          item,
          userId
        })

        // Notify other collaborators
        await this.notifySharedListMembers(
          sharedListId,
          userId,
          `${item.title} was added to the shared list`,
          NotificationType.SHARED_LIST_INVITE
        )
      }

      Logger.debug('Item creation broadcasted', {
        itemId: item.id,
        userId,
        sharedListCount: sharedListItems.length
      })

    } catch (error) {
      Logger.error('Failed to handle item creation', error)
    }
  }

  /**
   * Handle bucket list item update with conflict detection
   */
  async handleItemUpdated(
    itemId: string,
    updateData: Partial<ItemUpdateData>,
    userId: string
  ): Promise<{ success: boolean; conflict?: ConflictResolution }> {
    try {
      // Get current item from database
      const currentItem = await prisma.bucketListItem.findUnique({
        where: { id: itemId }
      })

      if (!currentItem) {
        throw new Error('Item not found')
      }

      // Check for version conflict
      if (updateData.version && currentItem.version !== updateData.version) {
        const conflict = await this.handleVersionConflict(
          currentItem,
          updateData,
          userId
        )
        return { success: false, conflict }
      }

      // Prepare update data with version increment
      const { version: _, ...updateFields } = updateData
      const updatedData = {
        ...updateFields,
        version: currentItem.version + 1,
        lastModifiedBy: userId,
        updatedAt: new Date()
      }

      // Update item in database
      const updatedItem = await prisma.bucketListItem.update({
        where: { id: itemId },
        data: updatedData
      })

      // Calculate changes for real-time event
      const changes = this.calculateChanges(currentItem, updatedItem)

      // Emit to user's personal room
      socketService.emitToUser(userId, 'bucketListItemUpdated', {
        item: updatedItem,
        userId,
        changes
      })

      // Check if item belongs to any shared lists
      const sharedListItems = await prisma.sharedListItem.findMany({
        where: { bucketListItemId: itemId },
        include: { sharedList: true }
      })

      // Emit to shared list rooms and notify collaborators
      for (const sharedListItem of sharedListItems) {
        const sharedListId = sharedListItem.sharedListId

        socketService.emitToSharedList(sharedListId, 'bucketListItemUpdated', {
          item: updatedItem,
          userId,
          changes
        })

        // Notify other collaborators about significant changes
        if (this.isSignificantChange(changes)) {
          await this.notifySharedListMembers(
            sharedListId,
            userId,
            `${updatedItem.title} was updated in the shared list`,
            NotificationType.PROGRESS_UPDATE
          )
        }
      }

      Logger.debug('Item update broadcasted', {
        itemId,
        userId,
        changes: Object.keys(changes),
        version: updatedItem.version
      })

      return { success: true }

    } catch (error) {
      Logger.error('Failed to handle item update', error)
      throw error
    }
  }

  /**
   * Handle bucket list item deletion
   */
  async handleItemDeleted(itemId: string, userId: string): Promise<void> {
    try {
      // Get item and its shared list associations before deletion
      const item = await prisma.bucketListItem.findUnique({
        where: { id: itemId },
        select: {
          id: true,
          title: true
        }
      })

      if (!item) {
        throw new Error('Item not found')
      }

      // Get shared list associations before deletion
      const sharedListItems = await prisma.sharedListItem.findMany({
        where: { bucketListItemId: itemId },
        include: { sharedList: true }
      })

      // Delete item from database (cascade will handle shared list items)
      await prisma.bucketListItem.delete({
        where: { id: itemId }
      })

      // Emit to user's personal room
      socketService.emitToUser(userId, 'bucketListItemDeleted', {
        itemId,
        userId
      })

      // Emit to shared list rooms and notify collaborators
      for (const sharedListItem of sharedListItems) {
        const sharedListId = sharedListItem.sharedListId

        socketService.emitToSharedList(sharedListId, 'bucketListItemDeleted', {
          itemId,
          userId
        })

        // Notify other collaborators
        await this.notifySharedListMembers(
          sharedListId,
          userId,
          `${item.title} was removed from the shared list`,
          NotificationType.SHARED_LIST_INVITE
        )
      }

      Logger.debug('Item deletion broadcasted', {
        itemId,
        userId,
        sharedListCount: sharedListItems.length
      })

    } catch (error) {
      Logger.error('Failed to handle item deletion', error)
      throw error
    }
  }

  /**
   * Handle bucket list item status change
   */
  async handleItemStatusChanged(
    itemId: string,
    oldStatus: ItemStatus,
    newStatus: ItemStatus,
    userId: string
  ): Promise<void> {
    try {
      const item = await prisma.bucketListItem.findUnique({
        where: { id: itemId },
        select: {
          id: true,
          title: true
        }
      })

      if (!item) {
        throw new Error('Item not found')
      }

      // Emit status change event
      const statusChangeData = {
        itemId,
        oldStatus,
        newStatus,
        userId
      }

      socketService.emitToUser(userId, 'bucketListStatusChanged', statusChangeData)

      // Check if item belongs to any shared lists
      const sharedListItems = await prisma.sharedListItem.findMany({
        where: { bucketListItemId: itemId },
        include: { sharedList: true }
      })

      // Emit to shared list rooms and notify collaborators
      for (const sharedListItem of sharedListItems) {
        const sharedListId = sharedListItem.sharedListId

        socketService.emitToSharedList(sharedListId, 'bucketListStatusChanged', statusChangeData)

        // Notify collaborators about completion
        if (newStatus === ItemStatus.COMPLETED) {
          await this.notifySharedListMembers(
            sharedListId,
            userId,
            `${item.title} was completed!`,
            NotificationType.ACHIEVEMENT
          )
        }
      }

      Logger.debug('Item status change broadcasted', {
        itemId,
        oldStatus,
        newStatus,
        userId
      })

    } catch (error) {
      Logger.error('Failed to handle item status change', error)
    }
  }

  /**
   * Handle user joining shared list
   */
  async handleUserJoinedSharedList(listId: string, userId: string): Promise<void> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          displayName: true,
          email: true
        }
      })

      if (!user) {
        throw new Error('User not found')
      }

      // Emit to shared list room
      socketService.emitToSharedList(listId, 'sharedListUserJoined', {
        listId,
        user
      })

      // Notify existing members
      await this.notifySharedListMembers(
        listId,
        userId,
        `${user.displayName} joined the shared list`,
        NotificationType.SHARED_LIST_INVITE
      )

      Logger.debug('User joined shared list broadcasted', {
        listId,
        userId,
        userName: user.displayName
      })

    } catch (error) {
      Logger.error('Failed to handle user joined shared list', error)
    }
  }

  /**
   * Handle user leaving shared list
   */
  async handleUserLeftSharedList(listId: string, userId: string): Promise<void> {
    try {
      // Emit to shared list room
      socketService.emitToSharedList(listId, 'sharedListUserLeft', {
        listId,
        userId
      })

      Logger.debug('User left shared list broadcasted', {
        listId,
        userId
      })

    } catch (error) {
      Logger.error('Failed to handle user left shared list', error)
    }
  }

  /**
   * Handle shared list creation
   */
  async handleSharedListCreated(sharedList: any, userId: string): Promise<void> {
    try {
      // Emit to user's personal room
      socketService.emitToUser(userId, 'sharedListCreated', {
        sharedList,
        userId
      })

      Logger.debug('Shared list creation broadcasted', {
        listId: sharedList.id,
        userId,
        listName: sharedList.name
      })

    } catch (error) {
      Logger.error('Failed to handle shared list creation', error)
    }
  }

  /**
   * Handle shared list update
   */
  async handleSharedListUpdated(sharedList: any, userId: string): Promise<void> {
    try {
      // Emit to shared list room
      socketService.emitToSharedList(sharedList.id, 'sharedListUpdated', {
        sharedList,
        userId
      })

      // Notify members
      await this.notifySharedListMembers(
        sharedList.id,
        userId,
        `${sharedList.name} was updated`,
        NotificationType.SHARED_LIST_INVITE
      )

      Logger.debug('Shared list update broadcasted', {
        listId: sharedList.id,
        userId,
        listName: sharedList.name
      })

    } catch (error) {
      Logger.error('Failed to handle shared list update', error)
    }
  }

  /**
   * Handle member added to shared list
   */
  async handleSharedListMemberAdded(listId: string, member: any, userId: string): Promise<void> {
    try {
      // Emit to shared list room
      socketService.emitToSharedList(listId, 'sharedListMemberAdded', {
        listId,
        member,
        userId
      })

      // Join the new member to the shared list room
      socketService.joinSharedListRoom(member.userId, listId)

      Logger.debug('Shared list member added broadcasted', {
        listId,
        memberId: member.userId,
        addedBy: userId
      })

    } catch (error) {
      Logger.error('Failed to handle shared list member added', error)
    }
  }

  /**
   * Handle item added to shared list
   */
  async handleSharedListItemAdded(listId: string, item: any, userId: string): Promise<void> {
    try {
      // Emit to shared list room
      socketService.emitToSharedList(listId, 'sharedListItemAdded', {
        listId,
        item,
        userId
      })

      // Notify members
      await this.notifySharedListMembers(
        listId,
        userId,
        `${item.bucketListItem.title} was added to the shared list`,
        NotificationType.SHARED_LIST_INVITE
      )

      Logger.debug('Shared list item added broadcasted', {
        listId,
        itemId: item.bucketListItem.id,
        addedBy: userId
      })

    } catch (error) {
      Logger.error('Failed to handle shared list item added', error)
    }
  }

  /**
   * Handle item removed from shared list
   */
  async handleSharedListItemRemoved(listId: string, itemId: string, userId: string): Promise<void> {
    try {
      // Emit to shared list room
      socketService.emitToSharedList(listId, 'sharedListItemRemoved', {
        listId,
        itemId,
        userId
      })

      Logger.debug('Shared list item removed broadcasted', {
        listId,
        itemId,
        removedBy: userId
      })

    } catch (error) {
      Logger.error('Failed to handle shared list item removed', error)
    }
  }

  /**
   * Handle new notification
   */
  async handleNewNotification(notification: any, userId: string): Promise<void> {
    try {
      // Emit to user's personal room
      socketService.emitToUser(userId, 'notificationNew', {
        notification,
        userId
      })

      Logger.debug('New notification broadcasted', {
        notificationId: notification.id,
        userId,
        type: notification.type
      })

    } catch (error) {
      Logger.error('Failed to handle new notification', error)
    }
  }

  /**
   * Handle version conflict
   */
  private async handleVersionConflict(
    currentItem: any,
    updateData: Partial<ItemUpdateData>,
    userId: string
  ): Promise<ConflictResolution> {
    try {
      const conflict: ConflictResolution = {
        itemId: currentItem.id,
        conflictType: 'version_mismatch',
        originalData: currentItem,
        conflictingData: updateData,
        resolvedData: null,
        resolvedBy: userId,
        resolvedAt: new Date()
      }

      // Attempt automatic conflict resolution
      const resolvedData = this.attemptAutoResolve(currentItem, updateData)
      
      if (resolvedData) {
        conflict.resolvedData = resolvedData
        
        // Apply resolved changes
        await prisma.bucketListItem.update({
          where: { id: currentItem.id },
          data: {
            ...resolvedData,
            version: currentItem.version + 1,
            lastModifiedBy: userId
          }
        })
      }

      // Notify affected users about conflict
      const affectedUsers = await this.getAffectedUsers(currentItem)
      
      for (const affectedUserId of affectedUsers) {
        socketService.emitToUser(affectedUserId, 'conflictDetected', {
          itemId: currentItem.id,
          conflictType: 'version_mismatch',
          conflictData: conflict,
          affectedUsers
        })
      }

      Logger.warn('Version conflict detected and handled', {
        itemId: currentItem.id,
        conflictType: 'version_mismatch',
        autoResolved: !!resolvedData,
        affectedUsers: affectedUsers.length
      })

      return conflict

    } catch (error) {
      Logger.error('Failed to handle version conflict', error)
      throw error
    }
  }

  /**
   * Attempt automatic conflict resolution
   */
  private attemptAutoResolve(currentItem: any, updateData: any): any | null {
    try {
      // Simple last-write-wins for non-conflicting fields
      const resolved = { ...currentItem }
      
      // Merge non-conflicting changes
      for (const [key, value] of Object.entries(updateData)) {
        if (key !== 'version' && key !== 'id') {
          resolved[key] = value
        }
      }

      return resolved
    } catch (error) {
      Logger.error('Failed to auto-resolve conflict', error)
      return null
    }
  }

  /**
   * Calculate changes between old and new item
   */
  private calculateChanges(oldItem: any, newItem: any): Record<string, any> {
    const changes: Record<string, any> = {}
    
    const fieldsToCheck = ['title', 'description', 'status', 'priority', 'category', 'targetDate', 'location', 'tags', 'notes']
    
    for (const field of fieldsToCheck) {
      if (oldItem[field] !== newItem[field]) {
        changes[field] = {
          old: oldItem[field],
          new: newItem[field]
        }
      }
    }

    return changes
  }

  /**
   * Check if change is significant enough to notify collaborators
   */
  private isSignificantChange(changes: Record<string, any>): boolean {
    const significantFields = ['title', 'status', 'targetDate']
    return significantFields.some(field => field in changes)
  }

  /**
   * Get affected users for conflict resolution
   */
  private async getAffectedUsers(item: any): Promise<string[]> {
    const users = [item.userId] // Item owner

    // Get shared list members for this item
    const sharedListItems = await prisma.sharedListItem.findMany({
      where: { bucketListItemId: item.id },
      include: {
        sharedList: {
          include: {
            members: {
              select: { userId: true }
            }
          }
        }
      }
    })

    // Add all shared list members
    for (const sharedListItem of sharedListItems) {
      const memberUserIds = sharedListItem.sharedList.members.map(m => m.userId)
      users.push(...memberUserIds)
    }

    return [...new Set(users)] // Remove duplicates
  }

  /**
   * Notify shared list members
   */
  private async notifySharedListMembers(
    sharedListId: string,
    excludeUserId: string,
    message: string,
    type: NotificationType
  ): Promise<void> {
    try {
      const members = await prisma.sharedListMember.findMany({
        where: {
          sharedListId,
          userId: { not: excludeUserId }
        },
        select: { userId: true }
      })

      for (const member of members) {
        await notificationService.createNotification({
          userId: member.userId,
          type,
          title: 'Shared List Update',
          message,
          data: { sharedListId }
        }, false) // Don't emit realtime to avoid circular dependency
      }

    } catch (error) {
      Logger.error('Failed to notify shared list members', error)
    }
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      socketServiceStatus: socketService.getStatus()
    }
  }
}

// Export singleton instance
export const realtimeService = new RealtimeService()
