import { Logger } from './monitoring'
import { config } from '../config'

export interface WeatherData {
  temperature: number
  condition: string
  description: string
  humidity: number
  windSpeed: number
  visibility: number
  uvIndex: number
  season: 'spring' | 'summer' | 'fall' | 'winter'
  isGoodWeather: boolean
  location: {
    city: string
    country: string
    latitude: number
    longitude: number
  }
}

export interface WeatherForecast {
  date: Date
  temperature: {
    min: number
    max: number
  }
  condition: string
  description: string
  precipitationChance: number
}

export interface ActivitySuggestion {
  itemId: string
  title: string
  reason: string
  weatherScore: number
  priority: 'high' | 'medium' | 'low'
  suggestedTime: Date
  weatherContext: {
    condition: string
    temperature: number
    isOptimal: boolean
    details: string
  }
}

class WeatherService {
  private isInitialized = false
  private apiKey?: string
  private baseUrl = 'https://api.openweathermap.org/data/2.5'

  constructor() {
    this.initialize()
  }

  private async initialize() {
    try {
      Logger.info('Initializing weather service...')
      
      this.apiKey = process.env.OPENWEATHER_API_KEY

      if (!this.apiKey) {
        Logger.warn('OpenWeather API key not configured - weather-based suggestions will be disabled')
      }

      this.isInitialized = true
      Logger.info('Weather service initialized successfully')
    } catch (error) {
      Logger.error('Failed to initialize weather service', error)
      this.isInitialized = false
    }
  }

  /**
   * Get current weather for location
   */
  async getCurrentWeather(latitude: number, longitude: number): Promise<WeatherData | null> {
    try {
      if (!this.apiKey) {
        Logger.warn('Weather API key not configured')
        return null
      }

      // In a real implementation, you would make an API call to OpenWeatherMap
      Logger.info('Weather data would be fetched', { latitude, longitude })

      // TODO: Implement actual OpenWeatherMap API call
      // Example:
      // const response = await fetch(
      //   `${this.baseUrl}/weather?lat=${latitude}&lon=${longitude}&appid=${this.apiKey}&units=metric`
      // )
      // const data = await response.json()

      // Mock weather data for now
      const mockWeatherData: WeatherData = {
        temperature: 22,
        condition: 'Clear',
        description: 'Clear sky',
        humidity: 65,
        windSpeed: 5.2,
        visibility: 10000,
        uvIndex: 6,
        season: this.getCurrentSeason(),
        isGoodWeather: true,
        location: {
          city: 'Sample City',
          country: 'Sample Country',
          latitude,
          longitude
        }
      }

      return mockWeatherData
    } catch (error) {
      Logger.error('Failed to get current weather', error)
      return null
    }
  }

  /**
   * Get weather forecast for location
   */
  async getWeatherForecast(latitude: number, longitude: number, days: number = 5): Promise<WeatherForecast[]> {
    try {
      if (!this.apiKey) {
        Logger.warn('Weather API key not configured')
        return []
      }

      Logger.info('Weather forecast would be fetched', { latitude, longitude, days })

      // TODO: Implement actual OpenWeatherMap forecast API call
      // Example:
      // const response = await fetch(
      //   `${this.baseUrl}/forecast?lat=${latitude}&lon=${longitude}&appid=${this.apiKey}&units=metric&cnt=${days * 8}`
      // )
      // const data = await response.json()

      // Mock forecast data for now
      const mockForecast: WeatherForecast[] = []
      for (let i = 0; i < days; i++) {
        const date = new Date()
        date.setDate(date.getDate() + i)
        
        mockForecast.push({
          date,
          temperature: {
            min: 15 + Math.random() * 5,
            max: 20 + Math.random() * 10
          },
          condition: (['Clear', 'Clouds', 'Rain'] as const)[Math.floor(Math.random() * 3)] || 'Clear',
          description: 'Sample weather description',
          precipitationChance: Math.random() * 100
        })
      }

      return mockForecast
    } catch (error) {
      Logger.error('Failed to get weather forecast', error)
      return []
    }
  }

  /**
   * Generate weather-based activity suggestions
   */
  async generateWeatherBasedSuggestions(
    userId: string,
    bucketListItems: any[],
    latitude: number,
    longitude: number
  ): Promise<ActivitySuggestion[]> {
    try {
      const weather = await this.getCurrentWeather(latitude, longitude)
      if (!weather) {
        return []
      }

      const suggestions: ActivitySuggestion[] = []

      for (const item of bucketListItems) {
        const suggestion = this.analyzeItemForWeather(item, weather)
        if (suggestion) {
          suggestions.push(suggestion)
        }
      }

      // Sort by weather score (highest first)
      suggestions.sort((a, b) => b.weatherScore - a.weatherScore)

      // Return top 5 suggestions
      return suggestions.slice(0, 5)
    } catch (error) {
      Logger.error('Failed to generate weather-based suggestions', error)
      return []
    }
  }

  /**
   * Analyze bucket list item for weather suitability
   */
  private analyzeItemForWeather(item: any, weather: WeatherData): ActivitySuggestion | null {
    try {
      const title = item.title.toLowerCase()
      const description = (item.description || '').toLowerCase()
      const category = item.category?.toLowerCase() || ''

      let weatherScore = 0
      let reason = ''
      let priority: 'high' | 'medium' | 'low' = 'low'
      let isOptimal = false

      // Outdoor activities
      if (this.isOutdoorActivity(title, description, category)) {
        if (weather.isGoodWeather && weather.temperature > 15 && weather.temperature < 30) {
          weatherScore = 90
          reason = `Perfect weather for outdoor activities! ${weather.temperature}°C with ${weather.condition.toLowerCase()}`
          priority = 'high'
          isOptimal = true
        } else if (weather.condition === 'Rain') {
          weatherScore = 20
          reason = `Not ideal for outdoor activities due to rain`
          priority = 'low'
        }
      }

      // Beach/water activities
      if (this.isBeachActivity(title, description, category)) {
        if (weather.temperature > 25 && weather.condition === 'Clear') {
          weatherScore = 95
          reason = `Perfect beach weather! ${weather.temperature}°C and sunny`
          priority = 'high'
          isOptimal = true
        } else if (weather.temperature < 20) {
          weatherScore = 30
          reason = `Too cold for beach activities (${weather.temperature}°C)`
          priority = 'low'
        }
      }

      // Winter activities
      if (this.isWinterActivity(title, description, category)) {
        if (weather.season === 'winter' && weather.temperature < 5) {
          weatherScore = 85
          reason = `Great winter weather for cold-weather activities!`
          priority = 'high'
          isOptimal = true
        } else if (weather.season !== 'winter') {
          weatherScore = 20
          reason = `Not the right season for winter activities`
          priority = 'low'
        }
      }

      // Indoor activities (good for bad weather)
      if (this.isIndoorActivity(title, description, category)) {
        if (!weather.isGoodWeather || weather.condition === 'Rain') {
          weatherScore = 70
          reason = `Perfect time for indoor activities due to ${weather.condition.toLowerCase()}`
          priority = 'medium'
          isOptimal = true
        }
      }

      // Photography activities
      if (this.isPhotographyActivity(title, description, category)) {
        if (weather.condition === 'Clear' || weather.condition === 'Clouds') {
          weatherScore = 80
          reason = `Great lighting conditions for photography`
          priority = 'medium'
          isOptimal = true
        }
      }

      // Only return suggestions with meaningful weather scores
      if (weatherScore < 50) {
        return null
      }

      return {
        itemId: item.id,
        title: item.title,
        reason,
        weatherScore,
        priority,
        suggestedTime: new Date(),
        weatherContext: {
          condition: weather.condition,
          temperature: weather.temperature,
          isOptimal,
          details: `${weather.temperature}°C, ${weather.description}, humidity ${weather.humidity}%`
        }
      }
    } catch (error) {
      Logger.error('Failed to analyze item for weather', error)
      return null
    }
  }

  /**
   * Activity type detection helpers
   */
  private isOutdoorActivity(title: string, description: string, category: string): boolean {
    const outdoorKeywords = [
      'hike', 'hiking', 'walk', 'run', 'bike', 'cycling', 'outdoor', 'park', 'garden',
      'picnic', 'camping', 'festival', 'market', 'sports', 'tennis', 'golf', 'climb'
    ]
    const text = `${title} ${description} ${category}`
    return outdoorKeywords.some(keyword => text.includes(keyword))
  }

  private isBeachActivity(title: string, description: string, category: string): boolean {
    const beachKeywords = [
      'beach', 'swim', 'swimming', 'surf', 'surfing', 'ocean', 'sea', 'sand',
      'sunbathe', 'tan', 'water sports', 'diving', 'snorkel'
    ]
    const text = `${title} ${description} ${category}`
    return beachKeywords.some(keyword => text.includes(keyword))
  }

  private isWinterActivity(title: string, description: string, category: string): boolean {
    const winterKeywords = [
      'ski', 'skiing', 'snowboard', 'snow', 'ice', 'skating', 'winter', 'cold',
      'sledding', 'snowman', 'christmas', 'holiday'
    ]
    const text = `${title} ${description} ${category}`
    return winterKeywords.some(keyword => text.includes(keyword))
  }

  private isIndoorActivity(title: string, description: string, category: string): boolean {
    const indoorKeywords = [
      'museum', 'gallery', 'movie', 'cinema', 'theater', 'restaurant', 'cafe',
      'shopping', 'mall', 'library', 'book', 'read', 'study', 'learn', 'course',
      'gym', 'workout', 'spa', 'massage'
    ]
    const text = `${title} ${description} ${category}`
    return indoorKeywords.some(keyword => text.includes(keyword))
  }

  private isPhotographyActivity(title: string, description: string, category: string): boolean {
    const photoKeywords = [
      'photo', 'photography', 'picture', 'shoot', 'camera', 'landscape',
      'portrait', 'sunset', 'sunrise', 'view', 'scenic'
    ]
    const text = `${title} ${description} ${category}`
    return photoKeywords.some(keyword => text.includes(keyword))
  }

  /**
   * Get current season based on date
   */
  private getCurrentSeason(): 'spring' | 'summer' | 'fall' | 'winter' {
    const month = new Date().getMonth() + 1 // 1-12
    
    if (month >= 3 && month <= 5) return 'spring'
    if (month >= 6 && month <= 8) return 'summer'
    if (month >= 9 && month <= 11) return 'fall'
    return 'winter'
  }

  /**
   * Check if service is available
   */
  isAvailable(): boolean {
    return this.isInitialized && !!this.apiKey
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      isAvailable: this.isAvailable(),
      configured: !!this.apiKey,
      baseUrl: this.baseUrl
    }
  }
}

// Export singleton instance
export const weatherService = new WeatherService()
