import { Server as SocketIOServer, Socket } from 'socket.io'
import { Server as HTTPServer } from 'http'
import jwt from 'jsonwebtoken'
import { Logger } from './monitoring'
import { prisma } from './prisma'

export interface AuthenticatedSocket extends Socket {
  userId?: string
  user?: {
    id: string
    email: string
    displayName: string
  }
}

export interface SocketEventData {
  bucketListItemCreated: {
    item: any
    userId: string
  }
  bucketListItemUpdated: {
    item: any
    userId: string
    changes: Record<string, any>
  }
  bucketListItemDeleted: {
    itemId: string
    userId: string
  }
  bucketListStatusChanged: {
    itemId: string
    oldStatus: string
    newStatus: string
    userId: string
  }
  sharedListUserJoined: {
    listId: string
    user: any
  }
  sharedListUserLeft: {
    listId: string
    userId: string
  }
  notificationNew: {
    notification: any
    userId: string
  }
  userPresence: {
    userId: string
    status: 'online' | 'offline' | 'away'
    lastSeen: Date
  }
  conflictDetected: {
    itemId: string
    conflictType: 'concurrent_edit' | 'version_mismatch'
    conflictData: any
    affectedUsers: string[]
  }
}

class SocketService {
  private io?: SocketIOServer
  private connectedUsers = new Map<string, Set<string>>() // userId -> Set of socketIds
  private userPresence = new Map<string, { status: string; lastSeen: Date }>()

  /**
   * Initialize Socket.io server
   */
  initialize(httpServer: HTTPServer): void {
    try {
      this.io = new SocketIOServer(httpServer, {
        cors: {
          origin: [
            process.env.WEB_APP_URL || 'http://localhost:3000',
            process.env.MOBILE_APP_URL || 'http://localhost:8081'
          ],
          methods: ['GET', 'POST'],
          credentials: true
        },
        transports: ['websocket', 'polling']
      })

      // Authentication middleware
      this.io.use(this.authenticateSocket.bind(this))

      // Connection handling
      this.io.on('connection', this.handleConnection.bind(this))

      Logger.info('Socket.io server initialized successfully')
    } catch (error) {
      Logger.error('Failed to initialize Socket.io server', error)
    }
  }

  /**
   * Authenticate socket connection using JWT
   */
  private async authenticateSocket(socket: AuthenticatedSocket, next: (err?: Error) => void): Promise<void> {
    try {
      // For development, skip authentication
      if (process.env.AUTH_DISABLED === 'true' || process.env.NODE_ENV === 'development') {
        console.log('🔓 Socket auth disabled for development')
        socket.userId = 'dev-user-123'
        socket.user = {
          id: 'dev-user-123',
          email: '<EMAIL>',
          displayName: 'Dev User'
        }
        return next()
      }

      const token = socket.handshake.auth?.token || socket.handshake.headers?.authorization?.replace('Bearer ', '')

      if (!token) {
        return next(new Error('Authentication token required'))
      }

      // For now, skip JWT verification and use mock user
      // TODO: Implement proper Auth0 JWT verification for sockets
      socket.userId = 'dev-user-123'
      socket.user = {
        id: 'dev-user-123',
        email: '<EMAIL>',
        displayName: 'Dev User'
      }

      return next()

      // Attach user info to socket
      socket.userId = user.id
      socket.user = {
        id: user.id,
        email: user.email,
        displayName: user.displayName || user.email
      }

      next()
    } catch (error) {
      Logger.error('Socket authentication failed', error)
      next(new Error('Authentication failed'))
    }
  }

  /**
   * Handle new socket connection
   */
  private handleConnection(socket: AuthenticatedSocket): void {
    try {
      const userId = socket.userId!
      const user = socket.user!

      Logger.info('User connected via socket', {
        userId,
        socketId: socket.id,
        email: user.email
      })

      // Track connected user
      if (!this.connectedUsers.has(userId)) {
        this.connectedUsers.set(userId, new Set())
      }
      this.connectedUsers.get(userId)!.add(socket.id)

      // Update user presence
      this.updateUserPresence(userId, 'online')

      // Join user-specific room for personal notifications
      socket.join(`user:${userId}`)

      // Join shared list rooms
      this.joinUserSharedListRooms(socket, userId)

      // Set up event handlers
      this.setupEventHandlers(socket)

      // Handle disconnection
      socket.on('disconnect', () => this.handleDisconnection(socket))

      // Send initial presence update
      this.broadcastUserPresence(userId, 'online')

    } catch (error) {
      Logger.error('Failed to handle socket connection', error)
      socket.disconnect()
    }
  }

  /**
   * Handle socket disconnection
   */
  private handleDisconnection(socket: AuthenticatedSocket): void {
    try {
      const userId = socket.userId!

      Logger.info('User disconnected from socket', {
        userId,
        socketId: socket.id
      })

      // Remove socket from user's connections
      const userSockets = this.connectedUsers.get(userId)
      if (userSockets) {
        userSockets.delete(socket.id)
        
        // If no more connections, mark user as offline
        if (userSockets.size === 0) {
          this.connectedUsers.delete(userId)
          this.updateUserPresence(userId, 'offline')
          this.broadcastUserPresence(userId, 'offline')
        }
      }

    } catch (error) {
      Logger.error('Failed to handle socket disconnection', error)
    }
  }

  /**
   * Join user to their shared list rooms
   */
  private async joinUserSharedListRooms(socket: AuthenticatedSocket, userId: string): Promise<void> {
    try {
      // Get user's shared lists
      const sharedLists = await prisma.sharedList.findMany({
        where: {
          OR: [
            { createdBy: userId },
            { members: { some: { userId } } }
          ]
        },
        select: { id: true }
      })

      // Join each shared list room
      for (const list of sharedLists) {
        socket.join(`shared-list:${list.id}`)
      }

      Logger.debug('User joined shared list rooms', {
        userId,
        sharedListCount: sharedLists.length
      })

    } catch (error) {
      Logger.error('Failed to join user shared list rooms', error)
    }
  }

  /**
   * Set up event handlers for socket
   */
  private setupEventHandlers(socket: AuthenticatedSocket): void {
    // Bucket list item events
    socket.on('bucket-list:subscribe', (data) => {
      // Subscribe to specific bucket list updates
      if (data.listId) {
        socket.join(`bucket-list:${data.listId}`)
      }
    })

    socket.on('bucket-list:unsubscribe', (data) => {
      // Unsubscribe from bucket list updates
      if (data.listId) {
        socket.leave(`bucket-list:${data.listId}`)
      }
    })

    // Presence events
    socket.on('user:presence', (data) => {
      const userId = socket.userId!
      const status = data.status || 'online'
      this.updateUserPresence(userId, status)
      this.broadcastUserPresence(userId, status)
    })

    // Typing indicators for shared lists
    socket.on('shared-list:typing', (data) => {
      if (data.listId) {
        socket.to(`shared-list:${data.listId}`).emit('shared-list:user-typing', {
          userId: socket.userId,
          user: socket.user,
          isTyping: data.isTyping
        })
      }
    })
  }

  /**
   * Update user presence status
   */
  private updateUserPresence(userId: string, status: string): void {
    this.userPresence.set(userId, {
      status,
      lastSeen: new Date()
    })
  }

  /**
   * Broadcast user presence to relevant rooms
   */
  private broadcastUserPresence(userId: string, status: string): void {
    const presenceData = {
      userId,
      status,
      lastSeen: new Date()
    }

    // Broadcast to user's shared lists
    this.io?.to(`user:${userId}`).emit('user:presence', presenceData)
  }

  /**
   * Emit event to specific user
   */
  emitToUser<K extends keyof SocketEventData>(
    userId: string,
    event: K,
    data: SocketEventData[K]
  ): void {
    if (this.io) {
      this.io.to(`user:${userId}`).emit(event, data)
    }
  }

  /**
   * Emit event to shared list room
   */
  emitToSharedList<K extends keyof SocketEventData>(
    listId: string,
    event: K,
    data: SocketEventData[K]
  ): void {
    if (this.io) {
      this.io.to(`shared-list:${listId}`).emit(event, data)
    }
  }

  /**
   * Emit event to all connected clients
   */
  emitToAll<K extends keyof SocketEventData>(
    event: K,
    data: SocketEventData[K]
  ): void {
    if (this.io) {
      this.io.emit(event, data)
    }
  }

  /**
   * Get connected users count
   */
  getConnectedUsersCount(): number {
    return this.connectedUsers.size
  }

  /**
   * Get user presence status
   */
  getUserPresence(userId: string): { status: string; lastSeen: Date } | null {
    return this.userPresence.get(userId) || null
  }

  /**
   * Check if user is online
   */
  isUserOnline(userId: string): boolean {
    return this.connectedUsers.has(userId)
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      isInitialized: !!this.io,
      connectedUsers: this.getConnectedUsersCount(),
      totalConnections: Array.from(this.connectedUsers.values())
        .reduce((total, sockets) => total + sockets.size, 0)
    }
  }
}

// Export singleton instance
export const socketService = new SocketService()
