# DreamVault Project Structure

## Monorepo Organization

DreamVault uses a monorepo structure with clear separation of concerns across different application layers and shared resources.

```
DreamVault/
├── backend/                 # Express.js API server
├── web/                    # Next.js web application  
├── mobile/                 # React Native + Expo mobile app
├── packages/
│   └── types/              # Shared TypeScript type definitions
└── [config files]          # Root-level configuration
```

## Backend Structure (`/backend`)

```
backend/
├── src/
│   ├── routes/             # API route handlers (RESTful endpoints)
│   ├── lib/                # Core services and utilities
│   │   ├── auth.ts         # Auth0 integration
│   │   ├── prisma.ts       # Database client
│   │   ├── s3.ts           # AWS S3 file storage
│   │   ├── ai-service.ts   # OpenAI/AI integrations
│   │   └── [other services]
│   ├── middleware/         # Express middleware (auth, validation, etc.)
│   ├── tests/              # Jest test files
│   ├── prisma/             # Database seeds and utilities
│   └── index.ts            # Application entry point
├── prisma/
│   ├── schema.prisma       # Database schema definition
│   └── migrations/         # Database migration files
├── scripts/                # Utility scripts for testing/setup
└── [config files]          # TypeScript, Jest, ESLint configs
```

## Web Application Structure (`/web`)

```
web/
├── src/
│   ├── app/                # Next.js App Router pages
│   │   ├── (routes)/       # Route groups and pages
│   │   ├── api/            # API route handlers
│   │   ├── globals.css     # Global styles
│   │   └── layout.tsx      # Root layout component
│   ├── components/         # React components
│   │   ├── ui/             # Radix UI-based components
│   │   ├── layout/         # Layout-specific components
│   │   └── [feature components]
│   ├── lib/                # Utilities and hooks
│   │   ├── api.ts          # API client configuration
│   │   ├── store.ts        # Zustand state management
│   │   └── utils.ts        # Helper functions
│   └── types/              # Web-specific type definitions
├── public/                 # Static assets
└── [config files]          # Next.js, Tailwind, TypeScript configs
```

## Mobile Application Structure (`/mobile`)

```
mobile/
├── src/
│   ├── screens/            # Screen components
│   │   ├── auth/           # Authentication screens
│   │   └── [feature screens]
│   ├── navigation/         # React Navigation setup
│   ├── components/         # Reusable React Native components
│   ├── contexts/           # React contexts (AuthContext, etc.)
│   ├── hooks/              # Custom React hooks
│   ├── utils/              # Utilities and services
│   │   ├── api.ts          # API client
│   │   ├── storage.ts      # AsyncStorage utilities
│   │   └── camera.ts       # Camera/media utilities
│   ├── config/             # Configuration files
│   └── types/              # Mobile-specific types
├── assets/                 # Images, icons, splash screens
└── [config files]          # Expo, React Native, TypeScript configs
```

## Shared Types Structure (`/packages/types`)

```
packages/types/
├── src/
│   ├── index.ts            # Main export file
│   ├── bucketList.ts       # Bucket list related types
│   ├── user.ts             # User and profile types
│   ├── api.ts              # API request/response types
│   └── social.ts           # Social features types
├── dist/                   # Compiled TypeScript output
└── [config files]          # TypeScript configuration
```

## Key Architectural Patterns

### Database Layer
- **Prisma ORM**: Single source of truth for database schema
- **Migration-based**: Version-controlled schema changes
- **Enum definitions**: Consistent enums across database and TypeScript

### API Layer
- **RESTful design**: Standard HTTP methods and status codes
- **Route organization**: Grouped by feature/resource
- **Middleware pattern**: Authentication, validation, error handling
- **Service layer**: Business logic separated from route handlers

### Frontend Architecture
- **Component-based**: Reusable UI components with consistent patterns
- **State management**: Zustand for client-side state
- **Type safety**: Shared types ensure consistency across platforms
- **Theme system**: Consistent theming across web and mobile

### File Naming Conventions
- **kebab-case**: For file and directory names
- **PascalCase**: For React components and TypeScript interfaces
- **camelCase**: For functions, variables, and object properties
- **SCREAMING_SNAKE_CASE**: For constants and environment variables

### Import/Export Patterns
- **Barrel exports**: Index files re-export from subdirectories
- **Relative imports**: Use relative paths within same package
- **Absolute imports**: Use package names for cross-package imports
- **Type-only imports**: Use `import type` for TypeScript types

### Testing Organization
- **Co-location**: Test files alongside source files where possible
- **Feature grouping**: Tests organized by feature/module
- **Integration tests**: Separate directory for cross-module tests
- **Mocking**: Consistent mocking patterns for external services