# AI Agent Development Guidelines

Guidelines for AI agents working on the DreamVault bucket list application. These control agent behavior across all development tasks and ensure consistent, high-quality implementation.

## 🔧 MCP Tools - MANDATORY USAGE

### Tool Priorities (Always Use When Available)
- **Database MCP tools** - ALWAYS use for database operations, migrations, SQL queries
- **UI Component MCP tools** - ALWAYS use for component scaffolding and management
- **Playwright MCP tools** - ALWAYS use for web automation, testing, and file uploads
- **GitHub MCP tools** - Use for repository operations, PR management, and code reviews
- **Sentry MCP tools** - Use for error tracking, debugging, and performance monitoring
- **Context MCP tools** - Use for enhanced context management and awareness
- **Sequential Thinking MCP tools** - Use for complex problem-solving workflows
- **Modern UI/UX Design MCP tools** - Use for design system implementation and optimization
- **File Management MCP tools** - Use for efficient file operations
- **Testing MCP tools** - Use for automated testing and validation

### MCP Usage Rules
- **NEVER use manual CLI commands** when MCP equivalent exists
- **Database operations** - Use MCP tools instead of manual SQL/Prisma commands
- **UI components** - Use MCP tools for component generation and updates
- **Testing & automation** - Use Playwright MCP for web testing and file uploads
- **Error tracking** - Use Sentry MCP for debugging and monitoring
- **Repository management** - Use GitHub MCP for code operations
- **Always check MCP diagnostics** before completing tasks
- **Leverage parallel tool execution** for efficiency

## 🎯 Core Development Requirements

### Code Quality Standards
- **ALWAYS run linting** (`npm run lint`) and fix all TypeScript errors before completing tasks
- **ALWAYS use TypeScript strict mode** - no `any` types allowed, prefer proper typing
- **ALWAYS use established UI patterns** - Radix UI primitives with Tailwind CSS
- **ALWAYS implement responsive design** for all components (mobile-first approach)
- **ALWAYS follow accessibility guidelines** - proper ARIA labels, keyboard navigation

### Database Operations (Prisma + PostgreSQL)
- **ALWAYS use Prisma ORM** for database operations and type safety
- **ALWAYS check existing schema** before making changes via `prisma/schema.prisma`
- **ALWAYS use migrations** for schema changes (`npm run db:migrate`)
- **ALWAYS implement proper relationships** between entities (User, BucketList, Goal, etc.)
- **ALWAYS add database indexes** for performance-critical queries

### Component Architecture
- **ALWAYS use established patterns** - check existing components first
- **ALWAYS use Zustand** for state management (avoid prop drilling)
- **ALWAYS use React Hook Form + Zod** for form validation
- **ALWAYS implement proper loading states** and error boundaries
- **ALWAYS use Tailwind CSS** with consistent design tokens

## 🚀 Task Completion Checklist

### Before Completing Any Task
1. **Run diagnostics** - Check for TypeScript and linting errors
2. **Test functionality** - Ensure features work across all platforms (web/mobile)
3. **Check responsive design** - Test on mobile, tablet, and desktop viewports
4. **Verify database operations** - Ensure data integrity and proper relationships
5. **Test authentication flows** - Verify Auth0 integration works correctly

### Development Workflow
- **ALWAYS check existing code patterns** before implementing new features
- **ALWAYS use the established service layer** (`backend/src/lib/`) for business logic
- **ALWAYS implement proper error handling** with user-friendly messages
- **ALWAYS add loading states** for async operations
- **NEVER commit changes** unless explicitly requested by user
- **DO EXACTLY what the user asks** - nothing more, nothing less (VERY IMPORTANT)

### Development Server Management
- **Use root-level scripts** for all server operations:
  - `npm run dev` or `npm run dev:start` - Start all development servers
  - `npm run dev:stop` - Stop all development servers
  - `npm run dev:restart` - Restart all development servers
  - `npm run dev:status` - Check status of all servers
  - `npm run dev:logs:backend` - View backend logs
  - `npm run dev:logs:web` - View web application logs
  - `npm run dev:logs:mobile` - View mobile application logs
  - `npm run health` - Run health checks across all services
- **NEVER manually start individual servers** - always use the coordinated scripts

## 📋 DreamVault-Specific Guidelines

### Bucket List Features
- **Focus on goal achievement** - Playing, In Progress, Completed, Paused statuses
- **Implement proper categorization** - Travel, Career, Personal, Relationships, Adventures, Learning
- **Use AI-powered suggestions** - OpenAI/Google AI for goal recommendations
- **Prioritize user experience** - Smooth animations, intuitive navigation
- **Support multimedia content** - Images, notes, progress photos via AWS S3

### Multi-Platform Consistency
- **Shared type definitions** - Use `@dreamvault/types` package across all platforms
- **Consistent API contracts** - RESTful design with proper status codes
- **Theme synchronization** - Dark/light mode across web and mobile
- **Offline support** - Implement proper caching strategies for mobile

### AI Integration
- **Multi-provider support** - OpenAI, Google Generative AI with graceful fallbacks
- **Context-aware recommendations** - Base suggestions on user's existing goals and progress
- **Goal-focused prompts** - Tailor AI responses for personal development and achievement
- **Privacy-first approach** - Never store sensitive personal data in AI contexts

## 🔒 Security & Privacy Requirements

### Authentication & Authorization
- **Use Auth0 exclusively** - Never implement custom auth
- **Implement JWT validation** - Verify tokens on all protected routes
- **Apply proper RBAC** - Role-based access control for different user types
- **Session management** - Proper token refresh and logout flows

### Data Protection
- **Encrypt sensitive data** - Use proper encryption for personal goals and notes
- **Implement comprehensive validation** - Server-side validation for all inputs
- **Never expose internal details** - Sanitize error messages for users
- **Follow GDPR principles** - Data minimization and user consent

## 🎨 UI/UX Standards

### Modern Design System & Tools
- **Use Modern UI/UX Design MCP tools** - ALWAYS leverage MCP tools for design system implementation
- **Use Tailwind CSS** with consistent spacing, colors, and typography
- **Implement glassmorphism effects** - Subtle transparency and blur effects
- **Smooth animations** - Use Framer Motion for micro-interactions
- **Consistent iconography** - Lucide React icons throughout the application
- **Proper loading states** - Skeleton screens and progress indicators
- **Modern design patterns** - Use latest UI/UX methodologies via MCP tools

### Accessibility
- **WCAG 2.1 AA compliance** - Proper contrast ratios and screen reader support
- **Keyboard navigation** - All interactive elements accessible via keyboard
- **Focus management** - Proper focus indicators and logical tab order
- **Alternative text** - Descriptive alt text for all images and media

## 📱 Platform-Specific Guidelines

### Web Application (Next.js)
- **Use App Router** - Leverage Next.js 14 App Router for routing
- **Implement SSR/SSG** - Server-side rendering for SEO and performance
- **Optimize Core Web Vitals** - Focus on LCP, FID, and CLS metrics
- **Progressive enhancement** - Ensure functionality without JavaScript

### Mobile Application (React Native + Expo)
- **Native feel** - Use platform-specific UI patterns and navigation
- **Offline-first** - Implement proper caching and sync strategies
- **Camera integration** - Seamless photo capture for goal progress
- **Push notifications** - Achievement reminders and social updates

### Backend API (Express.js)
- **RESTful design** - Consistent HTTP methods and status codes
- **Proper middleware** - Authentication, validation, error handling
- **Rate limiting** - Protect against abuse and ensure fair usage
- **Comprehensive logging** - Structured logging with Sentry integration

## 🚀 Performance Optimization

### Database Performance
- **Optimize queries** - Use proper indexing and avoid N+1 problems
- **Implement caching** - Redis for frequently accessed data
- **Connection pooling** - Efficient database connection management
- **Query analysis** - Regular performance monitoring and optimization

### Frontend Performance
- **Code splitting** - Lazy load components and routes
- **Image optimization** - Use Next.js Image component and Sharp processing
- **Bundle analysis** - Regular bundle size monitoring and optimization
- **Caching strategies** - Implement proper HTTP caching headers

## 🧪 Testing & Debugging Requirements

### Test Coverage
- **Use Playwright MCP tools** - ALWAYS use for web automation, testing, and file uploads
- **Unit tests** - Jest for business logic and utilities
- **Integration tests** - API endpoint testing with proper mocking
- **E2E tests** - Critical user flows across platforms using Playwright MCP
- **Accessibility tests** - Automated a11y testing in CI/CD

### Testing Patterns
- **Playwright automation** - Use Playwright MCP for file uploads, form testing, UI automation
- **Mock external services** - Auth0, AWS S3, AI APIs
- **Test error scenarios** - Network failures, invalid inputs
- **Performance testing** - Load testing for critical endpoints
- **Cross-platform testing** - Ensure consistency across web and mobile

### Debugging & Monitoring
- **Use Sentry MCP tools** - ALWAYS use for error tracking and debugging
- **Monitor performance** - Use Sentry MCP for performance insights
- **Track user issues** - Implement proper error boundaries with Sentry integration
- **Debug production issues** - Use Sentry MCP for real-time debugging

## 🎯 Core Principle

### User-Centric Development
- **DO EXACTLY what the user asks** - This is VERY IMPORTANT
- **Nothing more, nothing less** - Avoid over-engineering or adding unrequested features
- **Focus on the specific request** - Don't assume additional requirements
- **Ask for clarification** if the request is unclear
- **Deliver precisely what was requested** - No extra features or modifications

## 🔗 Repository & Version Control

### GitHub Integration
- **Use GitHub MCP tools** - ALWAYS use for repository operations
- **Code reviews** - Use GitHub MCP for PR management and reviews
- **Issue tracking** - Link commits to issues via GitHub MCP
- **Branch management** - Use GitHub MCP for branch operations
- **Release management** - Coordinate releases via GitHub MCP tools

---

*These guidelines ensure consistent, high-quality development of DreamVault features while maintaining security, performance, and user experience standards across all platforms. Always prioritize doing exactly what the user requests - nothing more, nothing less.*