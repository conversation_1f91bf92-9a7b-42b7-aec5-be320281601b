# DreamVault Technology Stack

## Architecture Overview

DreamVault follows a modern full-stack architecture with a monorepo structure containing backend API, web application, mobile app, and shared type definitions.

## Backend Stack

- **Runtime**: Node.js 18+
- **Framework**: Express.js with TypeScript
- **Database**: PostgreSQL 14+ with Prisma ORM
- **Authentication**: Auth0 with JWT tokens
- **File Storage**: AWS S3 with Sharp for image processing
- **Real-time**: Socket.io for live updates
- **Caching**: Redis (optional)
- **AI Services**: OpenAI API, Google Generative AI
- **Monitoring**: Sentry for error tracking

## Frontend Stack

### Web Application
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript with strict mode
- **Styling**: Tailwind CSS with dark/light theme support
- **UI Components**: Radix UI primitives
- **State Management**: Zustand
- **Forms**: React Hook Form
- **HTTP Client**: Axios

### Mobile Application
- **Framework**: React Native with Expo
- **Navigation**: React Navigation v6
- **Authentication**: Auth0 React Native
- **State Management**: Zustand
- **Camera/Media**: Expo Camera, Expo Image Picker
- **Storage**: Expo Secure Store, AsyncStorage

## Shared Infrastructure

- **Types**: Shared TypeScript definitions in `@dreamvault/types`
- **Linting**: ESLint with TypeScript rules
- **Testing**: Jest for unit/integration tests
- **Build**: TypeScript compiler, Next.js build system

## Common Development Commands

### Backend Development
```bash
cd backend
npm run dev          # Start development server with hot reload
npm run build        # Build for production
npm test             # Run Jest tests
npm run db:push      # Push Prisma schema changes
npm run db:migrate   # Run database migrations
npm run db:seed      # Seed database with sample data
npm run db:studio    # Open Prisma Studio
```

### Web Development
```bash
cd web
npm run dev          # Start Next.js dev server (port 3000)
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking
```

### Mobile Development
```bash
cd mobile
npm run start        # Start Expo development server
npm run android      # Run on Android
npm run ios          # Run on iOS
npm run test         # Run Jest tests
npm run lint         # Run ESLint
```

### Shared Types
```bash
cd packages/types
npm run build        # Build TypeScript definitions
npm run dev          # Watch mode for development
```

## Database Management

- **ORM**: Prisma with PostgreSQL
- **Migrations**: Version-controlled schema changes
- **Seeding**: Automated sample data generation
- **Studio**: Visual database browser via Prisma Studio

## Environment Configuration

### Required Environment Variables
- `DATABASE_URL`: PostgreSQL connection string
- `AUTH0_DOMAIN`, `AUTH0_CLIENT_ID`, `AUTH0_CLIENT_SECRET`: Auth0 configuration
- `JWT_SECRET`: JWT signing secret
- `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`, `AWS_S3_BUCKET`: S3 storage
- `OPENAI_API_KEY`: AI features

## Development Workflow

1. Install dependencies in all packages
2. Set up environment variables from `.env.example` files
3. Initialize database with `npm run db:push && npm run db:seed`
4. Start backend API server (port 3001)
5. Start web application (port 3000) or mobile app
6. Use Prisma Studio for database inspection