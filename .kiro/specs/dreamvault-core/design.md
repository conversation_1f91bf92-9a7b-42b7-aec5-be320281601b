# Design Document

## Overview

DreamVault is a cross-platform bucket list application built with a modern tech stack to provide seamless experiences across mobile and web platforms. The system follows a microservices-inspired architecture with a shared API backend serving both React Native mobile apps and a Next.js web application.

### Core Architecture Principles

- **Cross-platform consistency**: Shared TypeScript types and API contracts ensure consistent behavior
- **Real-time synchronization**: All user data syncs instantly across devices
- **Offline-first mobile experience**: Mobile apps work without connectivity and sync when online
- **Scalable backend**: RESTful API with real-time capabilities using Socket.io
- **Security-first**: End-to-end encryption for sensitive data with secure authentication

### Technology Stack

**Backend:**
- Node.js with Express.js for API server
- PostgreSQL with Prisma ORM for data persistence
- Auth0 for authentication and user management
- AWS S3 for media file storage
- Redis for caching and session management
- Socket.io for real-time features

**Frontend:**
- React Native (iOS/Android) with TypeScript
- Next.js 14 with App Router (Web) and TypeScript
- Shared TypeScript types package for consistency
- React Query for state management and caching

**External Services:**
- OpenAI API for AI-powered features
- Google Calendar/Apple Calendar APIs for integration
- Weather API for contextual suggestions
- Push notification services (FCM/APNS)

## Architecture

### System Architecture Diagram

```mermaid
graph TB
    subgraph "Client Applications"
        RN[React Native App<br/>iOS/Android]
        WEB[Next.js Web App<br/>PWA]
    end
    
    subgraph "API Gateway & Load Balancer"
        LB[Load Balancer]
    end
    
    subgraph "Backend Services"
        API[Express.js API Server]
        SOCKET[Socket.io Server]
        AUTH[Auth0 Service]
    end
    
    subgraph "Data Layer"
        PG[(PostgreSQL<br/>Primary Database)]
        REDIS[(Redis<br/>Cache & Sessions)]
        S3[AWS S3<br/>Media Storage]
    end
    
    subgraph "External Services"
        OPENAI[OpenAI API]
        CAL[Calendar APIs]
        WEATHER[Weather API]
        PUSH[Push Notifications]
    end
    
    RN --> LB
    WEB --> LB
    LB --> API
    LB --> SOCKET
    API --> AUTH
    API --> PG
    API --> REDIS
    API --> S3
    API --> OPENAI
    API --> CAL
    API --> WEATHER
    API --> PUSH
    SOCKET --> REDIS
    SOCKET --> PG
```

### Data Flow Architecture

The application follows a unidirectional data flow pattern:

1. **Client Request**: Mobile/Web clients make authenticated requests to the API
2. **Authentication**: Auth0 validates JWT tokens and user permissions
3. **Business Logic**: API server processes requests with business rules
4. **Data Persistence**: Prisma ORM handles database operations with PostgreSQL
5. **Real-time Updates**: Socket.io broadcasts changes to connected clients
6. **Caching**: Redis caches frequently accessed data for performance

## Components and Interfaces

### Core Domain Models

#### User Model
```typescript
interface User {
  id: string
  email: string
  profile: UserProfile
  preferences: UserPreferences
  createdAt: Date
  updatedAt: Date
}

interface UserProfile {
  displayName: string
  avatar?: string
  bio?: string
  location?: string
  interests: string[]
  isPublic: boolean
}

interface UserPreferences {
  notifications: NotificationSettings
  privacy: PrivacySettings
  theme: 'light' | 'dark' | 'system'
}
```

#### Bucket List Item Model
```typescript
interface BucketListItem {
  id: string
  userId: string
  title: string
  description?: string
  category: ItemCategory
  priority: ItemPriority
  status: ItemStatus
  targetDate?: Date
  estimatedCost?: number
  location?: Location
  tags: string[]
  media: MediaFile[]
  progress: ProgressTracking
  createdAt: Date
  updatedAt: Date
}

type ItemCategory = 'Travel' | 'Career' | 'Personal Growth' | 'Relationships' | 'Adventures' | 'Learning'
type ItemPriority = 'Must-do' | 'Want-to-do' | 'Someday'
type ItemStatus = 'Not Started' | 'In Progress' | 'Completed' | 'On Hold'
```

#### Progress Tracking Model
```typescript
interface ProgressTracking {
  id: string
  itemId: string
  completionPercentage: number
  milestones: Milestone[]
  timeline: TimelineEntry[]
  achievements: Achievement[]
}

interface Milestone {
  id: string
  title: string
  description?: string
  completedAt?: Date
  media?: MediaFile[]
}

interface TimelineEntry {
  id: string
  date: Date
  description: string
  media?: MediaFile[]
  type: 'progress' | 'milestone' | 'completion'
}
```

### API Interface Design

#### RESTful API Endpoints

**Authentication Endpoints:**
```
POST /api/auth/register
POST /api/auth/login
POST /api/auth/refresh
POST /api/auth/logout
POST /api/auth/forgot-password
POST /api/auth/reset-password
```

**User Management:**
```
GET /api/users/profile
PUT /api/users/profile
DELETE /api/users/account
GET /api/users/preferences
PUT /api/users/preferences
```

**Bucket List Items:**
```
GET /api/items                    # List with filtering/sorting
POST /api/items                   # Create new item
GET /api/items/:id               # Get specific item
PUT /api/items/:id               # Update item
DELETE /api/items/:id            # Delete item
POST /api/items/:id/media        # Upload media
DELETE /api/items/:id/media/:mediaId  # Delete media
```

**Progress Tracking:**
```
GET /api/items/:id/progress      # Get progress data
PUT /api/items/:id/progress      # Update progress
POST /api/items/:id/milestones   # Add milestone
PUT /api/items/:id/milestones/:milestoneId  # Update milestone
```

**Search and Organization:**
```
GET /api/search                  # Advanced search with filters
GET /api/categories/suggestions  # AI-powered category suggestions
GET /api/analytics/dashboard     # Personal analytics
```

**Social Features:**
```
GET /api/shared-lists           # User's shared lists
POST /api/shared-lists          # Create shared list
GET /api/shared-lists/:id       # Get shared list details
POST /api/shared-lists/:id/invite  # Invite collaborators
GET /api/discovery              # Public discovery feed
POST /api/achievements/share    # Share achievements
```

#### Real-time Socket.io Events

**Connection Management:**
```typescript
// Client to Server
'join-room': { userId: string }
'leave-room': { userId: string }

// Server to Client
'connected': { userId: string }
'disconnected': { userId: string }
```

**Real-time Updates:**
```typescript
// Server to Client
'item-updated': { itemId: string, item: BucketListItem }
'progress-updated': { itemId: string, progress: ProgressTracking }
'achievement-unlocked': { userId: string, achievement: Achievement }
'shared-list-updated': { listId: string, changes: any }
```

### Component Architecture

#### Mobile App (React Native) Structure
```
src/
├── components/           # Reusable UI components
│   ├── common/          # Generic components
│   ├── forms/           # Form components
│   └── media/           # Media handling components
├── screens/             # Screen components
│   ├── auth/            # Authentication screens
│   ├── bucketlist/      # Bucket list management
│   ├── progress/        # Progress tracking
│   └── social/          # Social features
├── navigation/          # Navigation configuration
├── services/            # API and external services
├── hooks/               # Custom React hooks
├── utils/               # Utility functions
└── types/               # TypeScript type definitions
```

#### Web App (Next.js) Structure
```
src/
├── app/                 # App Router pages
│   ├── (auth)/         # Authentication pages
│   ├── dashboard/      # Main dashboard
│   ├── items/          # Item management
│   └── analytics/      # Analytics pages
├── components/         # React components
├── lib/                # Utility libraries
├── hooks/              # Custom hooks
└── types/              # Shared types
```

## Data Models

### Database Schema Design

#### Core Tables

**Users Table:**
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  auth0_id VARCHAR(255) UNIQUE NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  display_name VARCHAR(255),
  avatar_url TEXT,
  bio TEXT,
  location VARCHAR(255),
  interests JSONB DEFAULT '[]',
  is_public BOOLEAN DEFAULT false,
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

**Bucket List Items Table:**
```sql
CREATE TABLE bucket_list_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR(500) NOT NULL,
  description TEXT,
  category VARCHAR(50) NOT NULL,
  priority VARCHAR(20) NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'Not Started',
  target_date DATE,
  estimated_cost DECIMAL(10,2),
  location JSONB,
  tags JSONB DEFAULT '[]',
  completion_percentage INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

**Media Files Table:**
```sql
CREATE TABLE media_files (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  item_id UUID REFERENCES bucket_list_items(id) ON DELETE CASCADE,
  file_url TEXT NOT NULL,
  file_type VARCHAR(50) NOT NULL,
  file_size INTEGER,
  caption TEXT,
  uploaded_at TIMESTAMP DEFAULT NOW()
);
```

**Progress Tracking Table:**
```sql
CREATE TABLE progress_entries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  item_id UUID REFERENCES bucket_list_items(id) ON DELETE CASCADE,
  entry_date DATE NOT NULL,
  description TEXT,
  entry_type VARCHAR(20) NOT NULL,
  media_files JSONB DEFAULT '[]',
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### Relationships and Constraints

- **One-to-Many**: User → Bucket List Items
- **One-to-Many**: Bucket List Item → Media Files
- **One-to-Many**: Bucket List Item → Progress Entries
- **Many-to-Many**: Users ↔ Shared Lists (through junction table)

### Data Validation Rules

**Item Validation:**
- Title: Required, 1-500 characters
- Category: Must be one of predefined categories
- Priority: Must be 'Must-do', 'Want-to-do', or 'Someday'
- Status: Must be valid status enum
- Target Date: Must be future date if provided
- Estimated Cost: Must be positive number if provided

**Media Validation:**
- File Size: Maximum 10MB for images, 50MB for videos
- File Types: jpg, png, gif, mp4, mov, voice notes
- Virus Scanning: All uploads scanned before storage

## Error Handling

### Error Response Format

All API errors follow a consistent format:

```typescript
interface APIError {
  error: {
    code: string
    message: string
    details?: any
    timestamp: string
    requestId: string
  }
}
```

### Error Categories

**Authentication Errors (401):**
- `AUTH_TOKEN_MISSING`: No authentication token provided
- `AUTH_TOKEN_INVALID`: Invalid or expired token
- `AUTH_TOKEN_EXPIRED`: Token has expired

**Authorization Errors (403):**
- `INSUFFICIENT_PERMISSIONS`: User lacks required permissions
- `RESOURCE_ACCESS_DENIED`: Cannot access requested resource

**Validation Errors (400):**
- `VALIDATION_FAILED`: Request data validation failed
- `INVALID_FILE_TYPE`: Unsupported file type uploaded
- `FILE_TOO_LARGE`: File exceeds size limits

**Resource Errors (404):**
- `ITEM_NOT_FOUND`: Bucket list item not found
- `USER_NOT_FOUND`: User account not found

**Server Errors (500):**
- `DATABASE_ERROR`: Database operation failed
- `EXTERNAL_SERVICE_ERROR`: Third-party service unavailable
- `INTERNAL_SERVER_ERROR`: Unexpected server error

### Error Handling Strategy

**Client-Side Error Handling:**
- Automatic retry for network failures
- User-friendly error messages
- Offline mode fallback for mobile
- Error boundary components for React crashes

**Server-Side Error Handling:**
- Structured logging with request correlation IDs
- Graceful degradation for external service failures
- Database transaction rollbacks on errors
- Rate limiting to prevent abuse

## Testing Strategy

### Testing Pyramid

#### Unit Tests (70%)
**Backend API Tests:**
- Service layer business logic
- Data validation functions
- Utility functions
- Database query functions

**Frontend Component Tests:**
- React component rendering
- User interaction handling
- State management logic
- Custom hooks functionality

#### Integration Tests (20%)
**API Integration Tests:**
- End-to-end API workflows
- Database integration
- External service integration
- Authentication flows

**Cross-platform Tests:**
- Shared type consistency
- API contract validation
- Real-time synchronization

#### End-to-End Tests (10%)
**Critical User Flows:**
- User registration and onboarding
- Creating and managing bucket list items
- Progress tracking and achievements
- Cross-device synchronization
- Social sharing features

### Testing Tools and Framework

**Backend Testing:**
- Jest for unit and integration tests
- Supertest for API endpoint testing
- Prisma test database for isolated testing
- Mock external services (Auth0, OpenAI, etc.)

**Frontend Testing:**
- Jest and React Testing Library for React components
- Detox for React Native E2E testing
- Playwright for web application E2E testing
- Storybook for component documentation and testing

**Performance Testing:**
- Artillery.js for API load testing
- Lighthouse for web performance auditing
- React Native performance monitoring

### Continuous Integration

**Automated Testing Pipeline:**
1. Code quality checks (ESLint, Prettier, TypeScript)
2. Unit test execution with coverage reporting
3. Integration test execution
4. Security vulnerability scanning
5. Performance regression testing
6. Cross-platform build verification

**Quality Gates:**
- Minimum 80% code coverage
- All tests must pass
- No high-severity security vulnerabilities
- Performance budgets must be met
- Type checking must pass without errors

This comprehensive design provides a solid foundation for implementing the DreamVault bucket list application with all the features specified in the requirements document.