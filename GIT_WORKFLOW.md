# Git Workflow for DreamVault

**This document provides the standardized git workflow that <PERSON> should follow when committing changes to the DreamVault repository.**

---

## 🔄 Pre-Commit Workflow

### Step 1: Run Analysis Commands in Parallel

**CRITICAL**: Always run these commands in parallel using multiple Bash tool calls in a single message:

```bash
# Run these three commands simultaneously:
git status              # Check all untracked files and modifications
git diff               # Review staged and unstaged changes
git log --oneline -10  # Check recent commit message style
```

### Step 2: Analyze Changes

Before proceeding, analyze:
- **What changes are being committed** (from git diff output)
- **Why these changes were made** (understand the context)
- **Impact of the changes** (features, fixes, refactoring, etc.)
- **Commit message style** (from git log to match existing patterns)

---

## 📝 Commit Creation Workflow

### Step 3: Stage All Changes

Add all relevant files to staging area:

```bash
git add .
```

### Step 4: Create Commit with Proper Message Format

**ALWAYS use HEREDOC format** for commit messages:

```bash
git commit -m "$(cat <<'EOF'
[type]: [concise description in present tense]

[Optional: More detailed explanation if needed]
- Key change 1
- Key change 2
- Key change 3

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>
EOF
)"
```

#### Commit Message Types:
- `feat`: New features or major functionality
- `fix`: Bug fixes and error corrections  
- `refactor`: Code restructuring without functionality changes
- `docs`: Documentation updates
- `style`: Code formatting, whitespace, etc.
- `test`: Adding or updating tests
- `chore`: Maintenance tasks, dependency updates
- `perf`: Performance improvements
- `ci`: CI/CD configuration changes

### Step 5: Verify Commit Success

**IMMEDIATELY after committing**, verify the commit succeeded:

```bash
git status
```

Expected output should show "nothing to commit, working tree clean" or indicate the branch is ahead of origin.

---

## 🚀 Push Workflow

### Step 6: Handle Pre-commit Hook Changes

If the commit succeeded but `git status` shows modified files (from pre-commit hooks):

1. **Stage the hook changes**:
   ```bash
   git add .
   ```

2. **Amend or create a follow-up commit**:
   ```bash
   git commit -m "$(cat <<'EOF'
   fix: clean up files modified by pre-commit hooks
   
   Address automated changes from linters, formatters, or other
   pre-commit hooks to maintain repository consistency.
   
   🤖 Generated with [Claude Code](https://claude.ai/code)
   
   Co-Authored-By: Claude <<EMAIL>>
   EOF
   )"
   ```

### Step 7: Push to Remote

```bash
git push
```

#### Handle Push Conflicts:

If push is rejected (non-fast-forward):

1. **Pull with rebase**:
   ```bash
   git pull --rebase
   ```

2. **After successful rebase, push again**:
   ```bash
   git push
   ```

---

## 🧪 Quality Checks Before Committing

### Run Quality Checks (When Available)

Before committing, run appropriate quality checks for modified components:

#### Backend Changes:
```bash
cd backend
npm run lint           # ESLint
npm run type-check     # TypeScript checking
npm test              # Run tests
```

#### Web Changes:
```bash
npm run lint           # Next.js ESLint  
npm run type-check     # TypeScript checking
npm run build         # Verify build works
```

#### Mobile Changes:
```bash
cd mobile
npm run lint           # ESLint
npm run type-check     # TypeScript checking
```

#### Database Schema Changes:
```bash
cd backend
npm run db:push        # Push schema changes
npm run db:generate    # Regenerate Prisma client
```

---

## 📋 Complete Workflow Checklist

### ✅ Pre-Commit Checklist
- [ ] Run `git status`, `git diff`, and `git log` commands **in parallel**
- [ ] Analyze all changes and understand their impact
- [ ] Review commit message style from recent commits
- [ ] Run quality checks for modified components (lint, type-check, tests)
- [ ] Verify database schema changes if applicable

### ✅ Commit Checklist  
- [ ] Stage all changes with `git add .`
- [ ] Create commit using HEREDOC format
- [ ] Include proper commit type and concise description
- [ ] Add Claude Code attribution footer
- [ ] **IMMEDIATELY** run `git status` to verify commit success

### ✅ Post-Commit Checklist
- [ ] Handle any pre-commit hook modifications
- [ ] Amend commit or create follow-up commit if needed
- [ ] Push changes to remote repository
- [ ] Handle any push conflicts with rebase if necessary
- [ ] Verify final push success

---

## 🔧 Troubleshooting Common Issues

### NPM Cache Issues
```bash
# Clear npm cache if permission errors occur
npm cache clean --force
# Or try installing with legacy peer deps
npm install --legacy-peer-deps
```

### Merge Conflicts During Rebase
```bash
# Resolve conflicts manually, then:
git add .
git rebase --continue
```

### Accidentally Committed Wrong Files
```bash
# Undo last commit (keep changes)
git reset --soft HEAD~1
# Undo last commit (discard changes)  
git reset --hard HEAD~1
```

### Branch Behind Remote
```bash
# Pull latest changes and rebase
git pull --rebase origin main
```

---

## 📊 Commit Message Examples

### Good Commit Messages:

```bash
feat: implement comprehensive social features and documentation

Major project reorganization and feature additions:
- Add achievements system with sharing capabilities
- Implement discovery feed for exploring public lists
- Create shared lists functionality with real-time updates
- Add comprehensive API documentation and testing scripts
- Restructure backend with proper error handling

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>
```

```bash
fix: resolve TypeScript compilation errors in backend routes

- Fix missing import statements in items.ts and auth.ts
- Add proper type definitions for request parameters
- Resolve Prisma client type mismatches

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>
```

### Bad Commit Messages:
- ❌ "Fixed stuff"
- ❌ "WIP"
- ❌ "Updated files"
- ❌ Missing HEREDOC format
- ❌ Missing Claude Code attribution

---

## 🚨 Critical Reminders

1. **NEVER skip git status verification** after committing
2. **ALWAYS use parallel commands** for git status, diff, and log  
3. **ALWAYS use HEREDOC format** for commit messages
4. **NEVER commit without understanding** what changes are being made
5. **ALWAYS handle pre-commit hook changes** properly
6. **RUN quality checks** before committing when possible

---

**This workflow ensures consistent, high-quality commits that follow best practices and maintain repository integrity.**