# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

DreamVault is a full-stack bucket list application with three main components:
- **Backend API**: Express.js + TypeScript + PostgreSQL + Prisma
- **Web App**: Next.js + TypeScript + Tailwind CSS  
- **Mobile Apps**: React Native + Expo
- **Shared Types**: TypeScript definitions in `/packages/types`

## Architecture

### Monorepo Structure
```
├── backend/           # Express.js API server (port 3001)
├── web/              # Next.js web application (port 3000)
├── mobile/           # React Native + Expo app (current)
└── packages/types/   # Shared TypeScript types
```

### Core Technologies
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: Auth0 integration across all platforms
- **State Management**: Zustand (web/mobile)
- **UI Components**: Radix UI primitives with custom styling
- **Styling**: Tailwind CSS with custom design system (gradients, glassmorphism)
- **Real-time**: Socket.io planned
- **File Storage**: AWS S3 for media
- **AI Features**: OpenAI API integration
- **Monitoring**: Sentry integration

### Database Schema (Prisma)
Key models include:
- `User` - Auth0 integration with profiles and preferences
- `BucketListItem` - Core bucket list functionality with categories, priorities, status
- `Media` - File attachments and images
- `SharedListMember` - List sharing capabilities
- `Achievement` - Gamification system
- `Notification` - Push notifications

## Common Commands

### Git Workflow Commands
```bash
# Pre-commit analysis (run in parallel)
git status              # Check all modifications and untracked files
git diff               # Review changes being committed
git log --oneline -10  # Check recent commit message style

# Quality checks before committing
cd backend && npm run lint && npm run type-check && npm test
npm run lint && npm run type-check && npm run build  # Web checks
cd mobile && npm run lint && npm run type-check      # Mobile checks

# Commit with proper format (use HEREDOC)
git add .
git commit -m "$(cat <<'EOF'
feat: implement new feature

Detailed description of changes made and why.

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>
EOF
)"

# Verify and push
git status             # Verify commit succeeded
git push              # Push to remote
```

**📋 See `GIT_WORKFLOW.md` for complete step-by-step git procedures.**

### Backend Development
```bash
cd backend
npm run dev              # Start development server (port 3001)
npm run build           # Build TypeScript to JavaScript
npm run start           # Start production server
npm test               # Run all tests
npm run test:watch     # Run tests in watch mode
npm run test:coverage  # Run tests with coverage
npm run test:auth      # Test Auth0 connection
npm run lint           # ESLint
npm run type-check     # TypeScript type checking
```

### Backend Database
```bash
cd backend
npm run db:generate    # Generate Prisma client
npm run db:push        # Push schema changes to database
npm run db:migrate     # Run database migrations
npm run db:seed        # Seed database with sample data
npm run db:studio      # Open Prisma Studio
npm run db:reset       # Reset database (destructive)
npm run db:test        # Test database connection
```

### Web Development
```bash
cd web
npm run dev            # Start Next.js development server (port 3000)
npm run build          # Build for production
npm run start          # Start production server
npm run lint           # Next.js ESLint
npm run type-check     # TypeScript type checking
```

### Shared Types Development
```bash
cd packages/types
npm run build          # Build TypeScript definitions
npm run dev            # Build in watch mode
npm run clean          # Clean build artifacts
```

### Mobile Development (React Native + Expo)
```bash
cd mobile              # Current mobile implementation
npm run start          # Start Expo development server
npm run android        # React Native Android
npm run ios            # React Native iOS
npm run test           # Jest tests
npm run lint           # ESLint
npm run type-check     # TypeScript checking
```

**Note**: The `/mobile` directory is the current active implementation. The `/DreamVaultExpo` directory has been removed from the codebase.

## Development Workflow

### Environment Setup
1. Backend requires PostgreSQL database and Auth0 configuration
2. Copy `.env.example` to `.env` in backend directory
3. Web app requires Auth0 configuration for authentication
4. Mobile apps require Auth0 native app configuration
5. Build shared types: `cd packages/types && npm run build`

### API Integration
- Backend API runs on `http://localhost:3001`
- Web app connects to backend API
- Mobile apps connect to backend API (configure base URL in environment)
- All apps use Auth0 for authentication with JWT tokens

### Database Changes
1. Modify `backend/prisma/schema.prisma`
2. Run `npm run db:push` for development
3. Run `npm run db:migrate` for production migrations
4. Update shared types in `packages/types` if needed

### Testing Strategy
- Backend: Jest + Supertest for API integration tests
- Web: Use Next.js testing patterns
- Mobile: Jest for React Native testing
- Always run `npm test` before committing changes

### Git Workflow
- **Follow standardized workflow**: See `GIT_WORKFLOW.md` for complete git commit procedures
- **Quality checks**: Run lint, type-check, and tests before committing
- **Commit format**: Use HEREDOC format with proper commit types (feat, fix, refactor, etc.)
- **Parallel commands**: Always run `git status`, `git diff`, and `git log` in parallel before committing

## Key Features

### Authentication Flow
- Auth0 handles registration/login across all platforms
- Backend validates JWT tokens and manages user profiles
- Shared user context between web and mobile apps

### Bucket List Management
- CRUD operations for bucket list items
- Categories: TRAVEL, ADVENTURE, CAREER, PERSONAL, CREATIVE, FITNESS, EDUCATION, SOCIAL
- Priorities: LOW, MEDIUM, HIGH, URGENT
- Status tracking: NOT_STARTED, IN_PROGRESS, COMPLETED, ON_HOLD, CANCELLED

### Media Handling
- AWS S3 integration for file storage
- Image processing with Sharp library
- React Native image picker integration

### Real-time Features
- Socket.io server setup in backend
- Real-time notifications and updates planned

## Error Handling

### Backend API
- Structured error responses with proper HTTP status codes
- Request validation with comprehensive error messages
- Sentry integration for error monitoring

### Frontend Apps
- Error boundaries in React components
- User-friendly error messages
- Retry mechanisms for network requests

## Deployment Notes

### Backend
- Requires PostgreSQL database connection
- Environment variables for Auth0, AWS S3, OpenAI
- Health check endpoints: `/health`, `/ready`, `/live`
- Metrics endpoint: `/metrics`

### Web App
- Next.js build process with static optimization
- Sentry configuration for error tracking
- Environment variables for Auth0 and API URLs

### Mobile Apps
- Expo managed workflow for easier deployment
- Platform-specific builds for iOS/Android app stores
- Environment configuration for different environments

## UI Design System

### Custom Components
- **Button Component**: Located in `web/src/components/ui/button.tsx` with multiple variants:
  - `default`, `destructive`, `outline`, `secondary`, `ghost`, `link`
  - Enhanced variants: `gradient`, `gradient-secondary`, `premium`, `glow`, `glass`, `neon`
  - Uses Radix UI Slot for polymorphic behavior with `asChild` prop

### Styling Architecture
- **Tailwind Configuration**: Extended with custom colors, animations, and design tokens
- **Global Styles**: `web/src/app/globals.css` contains custom CSS classes:
  - `.gradient-text` - Gradient text effects
  - `.bg-gradient-primary/secondary/hero` - Custom gradient backgrounds
  - `.shadow-glow/glow-lg` - Custom glow effects
  - `.glass` variants - Glassmorphism effects with backdrop-blur
- **Animation Classes**: Custom animations with Tailwind integration
- **Responsive Design**: Mobile-optimized with reduced animations on smaller screens

### State Management Architecture
- **Zustand Stores**: Located in `web/src/lib/store.ts`
- **Client API**: Centralized API client in `web/src/lib/api.ts`
- **Error Handling**: Global error boundary and notification system

## Common Development Patterns

### Adding New UI Components
1. Create component in `web/src/components/ui/` using Radix UI primitives
2. Use `cn()` utility from `@/lib/utils` for class composition
3. Follow existing patterns for variants and sizes
4. Add to Tailwind safelist if using custom classes

### API Integration
- Backend routes follow REST conventions in `backend/src/routes/`
- Use Prisma for database operations with proper error handling
- Frontend API calls use centralized client in `web/src/lib/api.ts`
- Auth0 JWT tokens handled automatically in API middleware

### Database Schema Updates
1. Modify `backend/prisma/schema.prisma`
2. Generate migration: `npm run db:migrate`
3. Update shared types in `packages/types/`
4. Regenerate Prisma client: `npm run db:generate`

## Troubleshooting

### Common Issues
- **Styling not applying**: Check Tailwind safelist in `web/tailwind.config.ts`
- **Mobile Metro version conflicts**: Ensure consistent Metro versions in mobile dependencies
- **Empty asset files**: Mobile apps may have 0-byte asset files that need replacement
- **Type errors**: Ensure shared types in `packages/types/` are up to date